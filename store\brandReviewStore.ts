import type { BrandReview, BrandReviewSummary, BrandReviewSummaryResponse, DailyRandomBrand } from "@/api/services/brandReviewService";
import brandReviewService, { BrandReviewSummaryDetail } from "@/api/services/brandReviewService";
import userService from '@/api/services/userService';
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useState } from "react";
import { create } from "zustand";
import { useUserInfo } from "./userStore";
type BrandReviewStore = {
	summaryItems: BrandReviewSummary[];
	actions: {
		setSummaryItems: (
			summaryItems: BrandReviewSummary[],
			pagination?: { totalCount: number; totalPages: number; currentPage: number },
		) => void;
		clearSummaryItems: () => void;
	};
};

const useBrandReviewStore = create<BrandReviewStore>()((set) => ({
	summaryItems: [],
	actions: {
		setSummaryItems: (summaryItems) => {
			set({
				summaryItems: summaryItems,
			});
		},
		clearSummaryItems: () => {
			set({
				summaryItems: [],
			});
		},
	},
}));


export function useBrandReviewSummaryAdmin() {
	const [currentPage, setCurrentPage] = useState(1);
	const [sortBy, setSortBy] = useState<string>("review_count");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

	const queryClient = useQueryClient();

	const query = useQuery<BrandReviewSummaryResponse>({
		queryKey: ["brand-reviews", "summaries", currentPage, sortBy, sortOrder],
		queryFn: async () => {
			try {
				const response = await brandReviewService.getBrandReviewSummaries(currentPage, 10, sortBy, sortOrder);
				if (typeof response !== "object" || !response) {
					throw new Error("Invalid response format");
				}

				return response;
			} catch (error) {
				console.error("Brand Review summary fetch error:", error);
				throw error;
			}
		},
		placeholderData: (previousData) => previousData,
		staleTime: 10 * 100,
	});

	// Get reviews for a specific brand
	const useBrandReviews = (brandId: string, page = 1, limit = 10) => {
		return useQuery({
			queryKey: ["brand-reviews", "brand", brandId, page, limit],
			queryFn: () => brandReviewService.getReviewsByBrandId(brandId, page, limit),
			enabled: !!brandId,
		});
	};

	// Handle sort changes
	const handleSortChange = (newSortBy: string, newSortOrder: "asc" | "desc") => {
		setSortBy(newSortBy);
		setSortOrder(newSortOrder);
	};

	// Update review status
	const updateReviewStatusMutation = useMutation({
		mutationFn: ({ reviewId, status }: { reviewId: string; status: boolean }) =>
			brandReviewService.updateReviewStatus(reviewId, status),
		onSuccess: () => {
			// Invalidate review queries to refetch data
			queryClient.invalidateQueries({ queryKey: ["brand-reviews"] });
		},
	});

	// Update review content
	const updateReviewContentMutation = useMutation({
		mutationFn: ({ reviewId, data }: { reviewId: string; data: Partial<BrandReview> }) =>
			brandReviewService.updateReviewContent(reviewId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["brand-reviews"] });
		},
	});

	// Delete review
	const deleteReviewMutation = useMutation({
		mutationFn: (reviewId: string) => brandReviewService.deleteReview(reviewId),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["brand-reviews"] });
		},
	});

	// Create review
	const createReviewMutation = useMutation({
		mutationFn: (data: Partial<BrandReview>) => brandReviewService.createReview(data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["brand-reviews"] });
		},
	});

	return {
		data: query.data?.brands,
		pagination: query.data
			? {
					totalCount: Number(query.data.totalCount) || 0,
					totalPages: Number(query.data.totalPages) || 1,
					currentPage: Number(query.data.currentPage) || 1,
					sortBy: query.data.sortBy,
					sortOrder: query.data.sortOrder,
				}
			: undefined,
		setPage: setCurrentPage,
		useBrandReviews,
		handleSortChange,
		sortBy,
		sortOrder,
		isLoading: query.isLoading || query.isFetching,
		updateReviewStatus: updateReviewStatusMutation.mutate,
		updateReviewContent: updateReviewContentMutation.mutate,
		deleteReview: deleteReviewMutation.mutate,
		createReview: createReviewMutation.mutate,
		isUpdatingStatus: updateReviewStatusMutation.isPending,
		isUpdatingContent: updateReviewContentMutation.isPending,
		isDeleting: deleteReviewMutation.isPending,
		isCreating: createReviewMutation.isPending,
	};
}


export function useDailyRandomBrands(count = 9) {
	return useQuery<DailyRandomBrand[]>({
		queryKey: ["brand-reviews", "daily-random-brands", count],
		queryFn: () => brandReviewService.getDailyRandomBrands(count),
		staleTime: 24 * 60 * 60 * 1000, 
	});
}


interface UseBrandReviewDetailReturn {
  review: BrandReview | null;
  replies: BrandReview[];
  isLoading: boolean;
  error: string | null;
  submitReply: (content: string) => Promise<void>;
  refreshReview: () => Promise<void>;
}
/**
 * 
 * @param reviewId 评论ID
 * 这个方法主要用来获取单个评论下面的所有回复
 * @returns 
 */
export const useBrandReviewDetail = (reviewId: string): UseBrandReviewDetailReturn => {
  const userInfo = useUserInfo();
  const queryClient = useQueryClient();
  const [review, setReview] = useState<BrandReview | null>(null);
  const [replies, setReplies] = useState<BrandReview[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReviewDetail = useCallback(async () => {
    if (!reviewId) {
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      const reviewData = await brandReviewService.getReviewById(reviewId);
    
      if (reviewData) {
        setReview(reviewData);

        const replyId = reviewData.review_id
        
        try {
          const repliesData = await brandReviewService.getReviewReplies(replyId, 1, 100);

          // Since the structure is fixed, directly access the replies array
          if (repliesData && Array.isArray(repliesData.replies)) {
            setReplies(repliesData.replies);
          } else {
            setReplies([]);
          }
        } catch (repliesError) {
          setReplies([]);
        }
      } else {
        throw new Error('Brand review not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load brand review details');
      setReview(null);
    } finally {
      setIsLoading(false);
    }
  }, [reviewId]);

  const submitReply = async (content: string): Promise<void> => {
    if (!userInfo.id) {
      throw new Error('用户未登录，无法回复');
    }

    try {
      
      const replyData = {
        content,
        parent_review_id: reviewId,
        user_id: userInfo.id,
        brand_id: review?.brand_id,
        rating: 1,
        is_long_review: false
      };

      const newReply = await brandReviewService.createReview(replyData);
      
      if (newReply) {
        // 获取当前用户的基本信息（现在总是会返回数据）
        const currentUserInfo = await userService.getUserBasicInfo(userInfo.id);
        
        const correctedReply = {
          ...newReply,
          user_id: userInfo.id,
          user: currentUserInfo || {
            id: userInfo.id,
            username: userInfo.username || '匿名用户',
            avatar: userInfo.avatar
          }
        };
        
        setReplies(prev => [...prev, correctedReply]);

        // 更新主评论的回复计数
        setReview(prev => prev ? {
          ...prev,
          reply_count: (prev.reply_count || 0) + 1
        } : null);

        // 刷新品牌评论列表查询，这样品牌详情页面的回复计数会更新
        if (review?.brand_id) {
          queryClient.invalidateQueries({ 
            queryKey: ['brand-reviews', review.brand_id] 
          });
        }

        // 也刷新所有相关的品牌评论查询
        queryClient.invalidateQueries({ 
          queryKey: ['brand-reviews'] 
        });
      }
    } catch (err) {
      console.error('Error submitting brand reply:', err);
      throw err;
    }
  };

  const refreshReview = async (): Promise<void> => {
    await fetchReviewDetail();
  };

  useEffect(() => {
    if (reviewId) {
      fetchReviewDetail();
    }
  }, [reviewId, fetchReviewDetail]);

  return {
    review,
    replies,
    isLoading,
    error,
    submitReply,
    refreshReview,
  };
};


export type BrandReviewsResponse = {
	reviews: BrandReview[];
	replies?: BrandReview[]; // Add optional replies property
	totalCount: number;
	totalPages: number;
	currentPage: number;
	data?: {
		replies?: BrandReview[];
	};
};

// Remove duplicate interface definition - use the one from the service


/**
 * 
 * @param brandId 品牌ID
 * @param limit 条数
 * @param type 类型
 * @returns 
 * 获取品牌评论列表，支持分页和类型筛选
 */
export const useBrandReviews = (
	brandId: string,
	limit: number = 5,
	type: 'all' | 'long' | 'short' = 'all',
	sortBy: 'latest' | 'hot' = 'latest'
) => {
	const [allReviews, setAllReviews] = useState<BrandReview[]>([]);
	const [currentPage, setCurrentPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [isRefreshing, setIsRefreshing] = useState(false);

	const query = useQuery({
		queryKey: ['brand-reviews', brandId, currentPage, limit, type, sortBy],
		queryFn: async () => {
			try {	
				let result;
				
				switch (type) {
					case 'long':
						result = await brandReviewService.getLongReviews(brandId, currentPage, limit, true, 2, sortBy);
						break;
					case 'short':
						result = await brandReviewService.getShortReviews(brandId, currentPage, limit, true, 2, sortBy);
						break;
					default:
						result = await brandReviewService.getReviewsByBrandId(brandId, currentPage, limit, sortBy);
				}		
				
				
				if (result && result.reviews) {
					return {
						reviews: result.reviews,
						pagination: {
							totalCount: result.totalCount || 0,
							totalPages: result.totalPages || 1,
							currentPage: result.currentPage || currentPage,
						}
					};
				}
				
				return {
					reviews: [],
					pagination: {
						totalCount: 0,
						totalPages: 1,
						currentPage: 1,
					}
				};
			} catch (error) {
				console.error('错误的获取了评论信息:', error);
				throw error;
			}
		},
		enabled: !!brandId,
		staleTime: 1 * 60 * 1000, 
		retry: (failureCount, error) => {
			if (error instanceof Error && error.message.includes('404')) {
				return false;
			}
			return failureCount < 2;
		},
	});

	// 重置type、sortBy或brandId时重新开始
	useEffect(() => {
		setCurrentPage(1);
		setAllReviews([]);
		setHasMore(true);
	}, [type, sortBy, brandId]);

	// 当获取到新数据时更新累积的评论列表
	useEffect(() => {
		if (query.data?.reviews) {
			if (currentPage === 1) {
				// 如果是第一页或者刷新，替换所有数据
				setAllReviews(query.data.reviews);
			} else {
				// 如果是加载更多，追加数据，避免重复
				setAllReviews(prev => {
					const existingIds = new Set(prev.map(review => 
						review.review_id || review._id
					));
					const newReviews = query.data.reviews.filter(newReview => 
						!existingIds.has(newReview.review_id || newReview._id)
					);
					return [...prev, ...newReviews];
				});
			}
			
			// 检查是否还有更多数据
			const totalPages = query.data.pagination?.totalPages || 1;
			setHasMore(currentPage < totalPages);
		} else if (query.data && currentPage === 1) {
			// 如果是第一页且没有数据，清空列表
			setAllReviews([]);
			setHasMore(false);
		}
	}, [query.data, currentPage]);

	const refreshReviews = async () => {
		setIsRefreshing(true);
		setCurrentPage(1);
		setAllReviews([]);
		setHasMore(true);
		await query.refetch();
		setIsRefreshing(false);
	};

	const loadMoreReviews = () => {
		if (hasMore && !query.isFetching && !query.isLoading) {
			setCurrentPage(prev => prev + 1);
		}
	};

	return {
		reviews: allReviews,
		pagination: query.data?.pagination,
		isLoading: query.isLoading && currentPage === 1,
		isLoadingMore: query.isFetching && currentPage > 1,
		isRefreshing,
		error: query.error,
		hasMore,
		refreshReviews,
		loadMoreReviews,
	};
};

/**
 * 
 * @param brandId 品牌ID
 * 获取品牌评论汇总信息
 * @returns 
 */
export const useBrandReviewSummary = (brandId?: string) => {
  const { data, isLoading, error } = useQuery<BrandReviewSummaryDetail>({
	queryKey: ['brand-review-summary', brandId],
	queryFn: async () => {
	  if (!brandId) {
		throw new Error("Brand ID is required");
	  }
	  return brandReviewService.getBrandReviewSummary(brandId);
	},
	enabled: !!brandId,
	staleTime: 5 * 60 * 1000, 
  });

  return {
	summary: data,
	isLoading,
	error,
  };
};


/**
 * Hook for creating brand reviews with automatic refresh
 */
export const useCreateBrandReview = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<BrandReview>) => brandReviewService.createReview(data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch related queries
      if (variables.brand_id) {
        // Invalidate brand-specific review queries
        queryClient.invalidateQueries({ 
          queryKey: ['brand-reviews', variables.brand_id] 
        });
        
        // Invalidate brand review summary
        queryClient.invalidateQueries({ 
          queryKey: ['brand-review-summary', variables.brand_id] 
        });
      }
      
      // Invalidate general brand review queries
      queryClient.invalidateQueries({ queryKey: ['brand-reviews'] });
    },
    onError: (error) => {
      console.error('Create brand review error:', error);
    },
  });
};

export default useBrandReviewStore;




import React from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface ProfileFormFieldProps {
  label: string;
  value: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  multiline?: boolean;
  maxLength?: number;
  editable?: boolean;
  onPress?: () => void;
  iconName?: string;
  returnKeyType?: 'done' | 'next' | 'search' | 'send' | 'go' | 'default';
}

export const ProfileFormField: React.FC<ProfileFormFieldProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  multiline = false,
  maxLength,
  editable = true,
  onPress,
  iconName,
  returnKeyType = 'done',
}) => {
  const isClickable = !editable && onPress;

  const renderInput = () => {
    if (isClickable) {
      return (
        <TouchableOpacity style={styles.clickableInput} onPress={onPress}>
          <Text style={[styles.inputText, !value && styles.placeholderText]}>
            {value || placeholder}
          </Text>
          {iconName && <Icon name={iconName} size={16} color="#666" />}
        </TouchableOpacity>
      );
    }

    return (
      <TextInput
        style={[styles.input, multiline && styles.multilineInput]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor="#999"
        multiline={multiline}
        maxLength={maxLength}
        returnKeyType={returnKeyType}
        editable={editable}
      />
    );
  };

  return (
    <View style={styles.field}>
      <Text style={styles.label}>{label}</Text>
      {renderInput()}
      {maxLength && multiline && (
        <Text style={styles.charCount}>
          {value.length}/{maxLength}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  field: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#333',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  clickableInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  placeholderText: {
    color: '#999',
  },
  charCount: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
    marginTop: 4,
  },
});

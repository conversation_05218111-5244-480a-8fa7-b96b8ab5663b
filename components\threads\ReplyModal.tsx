import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';

import type { CreateThreadRequest, Thread } from '@/api/services/threadsService';

interface ReplyModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: CreateThreadRequest) => Promise<void>;
  replyingTo: Thread | null;
  isLoading?: boolean;
}

export default function ReplyModal({
  visible,
  onClose,
  onSubmit,
  replyingTo,
  isLoading = false,
}: ReplyModalProps) {
  const [content, setContent] = useState('');
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (visible) {
      // 延迟聚焦输入框，确保模态框完全显示后再聚焦
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } else {
      // 清空内容
      setContent('');
    }
  }, [visible]);

  const handleClose = () => {
    setContent('');
    onClose();
  };

  const handleSubmit = async () => {
    if (!content.trim() || !replyingTo) {
      return;
    }

    try {
      const replyData: CreateThreadRequest = {
        content: content.trim(),
        parent_thread_id: replyingTo.thread_id,
        reply_to_user_id: replyingTo.user_id,
      };

      await onSubmit(replyData);
      handleClose();
    } catch {
      // 错误处理在父组件进行
    }
  };

  const handleBackdropPress = () => {
    if (!isLoading) {
      handleClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <View style={styles.backdrop} />
        </TouchableWithoutFeedback>

        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {/* 头部 */}
            <View style={styles.header}>
              <TouchableOpacity
                onPress={handleClose}
                disabled={isLoading}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>
                回复 @{replyingTo?.user.username}
              </Text>
              <TouchableOpacity
                onPress={handleSubmit}
                disabled={!content.trim() || isLoading}
                style={[
                  styles.submitButton,
                  { opacity: content.trim() && !isLoading ? 1 : 0.5 },
                ]}
              >
                {isLoading ? (
                  <ActivityIndicator size={16} color="#007AFF" />
                ) : (
                  <Text style={styles.submitText}>发送</Text>
                )}
              </TouchableOpacity>
            </View>

            {/* 原帖信息 */}
            {replyingTo && (
              <View style={styles.originalPost}>
                <View style={styles.originalHeader}>
                  <Text style={styles.originalUsername}>
                    {replyingTo.user.username}
                  </Text>
                  <Text style={styles.originalTime}>
                    {new Date(replyingTo.created_at).toLocaleDateString()}
                  </Text>
                  <View style={styles.spacer} />
                  <View style={styles.likeContainer}>
                    <Ionicons
                      name="heart-outline"
                      size={16}
                      color="#666"
                    />
                    <Text style={styles.likeCount}>
                      {replyingTo.like_count}
                    </Text>
                  </View>
                </View>
                <Text style={styles.originalContent} numberOfLines={3}>
                  {replyingTo.content}
                </Text>
              </View>
            )}

            {/* 输入区域 */}
            <View style={styles.inputContainer}>
              <TextInput
                ref={inputRef}
                style={styles.textInput}
                value={content}
                onChangeText={setContent}
                placeholder="写下你的回复..."
                placeholderTextColor="#6C757D"
                multiline
                maxLength={500}
                textAlignVertical="top"
                autoFocus={false}
              />
              <View style={styles.inputFooter}>
                <Text style={styles.charCount}>
                  {content.length}/500
                </Text>
              </View>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  closeButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
  },
  submitButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: '#F8F9FA',
  },
  submitText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  originalPost: {
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  originalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  originalUsername: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginRight: 8,
  },
  originalTime: {
    fontSize: 12,
    color: '#6C757D',
  },
  spacer: {
    flex: 1,
  },
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  likeCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    fontWeight: '500',
  },
  originalContent: {
    fontSize: 14,
    color: '#495057',
    lineHeight: 20,
  },
  inputContainer: {
    padding: 16,
  },
  textInput: {
    fontSize: 16,
    color: '#212529',
    lineHeight: 22,
    minHeight: 100,
    maxHeight: 150,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F8F9FA',
  },
  inputFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  charCount: {
    fontSize: 12,
    color: '#6C757D',
  },
});

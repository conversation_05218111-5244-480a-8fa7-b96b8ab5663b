import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ImageProps,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  uri: string;
  fallbackIcon?: keyof typeof Ionicons.glyphMap;
  showLoading?: boolean;
  containerStyle?: ViewStyle;
  loadingSize?: 'small' | 'large';
  loadingColor?: string;
  fallbackIconSize?: number;
  fallbackIconColor?: string;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: any) => void;
}

export default function OptimizedImage({
  uri,
  fallbackIcon = 'image-outline',
  showLoading = true,
  containerStyle,
  loadingSize = 'small',
  loadingColor = '#999',
  fallbackIconSize = 24,
  fallbackIconColor = '#ccc',
  onLoadStart,
  onLoadEnd,
  onError,
  style,
  ...imageProps
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
    onLoadStart?.();
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
    onLoadEnd?.();
  };

  const handleError = (error: any) => {
    setIsLoading(false);
    setHasError(true);
    onError?.(error);
    console.warn('图片加载失败:', uri, error);
  };

  // 如果没有URI或者URI无效，直接显示fallback
  if (!uri || uri.trim() === '') {
    return (
      <View style={[styles.container, containerStyle, style]}>
        <View style={styles.fallbackContainer}>
          <Ionicons 
            name={fallbackIcon} 
            size={fallbackIconSize} 
            color={fallbackIconColor} 
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, containerStyle]}>
      {!hasError ? (
        <>
          <Image
            {...imageProps}
            source={{ uri }}
            style={[style, hasError && styles.hidden]}
            onLoadStart={handleLoadStart}
            onLoadEnd={handleLoadEnd}
            onError={handleError}
          />
          
          {isLoading && showLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator 
                size={loadingSize} 
                color={loadingColor} 
              />
            </View>
          )}
        </>
      ) : (
        <View style={[styles.fallbackContainer, style]}>
          <Ionicons 
            name={fallbackIcon} 
            size={fallbackIconSize} 
            color={fallbackIconColor} 
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(248, 249, 250, 0.8)',
  },
  fallbackContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    flex: 1,
  },
  hidden: {
    opacity: 0,
  },
});

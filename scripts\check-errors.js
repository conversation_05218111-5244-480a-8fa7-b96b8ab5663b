#!/usr/bin/env node

/**
 * 快速错误检查脚本
 * 用于在打包前检查项目中的各种错误
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 开始检查项目错误...\n');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n📋 ${description}`, 'blue');
  log(`执行命令: ${command}`, 'yellow');
  
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    if (output.trim()) {
      log('✅ 通过', 'green');
      return { success: true, output };
    } else {
      log('✅ 通过 (无输出)', 'green');
      return { success: true, output: '' };
    }
  } catch (error) {
    log('❌ 发现错误:', 'red');
    log(error.stdout || error.message, 'red');
    return { success: false, error: error.stdout || error.message };
  }
}

function checkFileExists(filePath, description) {
  log(`\n📁 ${description}`, 'blue');
  
  if (fs.existsSync(filePath)) {
    log(`✅ 文件存在: ${filePath}`, 'green');
    return true;
  } else {
    log(`❌ 文件不存在: ${filePath}`, 'red');
    return false;
  }
}

function checkPackageJson() {
  log('\n📦 检查 package.json 配置', 'blue');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // 检查必要的字段
    const requiredFields = ['name', 'version', 'main'];
    const missingFields = requiredFields.filter(field => !packageJson[field]);
    
    if (missingFields.length > 0) {
      log(`❌ 缺少必要字段: ${missingFields.join(', ')}`, 'red');
      return false;
    }
    
    // 检查 Expo 相关配置
    if (!packageJson.main || !packageJson.main.includes('expo-router')) {
      log('⚠️  main 字段可能不正确，应该是 "expo-router/entry"', 'yellow');
    }
    
    log('✅ package.json 配置正常', 'green');
    return true;
  } catch (error) {
    log(`❌ 读取 package.json 失败: ${error.message}`, 'red');
    return false;
  }
}

function checkAppConfig() {
  const configFiles = ['app.json', 'app.config.js', 'app.config.ts'];
  const existingConfig = configFiles.find(file => fs.existsSync(file));
  
  if (!existingConfig) {
    log('❌ 未找到 Expo 配置文件 (app.json, app.config.js, app.config.ts)', 'red');
    return false;
  }
  
  log(`✅ 找到配置文件: ${existingConfig}`, 'green');
  return true;
}

async function main() {
  const results = [];
  
  // 1. 检查 TypeScript 编译
  results.push(runCommand('npx tsc --noEmit', 'TypeScript 类型检查'));
  
  // 2. 检查 ESLint
  results.push(runCommand('npm run lint', 'ESLint 代码检查'));
  
  // 3. 检查重要文件是否存在
  results.push({
    success: checkFileExists('package.json', '检查 package.json'),
    description: '检查 package.json'
  });
  
  results.push({
    success: checkPackageJson(),
    description: '检查 package.json 配置'
  });
  
  results.push({
    success: checkAppConfig(),
    description: '检查 Expo 配置文件'
  });
  
  results.push({
    success: checkFileExists('app/_layout.tsx', '检查根布局文件'),
    description: '检查根布局文件'
  });
  
  // 4. 检查依赖安装
  results.push(runCommand('npm ls --depth=0', '检查依赖安装'));
  
  // 5. 尝试 Expo 预检查
  try {
    results.push(runCommand('npx expo-doctor', 'Expo 环境检查'));
  } catch (error) {
    log('⚠️  Expo doctor 检查跳过 (可能未安装 Expo CLI)', 'yellow');
  }
  
  // 总结
  log('\n' + '='.repeat(50), 'bold');
  log('📊 检查结果总结', 'bold');
  log('='.repeat(50), 'bold');
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  if (successCount === totalCount) {
    log(`\n🎉 所有检查通过! (${successCount}/${totalCount})`, 'green');
    log('✅ 项目可以进行打包', 'green');
  } else {
    log(`\n⚠️  发现问题: ${totalCount - successCount} 个检查失败 (${successCount}/${totalCount} 通过)`, 'yellow');
    log('❌ 建议修复错误后再进行打包', 'red');
  }
  
  log('\n💡 提示:', 'blue');
  log('- 修复 TypeScript 错误是最重要的', 'blue');
  log('- ESLint 警告可以暂时忽略，但错误需要修复', 'blue');
  log('- 确保所有依赖都已正确安装', 'blue');
  
  process.exit(successCount === totalCount ? 0 : 1);
}

main().catch(error => {
  log(`\n💥 脚本执行失败: ${error.message}`, 'red');
  process.exit(1);
});

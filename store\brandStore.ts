import brandReviewService, { TopBrandProduct } from "@/api/services/brandReviewService";
import type { BrandRes } from "@/api/services/brandService";
import brandService from "@/api/services/brandService";
import type { Brand } from "@/types/entity";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { create } from "zustand";

type BrandStore = {
	brandList: Brand[];
	actions: {
		setBrandList: (
			brands: Brand[],
			pagination?: { totalCount: number; totalPages: number; currentPage: number },
		) => void;
		clearBrandList: () => void;
	};
};

const useBrandStore = create<BrandStore>()((set) => ({
	brandList: [],
	actions: {
		setBrandList: (brands) => {
			set({
				brandList: brands,
			});
		},
		clearBrandList: () => {
			set({
				brandList: [],
			});
		},
	},
}));

export const useBrandList = () => useBrandStore((state) => state.brandList);
export const useBrandAaction = () => useBrandStore((state) => state.actions);

/**
 *  获取一个带有翻页和搜索功能的品牌列表
 */
export const useBrands = () => {
	const [currentPage, setCurrentPage] = useState(1);
	const [searchName, setSearchName] = useState("");

	const handleSearch = (name: string) => {
		setSearchName(name);
		setCurrentPage(1);
	};

	const query = useQuery<BrandRes>({
		queryKey: ["brands", currentPage, searchName],
		queryFn: async () => {
			try {
				const response = await brandService.getBrands(currentPage, searchName || undefined);
				if (typeof response !== "object" || !response) {
					throw new Error("Invalid response format");
				}

				return response;
			} catch (error) {
				console.error("Brand fetch error:", error);
				throw error;
			}
		},
		placeholderData: (previousData) => previousData,
		staleTime: 10 * 100,
	});

	const data = query.data as BrandRes | undefined;

	return {
		data: data?.brands || [],
		pagination: data
			? {
				totalCount: Number(data.totalCount) || 0,
				totalPages: Number(data.totalPages) || 1,
				currentPage: Number(data.currentPage) || 1,
			}
			: undefined,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		setPage: setCurrentPage,
		searchName,
		setSearchName: handleSearch,
		clearSearch: () => handleSearch(""),
	};
};
/**
 *  一个不带翻页的品牌列表
 */
export const useAllBrandsForSelect = () => {
	const query = useQuery<BrandRes>({
		queryKey: ["brands-list"],
		queryFn: async () => {
			try {
				const response = await brandService.getAllBrandsNoPage();

				if (typeof response !== "object" || !response) {
					throw new Error("Invalid response format");
				}

				return response;
			} catch (error) {
				console.error("Brands list fetch error:", error);
				throw error;
			}
		},
		staleTime: 5 * 60 * 1000,
	});

	const data = query.data as BrandRes | undefined;

	return {
		data: data?.brands || [],
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
	};
};

/**
 * 
 * @param brandId 品牌ID
 * 获取品牌详情
 * @returns 
 */
export const useBrandDetail = (brandId?: string) => {
	const { data, isLoading, error } = useQuery<Brand>({
		queryKey: ["brand", brandId],
		queryFn: async () => {
			if (!brandId) {
				throw new Error("Brand ID is required");
			}

			try {
				const response = await brandService.getBrandById(brandId);

				if (response) {
					return response;
				} else {
					console.error("Unexpected response structure:", response);
					throw new Error("Invalid brand dataformat");
				}
			} catch (err) {
				console.error("Error fetching brand details:", err);
				throw err;
			}
		},
		enabled: !!brandId,
		staleTime: 5 * 60 * 1000,
		retry: 1,
	});

	return {
		brand: data,
		isLoading,
		error,
	};
};



/**
 * 
 * @param brandId 品牌ID
 * 获取品牌下的热门产品
 * @param limit 
 * @returns 
 */
export const useTopBrandProducts = (brandId?: string, limit = 5) => {
	const { data, isLoading, error } = useQuery<TopBrandProduct[]>({
		queryKey: ['brand-top-products', brandId, limit],
		queryFn: async () => {
			if (!brandId) {
				return [];
			}
			return brandReviewService.getTopProductsForBrand(brandId, limit);
		},
		enabled: !!brandId,
		staleTime: 5 * 60 * 1000,
	});

	return {
		products: data || [],
		isLoading,
		error,
	};
};



export default useBrandStore;


import { useRouter } from 'expo-router';
import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Alert } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Image } from 'expo-image';

import { useUserBasicInfo } from '@/store/userInfoStore';
import productReviewService, { Review } from '@/api/services/productReviewService';
import brandReviewService, { BrandReview } from '@/api/services/brandReviewService';
import { buildImageUrl, getUserAvatarUrlSync } from '@/utils/imageUtils';
import { useUserInfo } from '@/store/userStore';

// 联合类型支持产品评论和品牌评论
type ReviewType = Review | BrandReview;

// 类型守卫函数
const isBrandReview = (review: ReviewType): review is BrandReview => {
  return 'brand_id' in review;
};

interface ReviewItemProps {
  review: ReviewType;
  entityType: 'product' | 'brand';
  isReply?: boolean;
  compact?: boolean;
  showRepliesButton?: boolean;
  showRating?: boolean;
}

const ReviewItem: React.FC<ReviewItemProps> = ({
  review,
  entityType,
  isReply = false,
  compact = false,
  showRepliesButton = true,
  showRating = true
}) => {
  const router = useRouter();
  const userInfo = useUserInfo();
  // 状态管理
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [likesCount, setLikesCount] = useState(review.liked_by.length || 0);
  const [dislikesCount, setDislikesCount] = useState(review.disliked_by.length || 0);
  const [isLoading, setIsLoading] = useState(false);

  // 调试信息
  if (!isReply) {
    console.log('Reply data:', {
      review_id: review.review_id,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by,
      likesCount,
      dislikesCount
    });
  }
  // 使用 reply_count 字段，如果不存在则使用 replies 数组长度
  const repliesCount = review.reply_count ?? (review.replies ? review.replies.length : 0);
  const reviewDate = new Date(review.review_time).toLocaleDateString();

  // 同步review数据变化到本地状态
  useEffect(() => {
    setLikesCount(review.liked_by.length || 0);
    setDislikesCount(review.disliked_by.length || 0);
  }, [review.liked_by.length, review.disliked_by.length]);

  // 初始化用户反应状态
  useEffect(() => {
    if (userInfo?.id && review.review_id) {
      // 检查用户是否已经点赞或反对
      const isUserLiked = review.liked_by?.includes(userInfo.id) || false;
      const isUserDisliked = review.disliked_by?.includes(userInfo.id) || false;

      setIsLiked(isUserLiked);
      setIsDisliked(isUserDisliked);
    }
  }, [userInfo?.id, review.review_id, review.liked_by, review.disliked_by]);

  // 使用优化后的用户信息hook
  const userId = review.user_id;
  const hasNestedUser = isBrandReview(review) && review.user;
  const { data: userBasicInfo, isLoading: isLoadingUser } = useUserBasicInfo(
    // 只有在没有嵌套用户信息时才请求
    !hasNestedUser && userId ? userId : null
  );

  const getUserDisplayInfo = () => {
    // 优先使用嵌套的用户信息（只有BrandReview有user字段）
    if (isBrandReview(review) && review.user) {
      return {
        name: review.user.username,
        avatar: review.user.id ? getUserAvatarUrlSync(review.user.id) : null
      };
    }

    // 其次使用已获取的用户信息
    if (userBasicInfo) {
      return {
        name: userBasicInfo.username,
        avatar: userId ? getUserAvatarUrlSync(userId) : null
      };
    }

    // fallback到用户ID
    if (!userId) {
      return { name: '匿名用户', avatar: null };
    }

    // 如果正在加载，显示加载状态
    if (isLoadingUser) {
      return { name: '加载中...', avatar: getUserAvatarUrlSync(userId) };
    }

    // 如果是完整的用户ID，截取前8位作为显示名称
    const displayName = userId.length > 8 ? userId.substring(0, 8) : userId;
    return { name: displayName, avatar: getUserAvatarUrlSync(userId) };
  };

  const displayUserInfo = getUserDisplayInfo();

  // 点赞处理
  const handleLike = useCallback(async () => {
    if (!userInfo?.id) {
      Alert.alert('提示', '请先登录');
      return;
    }

    if (isLoading) return;

    try {
      setIsLoading(true);
      const reviewId = review.review_id || review._id;

      // 乐观更新UI
      const wasLiked = isLiked;
      const wasDisliked = isDisliked;

      if (wasLiked) {
        // 取消点赞
        setIsLiked(false);
        setLikesCount(prev => prev - 1);
      } else {
        // 点赞
        setIsLiked(true);
        setLikesCount(prev => prev + 1);

        // 如果之前是反对，取消反对
        if (wasDisliked) {
          setIsDisliked(false);
          setDislikesCount(prev => prev - 1);
        }
      }

      // 调用API
      const service = entityType === 'product' ? productReviewService : brandReviewService;

      let response;
      if (wasLiked) {
        response = await service.unlikeReview(reviewId);
      } else {
        response = await service.likeReview(reviewId);
      }

      // 使用服务器返回的真实数据更新状态
      if (response) {
        setLikesCount(response.likes || 0);
        setDislikesCount(response.dislikes || 0);
        setIsLiked(response.liked_by?.includes(userInfo.id) || false);
        setIsDisliked(response.disliked_by?.includes(userInfo.id) || false);
      }

    } catch (error: any) {
      // 回滚UI状态
      setIsLiked(review.liked_by?.includes(userInfo.id) || false);
      setIsDisliked(review.disliked_by?.includes(userInfo.id) || false);
      setLikesCount(review.liked_by.length || 0);
      setDislikesCount(review.disliked_by.length || 0);

      Alert.alert('错误', error.message || '操作失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [userInfo?.id, isLiked, isDisliked, isLoading, review, entityType]);

  // 反对处理
  const handleDislike = useCallback(async () => {
    if (!userInfo?.id) {
      Alert.alert('提示', '请先登录');
      return;
    }

    if (isLoading) return;

    try {
      setIsLoading(true);
      const reviewId = review.review_id || review._id;

      // 乐观更新UI
      const wasLiked = isLiked;
      const wasDisliked = isDisliked;

      if (wasDisliked) {
        // 取消反对
        setIsDisliked(false);
        setDislikesCount(prev => prev - 1);
      } else {
        // 反对
        setIsDisliked(true);
        setDislikesCount(prev => prev + 1);

        // 如果之前是点赞，取消点赞
        if (wasLiked) {
          setIsLiked(false);
          setLikesCount(prev => prev - 1);
        }
      }

      // 调用API
      const service = entityType === 'product' ? productReviewService : brandReviewService;

      let response;
      if (wasDisliked) {
        response = await service.undislikeReview(reviewId);
      } else {
        response = await service.dislikeReview(reviewId);
      }

      // 使用服务器返回的真实数据更新状态
      if (response) {
        setLikesCount(response.likes || 0);
        setDislikesCount(response.dislikes || 0);
        setIsLiked(response.liked_by?.includes(userInfo.id) || false);
        setIsDisliked(response.disliked_by?.includes(userInfo.id) || false);
      }

    } catch (error: any) {
      // 回滚UI状态
      setIsLiked(review.liked_by?.includes(userInfo.id) || false);
      setIsDisliked(review.disliked_by?.includes(userInfo.id) || false);
      setLikesCount(review.liked_by.length || 0);
      setDislikesCount(review.disliked_by.length || 0);

      Alert.alert('错误', error.message || '操作失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [userInfo?.id, isLiked, isDisliked, isLoading, review, entityType]);

  const handleReviewPress = () => {
    if (!isReply && showRepliesButton) {
      const entityIdValue = isBrandReview(review) ? review.brand_id : review.product_id;

      const reviewId = review.review_id || review._id;

      router.push({
        pathname: '/(stack)/review-detail',
        params: {
          reviewId,
          entityType,
          entityId: entityIdValue
        }
      });
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
      ]}
      onPress={handleReviewPress}
      disabled={isReply || !showRepliesButton}
      activeOpacity={isReply || !showRepliesButton ? 1 : 0.7}
    >
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <View style={styles.avatar}>
            {displayUserInfo.avatar ? (
              <Image
                source={{ uri: displayUserInfo.avatar }}
                style={styles.avatarImage}
                contentFit="cover"
              />
            ) : (
              <Icon name="account" size={isReply ? 16 : 20} color="#666" />
            )}
          </View>
          <Text style={styles.username}>
            {displayUserInfo.name}
          </Text>
        </View>
        <Text style={styles.date}>{reviewDate}</Text>
      </View>

      {review.title && !isReply && (
        <Text style={styles.title}>{review.title}</Text>
      )}

      {showRating && (
        <View style={styles.ratingContainer}>
          <View style={styles.stars}>
            {[1, 2, 3, 4, 5].map((star) => {
              // 将1-10分的评分转换为5星系统
              const normalizedRating = review.rating / 2; // 将10分制转换为5分制
              const isFilled = normalizedRating >= star;
              const isHalfFilled = normalizedRating >= star - 0.5 && normalizedRating < star;

              return (
                <Icon
                  key={star}
                  name={isFilled ? 'star' : isHalfFilled ? 'star-half-full' : 'star-outline'}
                  size={isReply ? 12 : 16}
                  color={isFilled || isHalfFilled ? '#ff9900' : '#ccc'}
                  style={styles.star}
                />
              );
            })}
          </View>
        </View>
      )}

      <Text style={[
        styles.content,
        compact && styles.compactContent
      ]}>
        {review.content}
      </Text>

      {review.images && review.images.length > 0 && (
        <View style={styles.imagesContainer}>
          {review.images.map((image: string, index: number) => (
            <Image
              key={index}
              source={{ uri: buildImageUrl(image) }}
              style={styles.image}
              contentFit="cover"
            />
          ))}
        </View>
      )}

      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleLike}
          disabled={isLoading}
        >
          <Icon
            name={isLiked ? "thumb-up" : "thumb-up-outline"}
            size={16}
            color={isLiked ? "#4caf50" : "#666"}
          />
          <Text style={[
            styles.actionText,
            isLiked && styles.likedText
          ]}>
            {likesCount}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleDislike}
          disabled={isLoading}
        >
          <Icon
            name={isDisliked ? "thumb-down" : "thumb-down-outline"}
            size={16}
            color={isDisliked ? "#f44336" : "#666"}
          />
          <Text style={[
            styles.actionText,
            isDisliked && styles.dislikedText
          ]}>
            {dislikesCount}
          </Text>
        </TouchableOpacity>

        {!isReply && showRepliesButton && (
          <TouchableOpacity style={styles.actionButton} onPress={handleReviewPress}>
            <Icon name="comment-outline" size={16} color="#666" />
            <Text style={styles.actionText}>{repliesCount}</Text>
          </TouchableOpacity>
        )}
      </View>

    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    marginBottom: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    overflow: 'hidden',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 16,
  },
  username: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  date: {
    fontSize: 12,
    color: '#999',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  stars: {
    flexDirection: 'row',
    marginRight: 8,
  },
  star: {
    marginRight: 2,
  },
  ratingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  recommendedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4caf50',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  recommendedText: {
    color: 'white',
    fontSize: 10,
    marginLeft: 2,
  },
  content: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
    marginBottom: 8,
  },
  compactContent: {
    marginBottom: 4,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  image: {
    width: 80,
    borderRadius: 4,
    margin: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  actionText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#666',
  },
  likedText: {
    color: '#4caf50',
    fontWeight: '500',
  },
  dislikedText: {
    color: '#f44336',
    fontWeight: '500',
  },
  repliesIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingVertical: 6,
    paddingHorizontal: 8,
    backgroundColor: '#f0f8ff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e0f0ff',
  },
  repliesIndicatorText: {
    fontSize: 12,
    color: '#3498db',
    marginLeft: 4,
    marginRight: 4,
    flex: 1,
  },
});





export default ReviewItem;

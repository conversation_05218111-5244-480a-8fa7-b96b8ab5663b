import { create } from 'zustand';
import threadsService, { Thread, ThreadListResponse } from '@/api/services/threadsService';

interface UserCommentsState {
  comments: Thread[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
  totalCount: number;
  refreshing: boolean;
}

interface UserCommentsActions {
  fetchUserComments: (userId: string, page?: number) => Promise<void>;
  refreshUserComments: (userId: string) => Promise<void>;
  loadMoreUserComments: (userId: string) => Promise<void>;
  clearComments: () => void;
  reset: () => void;
}

type UserCommentsStore = UserCommentsState & UserCommentsActions;

const initialState: UserCommentsState = {
  comments: [],
  loading: false,
  error: null,
  hasMore: true,
  currentPage: 1,
  totalPages: 1,
  totalCount: 0,
  refreshing: false,
};

export const useUserCommentsStore = create<UserCommentsStore>((set, get) => ({
  ...initialState,

  fetchUserComments: async (userId: string, page = 1) => {
    const { comments } = get();
    
    try {
      set({ 
        loading: page === 1, 
        error: null,
        refreshing: page === 1 && comments.length > 0
      });

      const response: ThreadListResponse = await threadsService.getUserComments(userId, page, 10);
      
      const newComments = response.threads || [];
      
      set({
        comments: page === 1 ? newComments : [...comments, ...newComments],
        loading: false,
        refreshing: false,
        currentPage: response.page || page,
        totalPages: Math.ceil((response.total || 0) / (response.limit || 10)),
        totalCount: response.total || 0,
        hasMore: (response.page || page) < Math.ceil((response.total || 0) / (response.limit || 10)),
        error: null,
      });
    } catch (error: any) {
      console.error('Fetch user comments error:', error);
      set({
        loading: false,
        refreshing: false,
        error: error.message || '获取评论失败',
      });
    }
  },

  refreshUserComments: async (userId: string) => {
    await get().fetchUserComments(userId, 1);
  },

  loadMoreUserComments: async (userId: string) => {
    const { hasMore, loading, currentPage } = get();
    
    if (!hasMore || loading) return;
    
    await get().fetchUserComments(userId, currentPage + 1);
  },

  clearComments: () => {
    set({ comments: [] });
  },

  reset: () => {
    set(initialState);
  },
}));

// Selector hooks for better performance
export const useUserComments = () => useUserCommentsStore(state => state.comments);
export const useUserCommentsLoading = () => useUserCommentsStore(state => state.loading);
export const useUserCommentsError = () => useUserCommentsStore(state => state.error);
export const useUserCommentsHasMore = () => useUserCommentsStore(state => state.hasMore);
export const useUserCommentsRefreshing = () => useUserCommentsStore(state => state.refreshing);
export const useUserCommentsTotalCount = () => useUserCommentsStore(state => state.totalCount);

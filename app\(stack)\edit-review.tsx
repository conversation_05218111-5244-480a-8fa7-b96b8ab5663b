import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import brandReviewService from '@/api/services/brandReviewService';
import reviewService from '@/api/services/productReviewService';
import { useUserInfo } from '@/store/userStore';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export default function EditReview() {
  const router = useRouter();
  const userInfo = useUserInfo();
  const queryClient = useQueryClient();
  
  const { entityType, entityId } = useLocalSearchParams<{
    entityType: 'product' | 'brand';
    entityId: string;
  }>();

  const [reviewType, setReviewType] = useState<'long' | 'short'>('long');
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [rating, setRating] = useState(0);

  // Create review mutation
  const createReviewMutation = useMutation<
    any, // 使用any作为返回类型来避免类型冲突
    any,
    any,
    unknown
  >({
    mutationFn: (reviewData: any) => {
      if (entityType === 'brand') {
        return brandReviewService.createReview(reviewData);
      } else if (entityType === 'product') {
        return reviewService.createReview(reviewData);
      } else {
        throw new Error('Unsupported entity type');
      }
    },
    onSuccess: (data) => {
      // Invalidate and refetch related queries
      if (entityType === 'brand') {
        queryClient.invalidateQueries({ 
          queryKey: ['brand-reviews', entityId] 
        });
        queryClient.invalidateQueries({ 
          queryKey: ['brand-review-summary', entityId] 
        });
      } else if (entityType === 'product') {
        queryClient.invalidateQueries({ 
          queryKey: ['reviews', 'product', entityId] 
        });
        queryClient.invalidateQueries({ 
          queryKey: ['product-review-summary', entityId] 
        });
      }
      
      Alert.alert('成功', '评论提交成功！', [
        { text: '确定', onPress: () => router.back() }
      ]);
    },
    onError: (error: any) => {
      console.error('Submit review error:', error);
      let errorMessage = '提交失败，请重试';
      
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      Alert.alert('错误', errorMessage);
    },
  });

  const handleRatingPress = (newRating: number) => {
    setRating(newRating);
  };

  const validateForm = () => {
    if (rating === 0) {
      Alert.alert('提示', '请选择评分');
      return false;
    }
    if (!content.trim()) {
      Alert.alert('提示', '请输入评论内容');
      return false;
    }
    if (reviewType === 'long' && !title.trim()) {
      Alert.alert('提示', '请输入评论标题');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    // Check if user is logged in
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录后再发表评论', [
        { text: '取消', style: 'cancel' },
        { text: '去登录', onPress: () => router.push('/login') }
      ]);
      return;
    }

    try {
      // Check if user has already reviewed this entity
      let hasReviewed = false;
      if (entityType === 'brand') {
        const response = await brandReviewService.hasUserReviewedBrand(entityId, userInfo.id);
        hasReviewed = response.hasReviewed;
      } else if (entityType === 'product') {
        const response = await reviewService.hasUserReviewedProduct(entityId, userInfo.id);
        hasReviewed = response.hasReviewed;
      }

      if (hasReviewed) {
        Alert.alert(
          '提示',
          `您已经对该${entityType === 'brand' ? '品牌' : '产品'}发表过评论，每个${entityType === 'brand' ? '品牌' : '产品'}只能评论一次`,
          [
            { text: '确定', style: 'default' }
          ]
        );
        return;
      }

      const reviewData = {
        user_id: userInfo.id,
        content: content.trim(),
        rating,
        is_long_review: reviewType === 'long',
        ...(reviewType === 'long' && title.trim() && { title: title.trim() }),
        images: [], // 可以后续添加图片功能
        liked_by: [],
        disliked_by: [],
      };

      if (entityType === 'brand') {
        createReviewMutation.mutate({
          ...reviewData,
          brand_id: entityId,
        });
      } else if (entityType === 'product') {
        createReviewMutation.mutate({
          ...reviewData,
          product_id: entityId,
        });
      } else {
        Alert.alert('提示', '不支持的实体类型');
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '检查评论状态失败，请重试');
    }
  };

  const renderStarRating = () => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      const isHalfFilled = rating >= i * 2 - 1 && rating < i * 2;
      const isFilled = rating >= i * 2;
      
      stars.push(
        <TouchableOpacity
          key={i}
          style={styles.starContainer}
          onPress={() => handleRatingPress(i * 2)}
        >
          <Icon
            name={isFilled ? 'star' : isHalfFilled ? 'star-half-full' : 'star-outline'}
            size={32}
            color={isFilled || isHalfFilled ? '#ff9900' : '#ccc'}
          />
        </TouchableOpacity>
      );
    }
    return stars;
  };

  const renderRatingButtons = () => {
    const ratingButtons = [];
    for (let i = 1; i <= 10; i++) {
      ratingButtons.push(
        <TouchableOpacity
          key={i}
          style={[
            styles.ratingButton,
            rating === i && styles.ratingButtonActive
          ]}
          onPress={() => handleRatingPress(i)}
        >
          <Text style={[
            styles.ratingButtonText,
            rating === i && styles.ratingButtonTextActive
          ]}>
            {i}
          </Text>
        </TouchableOpacity>
      );
    }
    return ratingButtons;
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Icon name="arrow-left" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>写评论</Text>
          <TouchableOpacity
            style={[styles.submitButton, createReviewMutation.isPending && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={createReviewMutation.isPending}
          >
            <Text style={styles.submitButtonText}>
              {createReviewMutation.isPending ? '提交中...' : '发布'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Show content for both brand and product reviews */}
        {(entityType === 'brand' || entityType === 'product') ? (
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Review Type Toggle */}
            <View style={styles.reviewTypeContainer}>
              <View style={styles.toggleContainer}>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    reviewType === 'long' && styles.toggleButtonActive
                  ]}
                  onPress={() => setReviewType('long')}
                >
                  <Text style={[
                    styles.toggleButtonText,
                    reviewType === 'long' && styles.toggleButtonTextActive
                  ]}>
                    长评
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    reviewType === 'short' && styles.toggleButtonActive
                  ]}
                  onPress={() => setReviewType('short')}
                >
                  <Text style={[
                    styles.toggleButtonText,
                    reviewType === 'short' && styles.toggleButtonTextActive
                  ]}>
                    短评
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Rating Section */}
            <View style={styles.ratingSection}>
              <View style={styles.starRating}>
                {renderStarRating()}
              </View>
              <View style={styles.ratingNumbers}>
                {renderRatingButtons()}
              </View>
            </View>

            {/* Title Input (Long Review Only) */}
            {reviewType === 'long' && (
              <View style={styles.titleSection}>
                <Text style={styles.sectionTitle}>标题</Text>
                <TextInput
                  style={styles.titleInput}
                  value={title}
                  onChangeText={setTitle}
                  placeholder="请输入评论标题"
                  maxLength={100}
                />
                <Text style={styles.characterCount}>{title.length}/100</Text>
              </View>
            )}

            {/* Content Input - Expanded */}
            <View style={styles.contentSection}>
              <Text style={styles.sectionTitle}>评论内容</Text>
              <TextInput
                style={styles.contentInput}
                value={content}
                onChangeText={setContent}
                placeholder={reviewType === 'long' ? '请详细描述您的使用体验...' : '请简要描述您的体验...'}
                multiline
                textAlignVertical="top"
                maxLength={reviewType === 'long' ? 2000 : 500}
                scrollEnabled={false}
              />
              <Text style={styles.characterCount}>
                {content.length}/{reviewType === 'long' ? 2000 : 500}
              </Text>
            </View>
          </ScrollView>
        ) : (
          <View style={styles.unsupportedContainer}>
            <Icon name="information-outline" size={60} color="#3498db" />
            <Text style={styles.unsupportedTitle}>不支持的类型</Text>
            <Text style={styles.unsupportedText}>
              当前不支持此类型的评论
            </Text>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  keyboardContainer: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 12 : 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  submitButton: {
    backgroundColor: '#3498db',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  reviewTypeContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  toggleContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    padding: 2,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  toggleButtonActive: {
    backgroundColor: '#3498db',
  },
  toggleButtonText: {
    fontSize: 14,
    color: '#666',
  },
  toggleButtonTextActive: {
    color: '#fff',
    fontWeight: '600',
  },
  ratingSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  starRating: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  starContainer: {
    marginHorizontal: 4,
  },
  ratingNumbers: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  ratingButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingButtonActive: {
    backgroundColor: '#3498db',
  },
  ratingButtonText: {
    fontSize: 12,
    color: '#666',
  },
  ratingButtonTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  titleSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  titleInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'android' ? 50 : 20,
  },
  contentSection: {
    padding: 16,
    paddingBottom: 20,
  },
  contentInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#f9f9f9',
    minHeight: 200,
    maxHeight: 300,
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 8,
  },
  unsupportedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  unsupportedTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  unsupportedText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

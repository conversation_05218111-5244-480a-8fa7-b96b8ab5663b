import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
interface SearchHeaderProps {
  searchText: string;
  onSearchTextChange: (text: string) => void;
  onBack: () => void;
  onFocus: () => void;
  onSubmit?: (text: string) => void;
  textInputRef: React.RefObject<TextInput | null>;
}

const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchText,
  onSearchTextChange,
  onBack,
  onFocus,
  onSubmit,
  textInputRef,
}) => {
  return (
    <View style={styles.searchHeader}>
      <TouchableOpacity onPress={onBack} style={styles.backButton}>
        <Ionicons name="chevron-back" size={24} color="#333" />
      </TouchableOpacity>
      <View style={styles.searchInputContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          ref={textInputRef}
          style={styles.searchInput}
          placeholder="搜索宠粮、品牌、帖子..."
          value={searchText}
          onChangeText={onSearchTextChange}
          onFocus={onFocus}
          onSubmitEditing={() => onSubmit?.(searchText)}
          returnKeyType="search"
          autoCorrect={false}
          autoCapitalize="none"
        />
        {searchText.length > 0 && (
          <TouchableOpacity onPress={() => onSearchTextChange('')} style={styles.clearButton}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </View>
      <TouchableOpacity onPress={() => onSubmit?.(searchText)} style={styles.searchButton}>
        <Text style={styles.searchText}>搜索</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    height: 60,
  },
  backButton: {
    marginRight: 12,
    padding: 4,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E0E0E0',
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 36
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 1,
    fontSize: 14,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  searchButton:{
    padding: 4,
    marginLeft:7,
  },
  searchText: {
    fontSize: 18,
    color:'#145A32',
  },
});

export default SearchHeader;

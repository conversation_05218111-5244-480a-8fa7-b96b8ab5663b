import apiClient from '../apiClient';

export interface UserStats {
  followersCount: number;
  followingCount: number;
  likesReceivedCount: number;
  bookmarksReceivedCount: number;
  threadsCount: number;
}

export interface FollowResult {
  isFollowing: boolean;
  followersCount: number;
}

export interface FollowStatus {
  isFollowing: boolean;
}

export interface UserListItemData {
  follower_id?: string;
  following_id?: string;
  created_at?: string;
  user?: {
    id: string;
    username: string;
    avatar_url?: string;
    bio?: string;
  };
}

export interface UserListResponse {
  items: UserListItemData[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export enum UserStatsApi {
  UserStats = "/user-stats",
  Follow = "/user-stats",
  FollowStatus = "/user-stats",
  Followers = "/user-stats",
  Following = "/user-stats",
}

// 获取用户统计信息
const getUserStats = async (userId: string) => {
  try {
    const response = await apiClient.get<UserStats>({
      url: `${UserStatsApi.UserStats}/${userId}`,
    });
    return response;
  } catch (error) {
    console.error(`User stats API error for ID ${userId}:`, error);
    throw error;
  }
};

// 切换关注状态
const toggleFollow = async (userId: string, currentUserId: string) => {
  try {
    console.log('toggleFollow called with:', { userId, currentUserId });
    const response = await apiClient.post<FollowResult>({
      url: `${UserStatsApi.Follow}/${userId}/follow`,
      headers: { 'user-id': currentUserId },
    });
    console.log('toggleFollow response:', response);
    return response;
  } catch (error) {
    console.error(`Toggle follow API error for ID ${userId}:`, error);
    throw error;
  }
};

// 检查关注状态
const getFollowStatus = async (userId: string, currentUserId: string) => {
  try {
    console.log('getFollowStatus called with:', { userId, currentUserId });
    const response = await apiClient.get<FollowStatus>({
      url: `${UserStatsApi.FollowStatus}/${userId}/follow-status`,
      headers: { 'user-id': currentUserId },
    });
    console.log('getFollowStatus response:', response);
    return response;
  } catch (error) {
    console.error(`Follow status API error for ID ${userId}:`, error);
    throw error;
  }
};

// 获取粉丝列表
const getFollowers = async (userId: string, page: number = 1, limit: number = 10): Promise<UserListResponse> => {
  try {
    const response = await apiClient.get<UserListResponse>({
      url: `${UserStatsApi.Followers}/${userId}/followers`,
      params: { page, limit },
    });
    return response;
  } catch (error) {
    console.error(`Get followers API error for ID ${userId}:`, error);
    throw error;
  }
};

// 获取关注列表
const getFollowing = async (userId: string, page: number = 1, limit: number = 10): Promise<UserListResponse> => {
  try {
    const response = await apiClient.get<UserListResponse>({
      url: `${UserStatsApi.Following}/${userId}/following`,
      params: { page, limit },
    });
    return response;
  } catch (error) {
    console.error(`Get following API error for ID ${userId}:`, error);
    throw error;
  }
};

const userStatsService = {
  getUserStats,
  toggleFollow,
  getFollowStatus,
  getFollowers,
  getFollowing,
};

export default userStatsService;

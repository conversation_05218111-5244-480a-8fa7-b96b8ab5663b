import type { Thread } from '@/api/services/threadsService';
import { useUserInfo } from '@/store/userStore';
import { formatTimeAgo } from '@/utils/timeUtils';
import { getAvatarUrl } from '@/utils/userUtils';
import { Ionicons } from '@expo/vector-icons';
import React, { useMemo } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface ReplyCardProps {
    thread: Thread;
    onLike?: () => void;
    onComment?: () => void;
    onAvatarPress?: (userId: string) => void;
    isLiking?: boolean;
    depth: number;
}

export default function ReplyCard({
    thread,
    onLike,
    onComment,
    onAvatarPress,
    isLiking = false,
    depth
}: ReplyCardProps) {
    const userInfo = useUserInfo();

    const processedContent = useMemo(() => {
        return thread.content.trim();
    }, [thread.content]);

    const isLiked = thread.liked_by.includes(userInfo.id || '');
    const isShortContent = processedContent.length < 10;


    const getAvatarSize = (depth: number) => {
        switch (depth) {
            case 0: return 32;
            case 1: return 23;
            case 2: return 28;
            default: return 24;
        }
    };
    const getCommentBoxPaddingVertical = (depth: number) => {
        switch (depth) {
            case 0: return 4;
            case 1: return 0;
            case 2: return 1;
            default: return 0;
        }
    }
    const getContainerPaddingVertical = (depth: number) => {
        switch (depth) {
            case 0: return 8;
            case 1: return 2;
            case 2: return 1;
            default: return 0;
        }
    }
    const getAvatarContainerRightMargin = (depth: number) => {
        switch (depth) {
            case 0: return 12;
            case 1: return 6;
            case 2: return 8;
            default: return 0;
        }
    }

    const avatarSize = getAvatarSize(depth);
    const commentBoxPaddingVertical = getCommentBoxPaddingVertical(depth);
    const containerPaddingVertical = getContainerPaddingVertical(depth);
    const avatarContainerRightMargin = getAvatarContainerRightMargin(depth);

    return (
        <TouchableOpacity
            style={[styles.container, { paddingVertical: containerPaddingVertical }]}
            onPress={onComment}
            activeOpacity={0.7}
        >
            <View style={styles.commentBox}>
                <TouchableOpacity
                    style={[styles.avatarContainer, { marginRight: avatarContainerRightMargin }]}
                    onPress={() => onAvatarPress?.(thread.user.id)}
                    activeOpacity={0.7}
                >
                    <Image
                        source={{ uri: getAvatarUrl(thread.user.avatar) }}
                        style={[styles.avatar, { width: avatarSize, height: avatarSize, borderRadius: avatarSize / 2 }]}
                    />
                </TouchableOpacity>
                <View style={[styles.commentContentBox, { paddingVertical: commentBoxPaddingVertical }]}>
                    <View style={styles.leftContent}>
                        <Text style={styles.username}>{thread.user.username}</Text>
                        {isShortContent ? (
                            <View style={styles.sameLineContainer}>
                                <Text style={styles.text}>{processedContent}</Text>
                                <Text style={styles.timeAgoInline}> {formatTimeAgo(thread.created_at)} 成都</Text>
                            </View>
                        ) : (
                            <>
                                <Text style={styles.text} numberOfLines={10}>{processedContent}</Text>
                                <Text style={styles.timeAgo}>{formatTimeAgo(thread.created_at)} 成都 </Text>
                            </>
                        )}
                    </View>
                    <View style={styles.replyActions}>
                        <TouchableOpacity
                            style={styles.replyActionButton}
                            onPress={(e) => {
                                e.stopPropagation();
                                onLike?.();
                            }}
                            disabled={isLiking}
                        >
                            <Ionicons
                                name={isLiked ? "heart" : "heart-outline"}
                                size={18}
                                color={isLiked ? "#ff4757" : "#666"}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        marginVertical: 0,
        paddingVertical: 4,
        paddingHorizontal: 4,
    },
    commentBox: {
        flexDirection: 'row',
        paddingHorizontal: 12,
        borderRadius: 8,
    },
    avatarContainer: {
        marginRight: 12,
    },
    avatar: {
        borderRadius: 23,
    },
    defaultAvatar: {
        backgroundColor: '#f0f0f0',
        alignItems: 'center',
        justifyContent: 'center',
    },
    commentContentBox: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        flex: 1,
    },
    leftContent: {
        flex: 1,
    },
    username: {
        fontSize: 14,
        marginBottom: 4,
        fontWeight: '500',
        color: 'grey',
    },
    text: {
        fontSize: 14,
        color: 'black',
        lineHeight: 18,
    },
    timeAgo: {
        fontSize: 11,
        color: '#999',
    },
    replyActions: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        paddingTop: 4,
    },
    replyActionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 4,

        paddingHorizontal: 8,
    },
    sameLineContainer: {
        flexDirection: 'row',
        alignItems: 'baseline',
        flexWrap: 'wrap',
        marginBottom: 6,
    },
    timeAgoInline: {
        fontSize: 11,
        color: '#999',
        marginLeft: 4,
    },
});

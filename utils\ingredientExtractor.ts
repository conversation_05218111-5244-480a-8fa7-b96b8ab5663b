/**
 * 从产品数据中提取所有独特的成分名称
 * @param products 产品数据数组
 * @returns 所有独特成分的集合
 */
export const extractUniqueIngredients = (products: any[]): Set<string> => {
	const ingredients = new Set<string>();

	for (const product of products) {
		// 处理优质成分
		if (product.quality_ingredients && Array.isArray(product.quality_ingredients)) {
			for (const ingredient of product.quality_ingredients) {
				ingredients.add(ingredient.toLowerCase().trim());
			}
		}

		// 处理可疑成分
		if (product.questionable_ingredients && Array.isArray(product.questionable_ingredients)) {
			for (const ingredient of product.questionable_ingredients) {
				ingredients.add(ingredient.toLowerCase().trim());
			}
		}

		// 处理过敏源成分
		if (product.allergen_ingredients && Array.isArray(product.allergen_ingredients)) {
			for (const ingredient of product.allergen_ingredients) {
				ingredients.add(ingredient.toLowerCase().trim());
			}
		}
	}

	return ingredients;
};

/**
 * 生成成分翻译模板，可以复制到i18n文件中
 * @param ingredients 成分集合
 * @returns 格式化的JSON字符串
 */
export const generateIngredientTranslationTemplate = (ingredients: Set<string>): string => {
	const translations: Record<string, string> = {};

	for (const ingredient of ingredients) {
		translations[ingredient] = ""; // 空字符串表示需要翻译
	}

	return JSON.stringify(translations, null, 2);
};

/**
 * 工具函数：帮助开发人员从API响应中提取成分并生成翻译模板
 * @param apiResponse API响应数据
 */
export const extractIngredientsFromApiResponse = (apiResponse: any): void => {
	if (apiResponse?.data?.products && Array.isArray(apiResponse.data.products)) {
		const ingredients = extractUniqueIngredients(apiResponse.data.products);
		const template = generateIngredientTranslationTemplate(ingredients);
		console.log("提取到的成分翻译模板:");
		console.log(template);
	} else {
		console.error("API响应格式不正确，无法提取成分");
	}
};

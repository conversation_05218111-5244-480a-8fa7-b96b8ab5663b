
import type { Permission } from "@/types/entity";
import apiClient from "../apiClient";

export enum PermissionApi {
	Permission = "permissions",
}

const getPermissionTree = async () => {
	try {
		const response = await apiClient.get<Permission[]>({
			url: `${PermissionApi.Permission}/tree`,
		});
		return response;
	} catch (error) {
		console.error("Permission API error:", error);
		throw error;
	}
};

const createPermission = async (permissionData: Partial<Permission>) => {
	try {
		const response = await apiClient.post<Permission>({
			url: `${PermissionApi.Permission}`,
			data: permissionData,
		});
		return response;
	} catch (error) {
		console.error("Permission Create API error:", error);
		throw error;
	}
};

const updatePermission = async (id: string, permissionData: Partial<Permission>) => {
	try {
		const response = await apiClient.put<Permission>({
			url: `${PermissionApi.Permission}/${id}`,
			data: permissionData,
		});
		return response;
	} catch (error) {
		console.error("Permission Update API error:", error);
		throw error;
	}
};

const deletePermission = async (id: string) => {
	try {
		const response = await apiClient.delete<Permission>({
			url: `${PermissionApi.Permission}/${id}`,
		});
		return response;
	} catch (error) {
		console.error("Permission Delete API error:", error);
		throw error;
	}
};

export default {
	getPermissionTree,
	createPermission,
	updatePermission,
	deletePermission,
};

import RatingSection from '@/components/ui/RatingSection';
import { useBrandReviewSummary } from '@/store/brandReviewStore';
import { Brand } from '@/types/entity';
import { getBrandLogoUrlSync } from '@/utils/imageUtils';
import React, { useMemo, useState } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import TopProductsSection from './TopProductsSection';

interface BrandDetailHeaderProps {
  brand: Brand;
}

const BrandDetailHeader: React.FC<BrandDetailHeaderProps> = ({ brand }) => {
  const [imageError, setImageError] = useState(false);
  const { summary } = useBrandReviewSummary(brand._id);
  
  const imageUrl = useMemo(() => {
    if (!imageError && brand._id) {
      return getBrandLogoUrlSync(brand._id);
    }
    return '';
  }, [brand._id, imageError]);
  

  
  const handleImageError = () => {
    setImageError(true);
  };



  return (
    <View style={styles.container}>

      <View style={styles.heroContainer}>
        {imageUrl && !imageError ? (
          <Image
            source={{ uri: imageUrl }}
            style={styles.brandImage}
            resizeMode="contain"
            onError={handleImageError}
          />
        ) : (
          <View style={styles.placeholderLogo}>
            <Icon name="store" size={60} />
          </View>
        )}
      </View>


      <View style={styles.infoContainer}>
        <Text style={styles.brandName}>{brand.name}</Text>
        
        {brand.website_url && (
          <View style={styles.websiteContainer}>
            <Icon name="web" size={16} color="#3498db" style={styles.websiteIcon} />
            <Text style={styles.websiteText}>{brand.website_url}</Text>
          </View>
        )}
        
        <View style={styles.dateContainer}>
          <Icon name="calendar" size={14} color="#666" style={styles.dateIcon} />
          <Text style={styles.dateText}>
            收录时间: {new Date(brand.created_at).toLocaleDateString()}
          </Text>
        </View>
      </View>
      
      <RatingSection summary={summary} />
      
      <View style={styles.descriptionContainer}>
        <Text style={styles.descriptionTitle}>品牌简介</Text>
        <Text style={styles.descriptionText}>
          {brand.desc || "暂无品牌介绍信息。"}
        </Text>
      </View>

      <TopProductsSection brandId={brand._id} />
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
  },
  heroContainer: {
    height: 220, 
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandImage: {
    width: 150,
    height: 150,
    borderRadius: 10,
  },
  placeholderLogo: {
    width: 150,
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  infoContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
    backgroundColor: 'white',
    marginTop: -30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  brandName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  websiteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  websiteIcon: {
    marginRight: 6,
  },
  websiteText: {
    fontSize: 14,
    color: '#3498db',
    textDecorationLine: 'underline',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginRight: 6,
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },

  descriptionContainer: {
    backgroundColor: 'white',
    marginTop: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2, 
  },
  descriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
});

export default BrandDetailHeader;

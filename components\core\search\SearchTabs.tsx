import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
type TabType = 'brands' | 'products' | 'threads';

interface SearchTabsProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

const SearchTabs: React.FC<SearchTabsProps> = ({
  activeTab,
  onTabChange,
}) => {
  return (
    <View style={styles.tabContainer}>
      <TouchableOpacity 
        style={[styles.tabItem, activeTab === 'brands' && styles.activeTabItem]}
        onPress={() => onTabChange('brands')}
      >
        <Text style={[styles.tabText, activeTab === 'brands' && styles.activeTabText]}>
          品牌 
        </Text>
      </TouchableOpacity>
      <TouchableOpacity 
        style={[styles.tabItem, activeTab === 'products' && styles.activeTabItem]}
        onPress={() => onTabChange('products')}
      >
        <Text style={[styles.tabText, activeTab === 'products' && styles.activeTabText]}>
          产品
        </Text>
      </TouchableOpacity>
      <TouchableOpacity 
        style={[styles.tabItem, activeTab === 'threads' && styles.activeTabItem]}
        onPress={() => onTabChange('threads')}
      >
        <Text style={[styles.tabText, activeTab === 'threads' && styles.activeTabText]}>
          帖子
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    paddingHorizontal:15
  },
  tabItem: {
    paddingVertical: 8,
    paddingHorizontal:0,
    marginHorizontal:10
  },
  activeTabItem: {
    shadowOffset: { width: 0, height: 1 },
    borderBottomColor: '#4d920f',
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#333',
    fontWeight: '600',
  },
});

export default SearchTabs;
export type { TabType };

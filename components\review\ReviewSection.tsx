import React from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import ReviewItem from './ReviewItem';

interface ReviewSectionProps {
  entityType: 'product' | 'brand';
  entityId: string;
  reviews: any[];
  isLoading: boolean;
  isLoadingMore?: boolean;
  isRefreshing?: boolean;
  error: any;
  reviewType: 'all' | 'long' | 'short';
  onReviewTypeChange: (type: 'all' | 'long' | 'short') => void;
  sortBy?: 'latest' | 'hot';
  onSortByChange?: (sortBy: 'latest' | 'hot') => void;
  hasMore?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  brandHeader?: React.ReactNode;
}

const ReviewSection: React.FC<ReviewSectionProps> = ({
  entityType,
  entityId,
  reviews,
  isLoading,
  isLoadingMore = false,
  isRefreshing = false,
  error,
  reviewType,
  onReviewTypeChange,
  sortBy = 'latest',
  onSortByChange,
  hasMore = false,
  onRefresh,
  onLoadMore,
  brandHeader,
}) => {

  const renderReviewTypeFilter = () => (
    <View style={styles.filterContainer}>
      <View style={styles.filterRow}>
        {/* 左边：类型筛选按钮 */}
        <View style={styles.typeFilters}>
          {[
            { key: 'all', label: '全部' },
            { key: 'long', label: '长评' },
            { key: 'short', label: '短评' },
          ].map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.typeFilterButton,
                reviewType === filter.key && styles.typeFilterButtonActive,
              ]}
              onPress={() => onReviewTypeChange(filter.key as 'all' | 'long' | 'short')}
            >
              <Text
                style={[
                  styles.typeFilterButtonText,
                  reviewType === filter.key && styles.typeFilterButtonTextActive,
                ]}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        {/* 右边：排序选择按钮 */}
        {onSortByChange && (
          <TouchableOpacity 
            style={styles.sortButton}
            onPress={() => onSortByChange(sortBy === 'latest' ? 'hot' : 'latest')}
          >
            <Text style={styles.sortButtonText}>
              {sortBy === 'latest' ? '最新' : '最热'}
            </Text>
            <Icon name="chevron-down" size={16} color="#666" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderHeader = () => (
    <View>
      {brandHeader}
      <View style={styles.reviewsHeader}>
      </View>
      {renderReviewTypeFilter()}
    </View>
  );

  const renderFooter = () => {
    if (isLoadingMore) {
      return (
        <View style={styles.loadingFooter}>
          <ActivityIndicator size="small" color="#3498db" />
          <Text style={styles.loadingText}>加载更多...</Text>
        </View>
      );
    }
    
    if (!hasMore && reviews.length > 0) {
      return (
        <View style={styles.endFooter}>
          <Text style={styles.endText}>没有更多评论了</Text>
        </View>
      );
    }
    
    return null;
  };

  const renderEmpty = () => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#3498db" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.emptyContainer}>
          <Icon name="alert-circle-outline" size={40} color="#e74c3c" />
          <Text style={styles.errorText}>加载评论失败</Text>
          <Text style={styles.errorSubtext}>
            {error?.message || '请检查网络连接'}
          </Text>
          {__DEV__ && (
            <TouchableOpacity
              style={styles.retryButton}
              onPress={onRefresh}
            >
              <Text style={styles.retryText}>重试</Text>
            </TouchableOpacity>
          )}
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Icon name="comment-outline" size={40} color="#ccc" />
        <Text style={styles.emptyText}>暂无评论</Text>
        <Text style={styles.emptySubtext}>成为第一个发表评论的人吧</Text>
      </View>
    );
  };

  const renderItem = ({ item }: { item: any }) => (
    <ReviewItem
      review={item}
      entityType={entityType}
      isReply={false}
      compact={false}
      showRepliesButton={true}
      showRating={true}
    />
  );

  return (
    <FlatList
      style={styles.container}
      data={reviews}
      keyExtractor={(item, index) => item.review_id || item._id || index.toString()}
      renderItem={renderItem}
      ListHeaderComponent={renderHeader}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={reviews.length === 0 ? renderEmpty : null}
      ItemSeparatorComponent={() => <View style={styles.separator} />}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          colors={['#3498db']}
          tintColor="#3498db"
          title="下拉刷新"
          titleColor="#666"
        />
      }
      onEndReached={onLoadMore}
      onEndReachedThreshold={0.1}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={[
        styles.listContainer,
        reviews.length === 0 && styles.emptyListContainer
      ]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  reviewsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  typeFilters: {
    flexDirection: 'row',
    flex: 1,
  },
  typeFilterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: '#fff',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e1e8ff',
    minWidth: 60,
    alignItems: 'center',
  },
  typeFilterButtonActive: {
    backgroundColor: '#3498db',
    borderColor: '#3498db',
  },
  typeFilterButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  typeFilterButtonTextActive: {
    color: '#fff',
    fontWeight: '600',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: 70,
  },
  sortButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
    marginRight: 4,
  },
  separator: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginHorizontal: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  emptyListContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    minHeight: 200,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: 12,
    color: '#ccc',
    marginTop: 4,
  },
  errorText: {
    fontSize: 16,
    color: '#e74c3c',
    marginTop: 12,
  },
  errorSubtext: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#3498db',
  },
  endFooter: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  endText: {
    fontSize: 12,
    color: '#999',
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#3498db',
    borderRadius: 20,
  },
  retryText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
});



export default ReviewSection;

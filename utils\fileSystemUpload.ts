import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

export interface UploadResult {
  imageUrl: string;
  filename: string;
}

/**
 * 使用 Expo FileSystem 上传图片到服务器
 * @param asset ImagePicker 选择的图片资源
 * @param uploadType 上传类型：'thread' | 'user'
 * @param additionalData 额外的表单数据
 * @returns Promise<UploadResult>
 */
export const uploadImageWithFileSystem = async (
  asset: ImagePicker.ImagePickerAsset,
  uploadType: 'thread' | 'user' = 'thread',
  additionalData?: { [key: string]: string }
): Promise<UploadResult> => {
  try {
    // 确定上传端点
    const uploadEndpoint = uploadType === 'thread'
      ? `${API_BASE_URL}/api/threads/upload-image`
      : `${API_BASE_URL}/api/font-user/upload-image`;

    // 获取文件信息
    const fileInfo = await FileSystem.getInfoAsync(asset.uri);
    if (!fileInfo.exists) {
      throw new Error('文件不存在');
    }

    // 准备文件名
    const fileName = asset.fileName || `${uploadType}_${Date.now()}.jpg`;
    
    // 准备上传选项
    const uploadOptions: FileSystem.FileSystemUploadOptions = {
      httpMethod: 'POST',
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      fieldName: 'image',
      mimeType: asset.mimeType || asset.type || 'image/jpeg',
      parameters: additionalData || {},
    };

    // 执行上传
    const uploadResult = await FileSystem.uploadAsync(
      uploadEndpoint,
      asset.uri,
      uploadOptions
    );

    // 检查响应状态
    if (uploadResult.status !== 200) {
      throw new Error(`上传失败，状态码: ${uploadResult.status}`);
    }

    // 解析响应
    let responseData;
    try {
      responseData = JSON.parse(uploadResult.body);
    } catch (parseError) {
      throw new Error('服务器响应格式错误');
    }

    // 检查响应格式
    if (!responseData.success) {
      throw new Error(responseData.message || '上传失败');
    }

    const result = responseData.data;
    if (!result.imageUrl || !result.filename) {
      throw new Error('服务器返回数据格式错误');
    }

    return result;

  } catch (error) {
    
    // 提供更友好的错误信息
    if (error instanceof Error) {
      if (error.message.includes('Network')) {
        throw new Error('网络连接失败，请检查网络设置');
      } else if (error.message.includes('timeout')) {
        throw new Error('上传超时，请重试');
      } else {
        throw error;
      }
    }
    
    throw new Error('图片上传失败，请重试');
  }
};

/**
 * 专门用于帖子图片上传的函数
 * @param asset ImagePicker 选择的图片资源
 * @returns Promise<UploadResult>
 */
export const uploadThreadImage = async (
  asset: ImagePicker.ImagePickerAsset
): Promise<UploadResult> => {
  return uploadImageWithFileSystem(asset, 'thread');
};

/**
 * 专门用于用户头像/背景图片上传的函数
 * @param asset ImagePicker 选择的图片资源
 * @param type 图片类型：'avatar' | 'background'
 * @param userId 用户ID
 * @returns Promise<UploadResult>
 */
export const uploadUserImage = async (
  asset: ImagePicker.ImagePickerAsset,
  type: 'avatar' | 'background',
  userId: string
): Promise<UploadResult> => {
  return uploadImageWithFileSystem(asset, 'user', {
    type,
    userId
  });
};

/**
 * 获取文件大小的辅助函数
 * @param uri 文件URI
 * @returns Promise<number> 文件大小（字节）
 */
export const getFileSize = async (uri: string): Promise<number> => {
  try {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    return fileInfo.exists ? (fileInfo.size || 0) : 0;
  } catch (error) {
    console.error('获取文件大小失败:', error);
    return 0;
  }
};

/**
 * 检查文件是否存在
 * @param uri 文件URI
 * @returns Promise<boolean>
 */
export const fileExists = async (uri: string): Promise<boolean> => {
  try {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    return fileInfo.exists;
  } catch (error) {
    console.error('检查文件存在性失败:', error);
    return false;
  }
};

/**
 * 复制文件到应用文档目录（可选功能，用于本地缓存）
 * @param sourceUri 源文件URI
 * @param fileName 目标文件名
 * @returns Promise<string> 新的文件URI
 */
export const copyToDocumentDirectory = async (
  sourceUri: string,
  fileName: string
): Promise<string> => {
  try {
    const documentDirectory = FileSystem.documentDirectory;
    if (!documentDirectory) {
      throw new Error('无法访问文档目录');
    }

    const targetUri = `${documentDirectory}${fileName}`;
    
    // 如果目标文件已存在，先删除
    const targetExists = await fileExists(targetUri);
    if (targetExists) {
      await FileSystem.deleteAsync(targetUri);
    }

    // 复制文件
    await FileSystem.copyAsync({
      from: sourceUri,
      to: targetUri
    });

    return targetUri;
  } catch (error) {
    console.error('复制文件失败:', error);
    throw new Error('文件复制失败');
  }
};

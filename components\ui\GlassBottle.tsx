import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

interface GlassBottleProps {
  value: string;
  color: string;
}

const GlassBottle: React.FC<GlassBottleProps> = ({ value, color }) => {
  const waveAnim = useRef(new Animated.Value(0)).current;
  const bubbleAnim1 = useRef(new Animated.Value(0)).current;
  const bubbleAnim2 = useRef(new Animated.Value(0)).current;
  const bubbleAnim3 = useRef(new Animated.Value(0)).current;

  const getPercentage = (val: string): number => {
    if (val === 'N/A') return 0;
    const numericValue = parseFloat(val.replace('%', ''));
    return isNaN(numericValue) ? 0 : Math.min(numericValue, 100);
  };

  const fillPercentage = getPercentage(value);

  useEffect(() => {
    // Water wave animation
    const waveAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(waveAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: false,
        }),
        Animated.timing(waveAnim, {
          toValue: 0,
          duration: 2000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: false,
        }),
      ])
    );

    // Bubble animations with different delays
    const bubbleAnimation1 = Animated.loop(
      Animated.timing(bubbleAnim1, {
        toValue: 1,
        duration: 3000,
        easing: Easing.linear,
        useNativeDriver: false,
      })
    );

    const bubbleAnimation2 = Animated.loop(
      Animated.timing(bubbleAnim2, {
        toValue: 1,
        duration: 2500,
        easing: Easing.linear,
        useNativeDriver: false,
      })
    );

    const bubbleAnimation3 = Animated.loop(
      Animated.timing(bubbleAnim3, {
        toValue: 1,
        duration: 3500,
        easing: Easing.linear,
        useNativeDriver: false,
      })
    );

    waveAnimation.start();
    
    // Start bubble animations with delays
    setTimeout(() => bubbleAnimation1.start(), 0);
    setTimeout(() => bubbleAnimation2.start(), 800);
    setTimeout(() => bubbleAnimation3.start(), 1600);

    return () => {
      waveAnimation.stop();
      bubbleAnimation1.stop();
      bubbleAnimation2.stop();
      bubbleAnimation3.stop();
    };
  }, [waveAnim, bubbleAnim1, bubbleAnim2, bubbleAnim3]);

  const waveHeight = waveAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 2],
  });

  const bubble1Y = bubbleAnim1.interpolate({
    inputRange: [0, 1],
    outputRange: [40, -5],
  });

  const bubble2Y = bubbleAnim2.interpolate({
    inputRange: [0, 1],
    outputRange: [35, -5],
  });

  const bubble3Y = bubbleAnim3.interpolate({
    inputRange: [0, 1],
    outputRange: [45, -5],
  });

  const bubble1Opacity = bubbleAnim1.interpolate({
    inputRange: [0, 0.1, 0.9, 1],
    outputRange: [0, 0.7, 0.7, 0],
  });

  const bubble2Opacity = bubbleAnim2.interpolate({
    inputRange: [0, 0.1, 0.9, 1],
    outputRange: [0, 0.5, 0.5, 0],
  });

  const bubble3Opacity = bubbleAnim3.interpolate({
    inputRange: [0, 0.1, 0.9, 1],
    outputRange: [0, 0.6, 0.6, 0],
  });

  return (
    <View style={styles.glassBottle}>
      <View style={styles.bottleNeck} />
      <View style={styles.bottleBody}>
        {/* Liquid fill with wave effect */}
        <Animated.View 
          style={[
            styles.liquidFill, 
            { 
              backgroundColor: color, 
              height: `${fillPercentage}%`,
              transform: [{ translateY: waveHeight }]
            }
          ]} 
        />
        
        {/* Bubbles */}
        {fillPercentage > 10 && (
          <>
            <Animated.View
              style={[
                styles.bubble,
                {
                  left: 6,
                  width: 3,
                  height: 3,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  transform: [{ translateY: bubble1Y }],
                  opacity: bubble1Opacity,
                }
              ]}
            />
            <Animated.View
              style={[
                styles.bubble,
                {
                  left: 14,
                  width: 2,
                  height: 2,
                  backgroundColor: 'rgba(255, 255, 255, 0.6)',
                  transform: [{ translateY: bubble2Y }],
                  opacity: bubble2Opacity,
                }
              ]}
            />
            <Animated.View
              style={[
                styles.bubble,
                {
                  left: 10,
                  width: 2.5,
                  height: 2.5,
                  backgroundColor: 'rgba(255, 255, 255, 0.7)',
                  transform: [{ translateY: bubble3Y }],
                  opacity: bubble3Opacity,
                }
              ]}
            />
          </>
        )}
        
        {/* Glass reflection */}
        <View style={styles.glassReflection} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  glassBottle: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    height: 60,
    marginBottom: 4,
  },
  bottleNeck: {
    width: 8,
    height: 8,
    backgroundColor: '#e0e0e0',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    borderWidth: 1,
    borderColor: '#d0d0d0',
  },
  bottleBody: {
    width: 24,
    height: 50,
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    position: 'relative',
    overflow: 'hidden',
    justifyContent: 'flex-end',
  },
  liquidFill: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    opacity: 0.8,
  },
  glassReflection: {
    position: 'absolute',
    top: 2,
    left: 2,
    width: 6,
    height: '40%',
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 3,
  },
  bubble: {
    position: 'absolute',
    borderRadius: 50,
  },
});

export default GlassBottle;

import { useState, useCallback, useMemo } from 'react';
import { 
  PetFoodFilters, 
  FilterState, 
  NutritionRange,
  SORT_OPTIONS 
} from '@/types/pet-food-filter';

// 初始筛选状态
const initialFilters: PetFoodFilters = {
  searchQuery: '',
  brands: [],
  productTypes: [],
  excludeAllergens: [],
};

const initialState: FilterState = {
  filters: initialFilters,
  sortBy: 'quality',
  sortOrder: 'desc',
  page: 1,
  limit: 20,
};

export const usePetFoodFilter = () => {
  const [filterState, setFilterState] = useState<FilterState>(initialState);

  // 更新搜索关键词
  const updateSearchQuery = useCallback((query: string) => {
    setFilterState(prev => ({
      ...prev,
      filters: { ...prev.filters, searchQuery: query },
      page: 1, // 重置页码
    }));
  }, []);

  // 更新品牌筛选
  const updateBrands = useCallback((brands: string[]) => {
    setFilterState(prev => ({
      ...prev,
      filters: { ...prev.filters, brands },
      page: 1,
    }));
  }, []);

  // 更新产品类型筛选
  const updateProductTypes = useCallback((productTypes: string[]) => {
    setFilterState(prev => ({
      ...prev,
      filters: { ...prev.filters, productTypes },
      page: 1,
    }));
  }, []);

  // 更新营养成分范围
  const updateNutritionRange = useCallback((
    nutrient: keyof Pick<PetFoodFilters, 'protein' | 'fat' | 'carbs' | 'calories'>,
    range: NutritionRange | undefined
  ) => {
    setFilterState(prev => ({
      ...prev,
      filters: { ...prev.filters, [nutrient]: range },
      page: 1,
    }));
  }, []);

  // 更新成分质量筛选
  const updateQualityFilters = useCallback((updates: {
    minQualityIngredients?: number;
    maxQuestionableIngredients?: number;
  }) => {
    setFilterState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...updates },
      page: 1,
    }));
  }, []);

  // 更新过敏原筛选
  const updateAllergens = useCallback((allergens: string[]) => {
    setFilterState(prev => ({
      ...prev,
      filters: { ...prev.filters, excludeAllergens: allergens },
      page: 1,
    }));
  }, []);

  // 更新评价筛选
  const updateRatingFilters = useCallback((updates: {
    minRating?: number;
    minReviewCount?: number;
  }) => {
    setFilterState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...updates },
      page: 1,
    }));
  }, []);

  // 更新排序
  const updateSort = useCallback((sortBy: string, sortOrder?: 'asc' | 'desc') => {
    const sortOption = SORT_OPTIONS.find(option => option.key === sortBy);
    const order = sortOrder || sortOption?.order || 'desc';
    
    setFilterState(prev => ({
      ...prev,
      sortBy,
      sortOrder: order,
      page: 1,
    }));
  }, []);

  // 更新分页
  const updatePage = useCallback((page: number) => {
    setFilterState(prev => ({ ...prev, page }));
  }, []);

  // 重置所有筛选
  const resetFilters = useCallback(() => {
    setFilterState(initialState);
  }, []);

  // 重置特定筛选
  const resetFilter = useCallback((filterKey: keyof PetFoodFilters) => {
    setFilterState(prev => {
      const newFilters = { ...prev.filters };
      if (filterKey === 'brands' || filterKey === 'productTypes' || filterKey === 'excludeAllergens') {
        newFilters[filterKey] = [];
      } else if (filterKey === 'searchQuery') {
        newFilters[filterKey] = '';
      } else {
        delete newFilters[filterKey];
      }
      return {
        ...prev,
        filters: newFilters,
        page: 1,
      };
    });
  }, []);

  // 检查是否有活跃的筛选条件
  const hasActiveFilters = useMemo(() => {
    const { filters } = filterState;
    return (
      filters.searchQuery.length > 0 ||
      filters.excludeAllergens.length > 0 ||
      filters.protein !== undefined ||
      filters.fat !== undefined ||
      filters.carbs !== undefined ||
      filters.calories !== undefined ||
      filters.minQualityIngredients !== undefined ||
      filters.maxQuestionableIngredients !== undefined ||
      filters.minRating !== undefined ||
      filters.minReviewCount !== undefined
    );
  }, [filterState.filters]);

  // 获取活跃筛选条件的数量
  const activeFilterCount = useMemo(() => {
    const { filters } = filterState;
    let count = 0;
    
    if (filters.searchQuery.length > 0) count++;
    if (filters.excludeAllergens.length > 0) count++;
    if (filters.protein !== undefined) count++;
    if (filters.fat !== undefined) count++;
    if (filters.carbs !== undefined) count++;
    if (filters.calories !== undefined) count++;
    if (filters.minQualityIngredients !== undefined) count++;
    if (filters.maxQuestionableIngredients !== undefined) count++;
    if (filters.minRating !== undefined) count++;
    if (filters.minReviewCount !== undefined) count++;
    
    return count;
  }, [filterState.filters]);

  return {
    filterState,
    hasActiveFilters,
    activeFilterCount,
    
    // 更新方法
    updateSearchQuery,
    updateBrands,
    updateProductTypes,
    updateNutritionRange,
    updateQualityFilters,
    updateAllergens,
    updateRatingFilters,
    updateSort,
    updatePage,
    
    // 重置方法
    resetFilters,
    resetFilter,
  };
};

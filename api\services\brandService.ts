
import type { Brand } from "@/types/entity";
import apiClient from "../apiClient";

export interface BrandRes {
	totalCount?: number;
	totalPages?: number;
	currentPage?: number;
	brands?: Brand[];
	brand?: Brand;
}

export interface CreateBrandData {
	name: string;
	website_url?: string;
	logo_url?: string;
}

export interface UpdateBrandData {
	name?: string;
	website_url?: string;
	logo_url?: string;
}

export enum BrandApi {
	Brand = "brands",
}

const getBrands = async (page = 1, name?: string) => {
	try {
		const params: Record<string, any> = { page };
		if (name) params.name = name;

		const response = await apiClient.get<BrandRes>({
			url: `${BrandApi.Brand}/`,
			params,
		});
		return response;
	} catch (error) {
		console.error("Brand API error:", error);
		throw error;
	}
};

const getBrandById = async (id: string) => {
	try {
		const response = await apiClient.get<Brand>({
			url: `${BrandApi.Brand}/${id}`,
		});
		return response;
	} catch (error) {
		console.error("Brand API error:", error);
		throw error;
	}
};

const createBrand = async (brandData: CreateBrandData) => {
	try {
		const response = await apiClient.post<BrandRes>({
			url: `${BrandApi.Brand}/`,
			data: brandData,
		});
		return response;
	} catch (error) {
		console.error("Brand creation error:", error);
		throw error;
	}
};

const updateBrand = async (id: string, brandData: UpdateBrandData) => {
	try {
		const response = await apiClient.put<BrandRes>({
			url: `${BrandApi.Brand}/${id}`,
			data: brandData,
		});
		return response;
	} catch (error) {
		console.error("Brand update error:", error);
		throw error;
	}
};

const deleteBrand = async (id: string) => {
	try {
		const response = await apiClient.delete<BrandRes>({
			url: `${BrandApi.Brand}/${id}`,
		});
		return response;
	} catch (error) {
		console.error("Brand deletion error:", error);
		throw error;
	}
};

const getAllBrandsNoPage = async () => {
	try {
		const response = await apiClient.get<BrandRes>({
			url: `${BrandApi.Brand}`,
			params: { list: true },
		});
		return response;
	} catch (error) {
		console.error("Brand API error:", error);
		throw error;
	}
};

export default {
	getBrands,
	getBrandById,
	createBrand,
	updateBrand,
	deleteBrand,
	getAllBrandsNoPage,
};

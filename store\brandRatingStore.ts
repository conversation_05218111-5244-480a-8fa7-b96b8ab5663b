import brandReviewService from '@/api/services/brandReviewService';
import { useQuery } from '@tanstack/react-query';

// Hook to get brand rating distribution
export const useBrandRatingDistribution = (brandId: string) => {
  return useQuery({
    queryKey: ['brandRatingDistribution', brandId],
    queryFn: async () => {
      if (!brandId) return { distribution: {}, percentages: [0, 0, 0, 0, 0] };
      const response = await brandReviewService.getRatingDistribution(brandId);
      return response;
    },
    enabled: !!brandId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

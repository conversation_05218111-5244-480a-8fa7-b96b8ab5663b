import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

export default function FollowingScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>我的关注</Text>
      <Text style={styles.subtitle}>暂无关注用户</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingTop: 100,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
});

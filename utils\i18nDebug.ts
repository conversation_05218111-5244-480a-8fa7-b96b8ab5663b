import i18n from "@/locales/i18n";

/**
 * 输出当前加载的所有翻译资源到控制台
 */
export const logI18nResources = () => {
	if (process.env.NODE_ENV !== "development") return;

	const currentLang = i18n.language;
	const resources = i18n.getDataByLanguage(currentLang);

	console.group(`当前语言 (${currentLang}) 的翻译资源:`);
	console.log(resources);

	// 检查特定路径是否存在
	const checkPath = (path: string) => {
		const parts = path.split(".");
		let current: any = resources?.translation;

		for (const part of parts) {
			if (!current || typeof current !== "object") {
				console.log(`路径 "${path}" 不存在 - 在 "${part}" 处中断`);
				return false;
			}
			current = current[part];
		}

		console.log(`路径 "${path}" ${current ? "存在" : "不存在"}, 值:`, current);
		return !!current;
	};

	// 检查一些关键路径
	checkPath("sys.menu.ingredients");
	checkPath("ingredients");

	console.groupEnd();
};

// 导出一个在应用启动后调用的函数
export const debugI18n = () => {
	// 延迟执行，确保i18n已完全初始化
	setTimeout(() => {
		logI18nResources();
	}, 1000);
};

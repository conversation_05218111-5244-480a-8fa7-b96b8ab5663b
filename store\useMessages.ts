import {
  CreateMessageRequest,
  MessageData,
  messageService
} from '@/api/services/messageService';
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import useMessageStore from './messageStore';

// 获取会话列表
export const useConversations = (userId: string, enabled: boolean = true) => {
  const { actions } = useMessageStore();
  
  const result = useInfiniteQuery({
    queryKey: ['conversations', userId],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await messageService.getConversations(userId, pageParam as number, 20);
      return {
        data: response,
        currentPage: response.currentPage,
        totalPages: response.totalPages
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage?.data) return undefined;
      const { currentPage, totalPages } = lastPage.data;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    enabled: enabled && !!userId,
    staleTime: 1000 * 60 * 5, // 5分钟
  });

  // 当数据更新时同步到 store
  useEffect(() => {
    if (result.data) {
      const allConversations = result.data.pages.flatMap(page => page.data.items);
      actions.setConversations(allConversations);
    }
  }, [result.data, actions]);

  return result;
};

// 获取消息列表
export const useMessages = (conversationId: string, userId: string, enabled: boolean = true) => {
  const { actions } = useMessageStore();
  
  const result = useInfiniteQuery({
    queryKey: ['messages', conversationId, userId],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await messageService.getMessages(conversationId, userId, pageParam as number, 50);
      return {
        data: response,
        currentPage: response.currentPage,
        totalPages: response.totalPages
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage?.data) return undefined;
      const { currentPage, totalPages } = lastPage.data;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    enabled: enabled && !!conversationId && !!userId,
    staleTime: 1000 * 30, // 30秒
  });

  // 当数据更新时同步到 store
  useEffect(() => {
    if (result.data && conversationId) {
      const allMessages = result.data.pages.flatMap(page => page.data.items);
      actions.setMessages(conversationId, allMessages);
    }
  }, [result.data, conversationId, actions]);

  return result;
};

// 获取未读消息统计
export const useUnreadCount = (userId: string, enabled: boolean = true) => {
  const { actions } = useMessageStore();
  
  const result = useQuery({
    queryKey: ['unreadCount', userId],
    queryFn: () => messageService.getUnreadCount(userId),
    enabled: enabled && !!userId,
    refetchInterval: 1000 * 30, // 每30秒刷新一次
    staleTime: 1000 * 15, // 15秒
  });

  // 当数据更新时同步到 store
  useEffect(() => {
    if (result.data && typeof result.data.total_unread === 'number') {
      actions.setUnreadCount(result.data.total_unread);
    }
  }, [result.data, actions]);

  return result;
};

// 发送消息
export const useSendMessage = () => {
  const queryClient = useQueryClient();
  const { actions } = useMessageStore();

  return useMutation({
    mutationFn: (messageRequest: CreateMessageRequest) => messageService.sendMessage(messageRequest),
    onSuccess: (data, variables) => {
      const { senderId, receiver_id } = variables;
      
      // 更新 store 中的消息
      if (data && data.conversation_id) {
        actions.addMessage(data.conversation_id, data);
      }
      
      // 更新会话列表
      queryClient.invalidateQueries({ queryKey: ['conversations', senderId] });
      queryClient.invalidateQueries({ queryKey: ['conversations', receiver_id] });
      
      // 更新消息列表
      queryClient.invalidateQueries({ queryKey: ['messages'] });
      
      // 更新未读计数
      queryClient.invalidateQueries({ queryKey: ['unreadCount', receiver_id] });
    },
    onError: (error) => {
      console.error('Send message error:', error);
    },
  });
};

// 标记会话为已读
export const useMarkAsRead = () => {
  const queryClient = useQueryClient();
  const { actions } = useMessageStore();

  return useMutation({
    mutationFn: ({ conversationId, userId }: { conversationId: string; userId: string }) =>
      messageService.markConversationAsRead(conversationId, userId),
    onSuccess: (_, variables) => {
      const { userId, conversationId } = variables;
      
      // 更新 store 中的会话状态
      actions.markConversationAsRead(conversationId);
      
      // 更新会话列表
      queryClient.invalidateQueries({ queryKey: ['conversations', userId] });
      
      // 更新消息列表
      queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
      
      // 更新未读计数
      queryClient.invalidateQueries({ queryKey: ['unreadCount', userId] });
    },
  });
};

// 删除消息
export const useDeleteMessage = () => {
  const queryClient = useQueryClient();
  const { actions } = useMessageStore();

  return useMutation({
    mutationFn: ({ messageId, userId }: { messageId: string; userId: string }) =>
      messageService.deleteMessage(messageId, userId),
    onSuccess: (_, variables) => {
      const { messageId } = variables;
      
      // 从 store 中移除消息
      const { conversations } = useMessageStore.getState();
      conversations.forEach(conv => {
        actions.removeMessage(conv.conversation_id, messageId);
      });
      
      // 更新消息列表
      queryClient.invalidateQueries({ queryKey: ['messages'] });
      
      // 更新会话列表
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    },
    onError: (error) => {
      console.error('Delete message error:', error);
    },
  });
};

// 搜索消息
export const useSearchMessages = (userId: string, searchTerm: string, enabled: boolean = false) => {
  return useQuery({
    queryKey: ['searchMessages', userId, searchTerm],
    queryFn: () => messageService.searchMessages(userId, searchTerm),
    enabled: enabled && !!userId && !!searchTerm && searchTerm.trim().length > 0,
    staleTime: 1000 * 60 * 2, // 2分钟
  });
};

// 获取或创建会话
export const useCreateConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId1, userId2 }: { userId1: string; userId2: string }) =>
      messageService.getOrCreateConversation(userId1, userId2),
    onSuccess: (_, variables) => {
      const { userId1, userId2 } = variables;
      
      // 更新会话列表
      queryClient.invalidateQueries({ queryKey: ['conversations', userId1] });
      queryClient.invalidateQueries({ queryKey: ['conversations', userId2] });
    },
    onError: (error) => {
      console.error('Create conversation error:', error);
    },
  });
};

// 消息管理Hook - 类似于品牌评论的管理Hook
export function useMessageAdmin() {
  const queryClient = useQueryClient();
  const { actions } = useMessageStore();

  // 更新消息状态
  const updateMessageStatusMutation = useMutation({
    mutationFn: ({ messageId, userId }: { messageId: string; userId: string }) =>
      messageService.deleteMessage(messageId, userId),
    onSuccess: (_, variables) => {
      const { messageId } = variables;
      
      // 从 store 中移除消息
      const { conversations } = useMessageStore.getState();
      conversations.forEach(conv => {
        actions.removeMessage(conv.conversation_id, messageId);
      });
      
      queryClient.invalidateQueries({ queryKey: ['messages'] });
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    },
  });

  // 发送消息
  const sendMessageMutation = useMutation({
    mutationFn: (messageRequest: CreateMessageRequest) => messageService.sendMessage(messageRequest),
    onSuccess: (data, variables) => {
      const { senderId, receiver_id } = variables;
      
      // 更新 store 中的消息
      if (data && data.conversation_id) {
        actions.addMessage(data.conversation_id, data);
      }
      
      queryClient.invalidateQueries({ queryKey: ['conversations', senderId] });
      queryClient.invalidateQueries({ queryKey: ['conversations', receiver_id] });
      queryClient.invalidateQueries({ queryKey: ['messages'] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount', receiver_id] });
    },
  });

  // 标记为已读
  const markAsReadMutation = useMutation({
    mutationFn: ({ conversationId, userId }: { conversationId: string; userId: string }) =>
      messageService.markConversationAsRead(conversationId, userId),
    onSuccess: (_, variables) => {
      const { userId, conversationId } = variables;
      
      // 更新 store 中的会话状态
      actions.markConversationAsRead(conversationId);
      
      queryClient.invalidateQueries({ queryKey: ['conversations', userId] });
      queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount', userId] });
    },
  });

  return {
    updateMessageStatus: updateMessageStatusMutation.mutate,
    sendMessage: sendMessageMutation.mutate,
    markAsRead: markAsReadMutation.mutate,
    isUpdatingStatus: updateMessageStatusMutation.isPending,
    isSending: sendMessageMutation.isPending,
    isMarkingAsRead: markAsReadMutation.isPending,
  };
}

// 高级消息管理Hook - 类似于品牌评论的详细管理
export function useConversationDetail(conversationId: string, userId: string) {
  const [currentPage, setCurrentPage] = useState(1);
  const [messages, setMessages] = useState<MessageData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const { actions } = useMessageStore();

  const fetchMessages = useCallback(async (page = 1, reset = false) => {
    if (!conversationId || !userId) return;
    
    setIsLoading(true);
    try {
      const response = await messageService.getMessages(conversationId, userId, page, 50);
      const newMessages = response.items;
      
      if (reset) {
        setMessages(newMessages);
        actions.setMessages(conversationId, newMessages);
      } else {
        setMessages(prev => [...prev, ...newMessages]);
        actions.addMessages(conversationId, newMessages);
      }
      
      setHasMore(response.currentPage < response.totalPages);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setIsLoading(false);
    }
  }, [conversationId, userId, actions]);

  const loadMoreMessages = useCallback(() => {
    if (hasMore && !isLoading) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchMessages(nextPage);
    }
  }, [hasMore, isLoading, currentPage, fetchMessages]);

  const refreshMessages = useCallback(() => {
    setCurrentPage(1);
    fetchMessages(1, true);
  }, [fetchMessages]);

  return {
    messages,
    isLoading,
    hasMore,
    loadMoreMessages,
    refreshMessages,
    fetchMessages,
  };
}

// 导出store以便在组件中使用
export { useMessageStore };
export default useMessageStore;

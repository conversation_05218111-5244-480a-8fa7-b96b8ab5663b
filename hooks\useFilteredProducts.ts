import { useInfiniteQuery } from '@tanstack/react-query';
import { FilterState } from '@/types/pet-food-filter';
import { SearchProduct } from '@/types/search-type';
import apiClient from '@/api/apiClient';

interface FilteredProductsResponse {
  success: boolean;
  products: SearchProduct[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

interface FilteredProductsParams {
  // 基础搜索
  q?: string;
  brands?: string[];
  productTypes?: string[];
  
  // 营养成分筛选
  proteinMin?: number;
  proteinMax?: number;
  fatMin?: number;
  fatMax?: number;
  carbsMin?: number;
  carbsMax?: number;
  caloriesMin?: number;
  caloriesMax?: number;
  
  // 成分质量筛选
  minQualityIngredients?: number;
  maxQuestionableIngredients?: number;
  excludeAllergens?: string[];
  
  // 评价筛选
  minRating?: number;
  minReviewCount?: number;
  
  // 排序和分页
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// 将筛选状态转换为API参数
const buildFilterParams = (filterState: FilterState): FilteredProductsParams => {
  const { filters, sortBy, sortOrder, limit } = filterState;
  const params: FilteredProductsParams = {
    sortBy,
    sortOrder,
    limit,
  };

  // 基础筛选
  if (filters.searchQuery) {
    params.q = filters.searchQuery;
  }
  
  if (filters.brands.length > 0) {
    params.brands = filters.brands;
  }
  
  if (filters.productTypes.length > 0) {
    params.productTypes = filters.productTypes;
  }

  // 营养成分筛选
  if (filters.protein) {
    params.proteinMin = filters.protein.min;
    params.proteinMax = filters.protein.max;
  }
  
  if (filters.fat) {
    params.fatMin = filters.fat.min;
    params.fatMax = filters.fat.max;
  }
  
  if (filters.carbs) {
    params.carbsMin = filters.carbs.min;
    params.carbsMax = filters.carbs.max;
  }
  
  if (filters.calories) {
    params.caloriesMin = filters.calories.min;
    params.caloriesMax = filters.calories.max;
  }

  // 成分质量筛选
  if (filters.minQualityIngredients !== undefined) {
    params.minQualityIngredients = filters.minQualityIngredients;
  }
  
  if (filters.maxQuestionableIngredients !== undefined) {
    params.maxQuestionableIngredients = filters.maxQuestionableIngredients;
  }
  
  if (filters.excludeAllergens.length > 0) {
    params.excludeAllergens = filters.excludeAllergens;
  }

  // 评价筛选
  if (filters.minRating !== undefined) {
    params.minRating = filters.minRating;
  }
  
  if (filters.minReviewCount !== undefined) {
    params.minReviewCount = filters.minReviewCount;
  }

  return params;
};

export const useFilteredProducts = (filterState: FilterState) => {
  const query = useInfiniteQuery({
    queryKey: ['filteredProducts', filterState],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        const params = buildFilterParams(filterState);
        params.page = pageParam;

        const response = await apiClient.get<FilteredProductsResponse>({
          url: '/search/products/filtered',
          params,
        });

        return {
          products: response.products || [],
          total: response.totalCount || 0,
          page: response.currentPage || pageParam,
          pages: response.totalPages || 0,
        };
      } catch (error) {
        console.error('Filtered products fetch error:', error);
        throw error;
      }
    },
    enabled: true, // 始终启用，即使没有筛选条件也显示所有产品
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const nextPage = lastPage.page + 1;
      return nextPage <= lastPage.pages ? nextPage : undefined;
    },
    staleTime: 2 * 60 * 1000, // 2分钟
    retry: 3,
  });

  // 扁平化数据
  const products = query.data?.pages.flatMap(page => page.products) || [];
  const totalCount = query.data?.pages[0]?.total || 0;

  return {
    products,
    totalCount,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: query.fetchNextPage,
    error: query.error,
    refetch: query.refetch,
  };
};

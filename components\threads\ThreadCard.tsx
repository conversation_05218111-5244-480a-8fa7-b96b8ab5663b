import type { Thread } from '@/api/services/threadsService';
import { useUserInfo } from '@/store/userStore';
import { buildImageUrl } from '@/utils/imageUtils';
import { formatTimeAgo } from '@/utils/timeUtils';
import { getAvatarUrl } from '@/utils/userUtils';
import { Ionicons } from '@expo/vector-icons';
import React, { memo, useCallback, useMemo } from 'react';
import { Dimensions, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import OptimizedImage from '../common/OptimizedImage';

const { width } = Dimensions.get('window');

interface ThreadCardProps {
  thread: Thread;
  onPress?: () => void;
  onLike?: () => void;
  onBookmark?: () => void;
  onComment?: () => void;
  onUserPress?: () => void;
  onImagePress?: (images: string[], index: number) => void;
  isLiking?: boolean;
  isBookmarking?: boolean;
  showFullActions?: boolean;
  isMainPost?: boolean;
}

const ThreadCard = memo(function ThreadCard({
  thread,
  onPress,
  onLike,
  onBookmark,
  onComment,
  onUserPress,
  onImagePress,
  isLiking = false,
  isBookmarking = false,
  showFullActions = true,
  isMainPost = false
}: ThreadCardProps) {
  const userInfo = useUserInfo();

  // 使用 useMemo 优化计算
  const isLiked = useMemo(() =>
    thread.liked_by.includes(userInfo.id || ''),
    [thread.liked_by, userInfo.id]
  );

  const isBookmarked = useMemo(() =>
    thread.bookmarked_by.includes(userInfo.id || ''),
    [thread.bookmarked_by, userInfo.id]
  );

  const timeAgo = useMemo(() =>
    formatTimeAgo(thread.created_at),
    [thread.created_at]
  );



  const renderImages = useCallback(() => {
    if (!thread.images || thread.images.length === 0) return null;

    const imageCount = thread.images.length;
    const imageWidth = imageCount === 1 ? width - 80 : (width - 100) / 3;
    const imageHeight = imageCount === 1 ? 200 : 100;

    return (
      <View style={styles.imagesContainer}>
        {thread.images.slice(0, 9).map((image, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.imageWrapper, { width: imageWidth, height: imageHeight }]}
            onPress={() => onImagePress?.(thread.images?.map(img => buildImageUrl(img)) || [], index)}
            activeOpacity={0.9}
          >
            <OptimizedImage
              uri={buildImageUrl(image)}
              style={styles.image}
              resizeMode="cover"
              showLoading={true}
              fallbackIcon="image-outline"
            />
            {imageCount > 9 && index === 8 && (
              <View style={styles.moreImagesOverlay}>
                <Text style={styles.moreImagesText}>+{imageCount - 9}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  }, [thread.images, onImagePress]);

  return (
    <View
      style={[
        styles.container,
        isMainPost && styles.mainPostContainer
      ]}
    >
      <TouchableOpacity
        style={styles.header}
        onPress={onUserPress}
        activeOpacity={0.7}
      >
        <View style={styles.userInfo}>
          <View style={styles.avatarContainer}>
            <Image source={{ uri: getAvatarUrl(thread.user.avatar) }} style={styles.avatar} />
          </View>
          <View style={styles.userDetails}>
            <View style={styles.userNameRow}>
              <Text style={styles.username}>
                {thread.user.username}
              </Text>
            </View>
            <Text style={styles.timeAgo}>
              {timeAgo}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.contentWrapper}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={styles.content}>
          {thread.title && (
            <Text style={styles.title} numberOfLines={isMainPost ? undefined : 2}>
              {thread.title}
            </Text>
          )}
          <Text style={styles.text} numberOfLines={isMainPost ? undefined : 6}>
            {thread.content}
          </Text>
        </View>

        {renderImages()}
      </TouchableOpacity>

      {showFullActions && !isMainPost && (
        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={(e) => {
              e.stopPropagation();
              onLike?.();
            }}
            disabled={isLiking}
          >
            <Ionicons
              name={isLiked ? "heart" : "heart-outline"}
              size={20}
              color={isLiked ? "#ff4757" : "#666"}
            />
            <Text style={[styles.actionText, isLiked && styles.likedText]}>
              {thread.like_count}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={(e) => {
              e.stopPropagation();
              onComment?.();
            }}
          >
            <Ionicons name="chatbubble-outline" size={20} color="#666" />
            <Text style={styles.actionText}>{thread.reply_count}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={(e) => {
              e.stopPropagation();
              onBookmark?.();
            }}
            disabled={isBookmarking}
          >
            <Ionicons
              name={isBookmarked ? "bookmark" : "bookmark-outline"}
              size={20}
              color={isBookmarked ? "#ffa502" : "#666"}
            />
            <Text style={[styles.actionText, isBookmarked && styles.bookmarkedText]}>
              {thread.bookmark_count}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
)

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 6,
    display: 'flex',
  },
  mainPostContainer: {
    justifyContent: 'center',
    borderBottomWidth: 0,
    paddingBottom: 20,
  },
  header: {
    marginBottom: 12,
    paddingHorizontal: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  contentWrapper: {
    flex: 1,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  defaultAvatar: {
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userDetails: {
    flex: 1,
  },
  userNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  username: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  authorBadge: {
    marginLeft: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: '#FF6B6B',
    borderRadius: 8,
  },
  authorText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '500',
  },
  timeAgo: {
    fontSize: 12,
    color: '#999',
  },
  content: {
    marginBottom: 8,
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    lineHeight: 22,
  },
  text: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
    paddingHorizontal: 8,
    marginBottom: 12,
  },
  imageWrapper: {
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  moreImagesOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreImagesText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 0,
    marginTop: 0,
    marginBottom: -7,
    borderTopColor: '#f5f5f5',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  actionText: {
    marginLeft: 6,
    fontSize: 13,
    color: '#666',
  },
  likedText: {
    color: '#ff4757',
  },
  bookmarkedText: {
    color: '#ffa502',
  },
  replyToContainer: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: '#F8F9FA',
    borderRadius: 4,
    marginBottom: 8,
  },
  replyToText: {
    fontSize: 12,
    color: '#6C757D',
    fontStyle: 'italic',
  },
});

export default ThreadCard;

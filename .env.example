# PetFood App 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# API 配置
EXPO_PUBLIC_API_URL=http://172.17.11.242:3000/api

# Google API 密钥 (用于地图等服务)
GOOGLE_API_KEY=your_google_api_key_here

# 应用配置
APP_ENV=development
APP_VERSION=1.0.0

# 数据库配置 (如果需要)
DATABASE_URL=your_database_url_here

# 第三方服务配置
# 推送通知
EXPO_PUSH_TOKEN=your_expo_push_token_here

# 分析服务
ANALYTICS_KEY=your_analytics_key_here

# 错误报告
SENTRY_DSN=your_sentry_dsn_here

# 社交登录 (如果需要)
FACEBOOK_APP_ID=your_facebook_app_id_here
GOOGLE_CLIENT_ID=your_google_client_id_here

# 支付服务 (如果需要)
STRIPE_PUBLISHABLE_KEY=your_stripe_key_here

# 文件存储 (如果需要)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_BUCKET_NAME=your_aws_bucket_name_here

# 其他配置
DEBUG=false
LOG_LEVEL=info

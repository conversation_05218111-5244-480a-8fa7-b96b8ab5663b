import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { 
  PetFoodFilters, 
  NutritionRange,
  PRODUCT_TYPE_OPTIONS,
  COMMON_ALLERGENS,
  NUTRITION_RANGES 
} from '@/types/pet-food-filter';
import NutritionRangeSlider from './NutritionRangeSlider';

interface FilterPanelProps {
  filters: PetFoodFilters;
  onFiltersChange: {
    updateSearchQuery: (query: string) => void;
    updateBrands: (brands: string[]) => void;
    updateProductTypes: (types: string[]) => void;
    updateNutritionRange: (
      nutrient: keyof Pick<PetFoodFilters, 'protein' | 'fat' | 'carbs' | 'calories'>,
      range: NutritionRange | undefined
    ) => void;
    updateQualityFilters: (updates: {
      minQualityIngredients?: number;
      maxQuestionableIngredients?: number;
    }) => void;
    updateAllergens: (allergens: string[]) => void;
    updateRatingFilters: (updates: {
      minRating?: number;
      minReviewCount?: number;
    }) => void;
  };
  availableBrands?: string[];
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFiltersChange,
  availableBrands = [],
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['basic', 'nutrition'])
  );

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const handleProductTypeToggle = (type: string) => {
    const newTypes = filters.productTypes.includes(type)
      ? filters.productTypes.filter(t => t !== type)
      : [...filters.productTypes, type];
    onFiltersChange.updateProductTypes(newTypes);
  };

  const handleBrandToggle = (brand: string) => {
    const newBrands = filters.brands.includes(brand)
      ? filters.brands.filter(b => b !== brand)
      : [...filters.brands, brand];
    onFiltersChange.updateBrands(newBrands);
  };

  const handleAllergenToggle = (allergen: string) => {
    const newAllergens = filters.excludeAllergens.includes(allergen)
      ? filters.excludeAllergens.filter(a => a !== allergen)
      : [...filters.excludeAllergens, allergen];
    onFiltersChange.updateAllergens(newAllergens);
  };

  const renderSection = (
    key: string,
    title: string,
    content: React.ReactNode,
    badge?: number
  ) => {
    const isExpanded = expandedSections.has(key);
    
    return (
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(key)}
        >
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>{title}</Text>
            {badge !== undefined && badge > 0 && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{badge}</Text>
              </View>
            )}
          </View>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color="#666"
          />
        </TouchableOpacity>
        {isExpanded && (
          <View style={styles.sectionContent}>
            {content}
          </View>
        )}
      </View>
    );
  };

  const renderCheckboxGroup = (
    options: { value: string; label: string }[],
    selectedValues: string[],
    onToggle: (value: string) => void
  ) => (
    <View style={styles.checkboxGroup}>
      {options.map((option) => {
        const isSelected = selectedValues.includes(option.value);
        return (
          <TouchableOpacity
            key={option.value}
            style={[styles.checkbox, isSelected && styles.checkboxSelected]}
            onPress={() => onToggle(option.value)}
          >
            <Ionicons
              name={isSelected ? 'checkmark-circle' : 'ellipse-outline'}
              size={20}
              color={isSelected ? '#4d920f' : '#ccc'}
            />
            <Text style={[styles.checkboxText, isSelected && styles.checkboxTextSelected]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* 基础筛选 */}
      {renderSection(
        'basic',
        '基础筛选',
        <View>
          {/* 搜索框 */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>搜索关键词</Text>
            <TextInput
              style={styles.textInput}
              value={filters.searchQuery}
              onChangeText={onFiltersChange.updateSearchQuery}
              placeholder="输入产品名称或品牌"
              clearButtonMode="while-editing"
            />
          </View>


        </View>,
        filters.searchQuery.length
      )}

      {/* 营养成分筛选 */}
      {renderSection(
        'nutrition',
        '营养成分',
        <View>
          <NutritionRangeSlider
            label="蛋白质含量"
            unit="%"
            min={NUTRITION_RANGES.protein.min}
            max={NUTRITION_RANGES.protein.max}
            step={NUTRITION_RANGES.protein.step}
            value={filters.protein}
            onChange={(range) => onFiltersChange.updateNutritionRange('protein', range)}
          />
          <NutritionRangeSlider
            label="脂肪含量"
            unit="%"
            min={NUTRITION_RANGES.fat.min}
            max={NUTRITION_RANGES.fat.max}
            step={NUTRITION_RANGES.fat.step}
            value={filters.fat}
            onChange={(range) => onFiltersChange.updateNutritionRange('fat', range)}
          />
          <NutritionRangeSlider
            label="碳水化合物"
            unit="%"
            min={NUTRITION_RANGES.carbs.min}
            max={NUTRITION_RANGES.carbs.max}
            step={NUTRITION_RANGES.carbs.step}
            value={filters.carbs}
            onChange={(range) => onFiltersChange.updateNutritionRange('carbs', range)}
          />
          <NutritionRangeSlider
            label="卡路里"
            unit=" kcal"
            min={NUTRITION_RANGES.calories.min}
            max={NUTRITION_RANGES.calories.max}
            step={NUTRITION_RANGES.calories.step}
            value={filters.calories}
            onChange={(range) => onFiltersChange.updateNutritionRange('calories', range)}
          />
        </View>,
        [filters.protein, filters.fat, filters.carbs, filters.calories].filter(Boolean).length
      )}

      {/* 成分质量筛选 */}
      {renderSection(
        'quality',
        '成分质量',
        <View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>最少优质成分数量</Text>
            <TextInput
              style={styles.textInput}
              value={filters.minQualityIngredients?.toString() || ''}
              onChangeText={(text) => {
                const value = parseInt(text) || undefined;
                onFiltersChange.updateQualityFilters({ minQualityIngredients: value });
              }}
              placeholder="例如：5"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>最多可疑成分数量</Text>
            <TextInput
              style={styles.textInput}
              value={filters.maxQuestionableIngredients?.toString() || ''}
              onChangeText={(text) => {
                const value = parseInt(text) || undefined;
                onFiltersChange.updateQualityFilters({ maxQuestionableIngredients: value });
              }}
              placeholder="例如：2"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>排除过敏原</Text>
            {renderCheckboxGroup(
              COMMON_ALLERGENS,
              filters.excludeAllergens,
              handleAllergenToggle
            )}
          </View>
        </View>,
        (filters.minQualityIngredients ? 1 : 0) +
        (filters.maxQuestionableIngredients ? 1 : 0) +
        filters.excludeAllergens.length
      )}

      {/* 评价筛选 */}
      {renderSection(
        'rating',
        '评价筛选',
        <View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>最低评分</Text>
            <TextInput
              style={styles.textInput}
              value={filters.minRating?.toString() || ''}
              onChangeText={(text) => {
                const value = parseFloat(text) || undefined;
                onFiltersChange.updateRatingFilters({ minRating: value });
              }}
              placeholder="例如：4.0"
              keyboardType="decimal-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>最少评论数</Text>
            <TextInput
              style={styles.textInput}
              value={filters.minReviewCount?.toString() || ''}
              onChangeText={(text) => {
                const value = parseInt(text) || undefined;
                onFiltersChange.updateRatingFilters({ minReviewCount: value });
              }}
              placeholder="例如：10"
              keyboardType="numeric"
            />
          </View>
        </View>,
        (filters.minRating ? 1 : 0) + (filters.minReviewCount ? 1 : 0)
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  badge: {
    backgroundColor: '#4d920f',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  badgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  sectionContent: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  checkboxGroup: {
    gap: 12,
  },
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkboxSelected: {},
  checkboxText: {
    fontSize: 14,
    color: '#666',
  },
  checkboxTextSelected: {
    color: '#4d920f',
    fontWeight: '500',
  },
});

export default FilterPanel;

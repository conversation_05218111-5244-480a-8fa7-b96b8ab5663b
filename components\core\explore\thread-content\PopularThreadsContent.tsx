import { Thread } from "@/api/services/threadsService";
import { usePopularThreads } from "@/store/threadsStore";
import { router } from "expo-router";
import React, { useMemo } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

interface ThreadItemProps {
  thread: Thread;
  onPress: () => void;
}

function ThreadItem({ thread, onPress }: ThreadItemProps) {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}分钟前`;
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}天前`;
    }
  };

  const getThreadTypeIcon = () => {
    if (thread.images && thread.images.length > 0) {
      return "image-outline";
    }
    return "forum";
  };

  const getThreadTypeColor = () => {
    if (thread.images && thread.images.length > 0) {
      return "#FF9800";
    }
    return "#2196F3";
  };

  const getThreadTypeLabel = () => {
    if (thread.images && thread.images.length > 0) {
      return "图文";
    }
    return "讨论";
  };

  return (
    <TouchableOpacity style={styles.threadItem} onPress={onPress}>
      <View style={styles.threadHeader}>
        <View style={styles.threadTypeContainer}>
          <Icon 
            name={getThreadTypeIcon()} 
            size={14} 
            color={getThreadTypeColor()} 
            style={styles.threadTypeIcon}
          />
          <Text style={[styles.threadType, { color: getThreadTypeColor() }]}>
            {getThreadTypeLabel()}
          </Text>
        </View>
        <Text style={styles.threadTitle} numberOfLines={1}>
          {thread.title}
        </Text>
      </View>
      
      <View style={styles.threadFooter}>
        <View style={styles.userInfo}>
          <Text style={styles.username}>{thread.user.username}</Text>
        </View>
        
        <View style={styles.threadStats}>
          <View style={styles.statItem}>
            <Icon name="message-reply-outline" size={12} color="#666" />
            <Text style={styles.statText}>{thread.reply_count}</Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="heart-outline" size={12} color="#666" />
            <Text style={styles.statText}>{thread.like_count}</Text>
          </View>
          <Text style={styles.timeText}>{formatTime(thread.created_at)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

export default function PopularThreadsContent() {
  const { data: popularThreads, isLoading, error } = usePopularThreads();

  const displayedThreads = useMemo(() => {
    return popularThreads.slice(0, 20); 
  }, [popularThreads]);


  const handleThreadPress = (thread: Thread) => {
    router.push(`/thread-detail?threadId=${thread.thread_id}`);
  };

  if (isLoading) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (error || popularThreads.length === 0) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <Icon
          name="alert-circle-outline"
          size={60}
          color="#e74c3c"
          style={styles.errorIcon}
        />
        <Text style={styles.errorText}>无法加载热门帖子</Text>
        <Text style={styles.errorSubtext}>请检查网络连接或稍后再试</Text>
      </View>
    );
  }

  return (
    <View style={styles.contentContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>热门讨论</Text>
      </View>

      <View style={styles.threadsList}>
        <FlatList
          data={displayedThreads}
          renderItem={({ item }) => (
            <ThreadItem
              thread={item}
              onPress={() => handleThreadPress(item)}
            />
          )}
          keyExtractor={(item) => item.thread_id}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>暂无热门帖子</Text>
            </View>
          }
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    marginVertical: 8,
    borderRadius: 12,
    padding: 3,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
    minHeight: 200,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal:10,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  viewAllLink: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    color: "#666",
    marginRight: 4,
  },
  threadsList: {
    borderRadius: 8,
    padding: 8,
  },
  threadItem: {
    borderRadius: 6,
    padding: 8,
    marginVertical: 1,
  },
  threadHeader: {
    marginBottom: 6,
  },
  threadTypeContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 3,
  },
  threadTypeIcon: {
    marginRight: 3,
  },
  threadType: {
    fontSize: 11,
    fontWeight: "500",
  },
  threadTitle: {
    fontSize: 13,
    fontWeight: "500",
    color: "#333",
    lineHeight: 18,
  },
  threadFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  userInfo: {
    flex: 1,
  },
  username: {
    fontSize: 12,
    color: "#666",
  },
  threadStats: {
    flexDirection: "row",
    alignItems: "center",
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 12,
  },
  statText: {
    fontSize: 12,
    color: "#666",
    marginLeft: 2,
  },
  timeText: {
    fontSize: 12,
    color: "#999",
  },
  separator: {
    height: 0.5,
    backgroundColor: "#e0e0e0",
    marginVertical: 2,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
  },
  errorIcon: {
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: "#e74c3c",
    fontWeight: "500",
    marginBottom: 4,
  },
  errorSubtext: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
  },
  emptyContainer: {
    alignItems: "center",
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 14,
    color: "#999",
  },
});

import type { Brand, BrandNameRecord, Product, ProductNameRecord } from "@/types/entity";
import { BrandSearchResponse, ProductSearchResponse, ThreadSearchResponse } from "@/types/search-type";
import apiClient from "../apiClient";
// 搜索结果接口
export interface SearchPreviewResponse {
  products: ProductNameRecord[];
  brands: BrandNameRecord[];
}

export interface SearchDetailedResponse {
  products: Product[];
  brands: Brand[];
  threads: Thread[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export interface Thread {
  thread_id: string;
  title?: string;
  content: string;
  user_id: string;
  like_count: number;
  reply_count: number;
  created_at: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
  };
}

export interface SearchFilters {
  category?: 'all' | 'dog' | 'cat' | 'prescription' | 'natural';
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'rating' | 'sales';
  priceRange?: {
    min?: number;
    max?: number;
  };
}

export enum SearchApi {
  Search = "search",
}

// 实时搜索预览 - 用于renderSearchPreview
const searchPreview = async (query: string) => {
  try {
    if (!query.trim()) {
      return { products: [], brands: [] };
    }

    const response = await apiClient.get<SearchPreviewResponse>({
      url: `${SearchApi.Search}/preview`,
      params: { q: query.trim() }
    });
    return response;
  } catch (error) {
    console.error("Search preview API error:", error);
    throw error;
  }
};


// 获取热门搜索词
const getHotSearches = async () => {
  try {
    const response = await apiClient.get<string[]>({
      url: `${SearchApi.Search}/hot-searches`
    });
    return response;
  } catch (error) {
    console.error("Hot searches API error:", error);
    // 返回默认热门搜索词
    return [
      '天然粮',
      '幼猫粮', 
      '减肥粮',
      '无谷粮',
      '皇家',
      '希尔斯',
      '冠能'
    ];
  }
};

// 搜索建议 - 自动补全
const getSearchSuggestions = async (query: string) => {
  try {
    if (!query.trim()) {
      return [];
    }

    const response = await apiClient.get<string[]>({
      url: `${SearchApi.Search}/suggestions`,
      params: { q: query.trim() }
    });
    return response;
  } catch (error) {
    console.error("Search suggestions API error:", error);
    return [];
  }
};

// 搜索产品 
const searchProducts = async (query: string, page: number = 1, limit: number = 20) => {
  try {
    if (!query.trim()) {
      return { products: [], totalCount: 0, totalPages: 0, currentPage: page };
    }

    const response = await apiClient.get<ProductSearchResponse>({
      url: `${SearchApi.Search}/products`,
      params: { q: query.trim(), page, limit }
    });
    return {
      products: response.products || [],
      totalCount: response.totalCount || 0,
      totalPages: response.totalPages || 0,
      currentPage: response.currentPage || page
    };
  } catch (error) {
    console.error("Search products API error:", error);
    throw error;
  }
};

// 搜索品牌 
const searchBrands = async (query: string, page: number = 1, limit: number = 20) => {
  try {
    if (!query.trim()) {
      return { brands: [], totalCount: 0, totalPages: 0, currentPage: page };
    }

    const response = await apiClient.get<BrandSearchResponse>({
      url: `${SearchApi.Search}/brands`,
      params: { q: query.trim(), page, limit }
    });
    return {
      brands: response.brands || [],
      totalCount: response.totalCount || 0,
      totalPages: response.totalPages || 0,
      currentPage: response.currentPage || page
    };
  } catch (error) {
    console.error("Search brands API error:", error);
    throw error;
  }
};

// 搜索帖子 
const searchThreads = async (query: string, page: number = 1, limit: number = 20) => {
  try {
    if (!query.trim()) {
      return { threads: [], totalCount: 0, totalPages: 0, currentPage: page };
    }

    const response = await apiClient.get<ThreadSearchResponse>({
      url: `${SearchApi.Search}/threads`,
      params: { q: query.trim(), page, limit }
    });
    return {
      threads: response.threads || [],
      totalCount: response.totalCount || 0,
      totalPages: response.totalPages || 0,
      currentPage: response.currentPage || page
    };
  } catch (error) {
    console.error("Search threads API error:", error);
    throw error;
  }
};

export const searchService = {
  searchPreview,
  searchProducts,
  searchBrands,
  searchThreads,
  getHotSearches,
  getSearchSuggestions,
};

import { useUserInfo, useUserToken } from '@/store/userStore';
import { useRouter, useSegments } from 'expo-router';
import React, { useEffect, useState } from 'react';

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * 认证保护组件
 * 确保用户必须登录后才能访问应用的任何界面
 */
export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const router = useRouter();
  const segments = useSegments();
  const userInfo = useUserInfo();
  const userToken = useUserToken();
  const [isLoading, setIsLoading] = useState(true);

  // 检查用户是否已登录
  const isAuthenticated = !!(userToken.accessToken && userInfo.id);

  // 检查当前路由是否在认证路由组中
  const inAuthGroup = segments[0] === '(auth)';

  useEffect(() => {
    // 给持久化存储一点时间加载，但减少延迟因为 Expo SplashScreen 已经处理了初始加载
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isLoading) return;

    if (!isAuthenticated && !inAuthGroup) {
      // 用户未登录且不在认证页面，重定向到登录页
      router.replace('/(auth)/login');
    } else if (isAuthenticated && inAuthGroup) {
      // 用户已登录但在认证页面，重定向到主页
      router.replace('/(stack)/(tabs)');
    }
  }, [isAuthenticated, inAuthGroup, isLoading, router, segments, userToken.accessToken, userInfo.id]);

  // 显示加载状态 - 返回 null 让 Expo SplashScreen 继续显示
  if (isLoading) {
    return null;
  }

  // 如果用户未登录且不在认证页面，返回 null 等待重定向
  if (!isAuthenticated && !inAuthGroup) {
    return null;
  }

  // 如果用户已登录但在认证页面，返回 null 等待重定向
  if (isAuthenticated && inAuthGroup) {
    return null;
  }

  // 正常渲染子组件
  return <>{children}</>;
};


export default AuthGuard;

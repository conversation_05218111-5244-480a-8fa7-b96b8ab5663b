import type { BasicStatus, PermissionType } from "./enum";

export interface UserToken {
	accessToken?: string;
	refreshToken?: string;
}

export interface UserInfo {
	id: string;
	username: string;
	email: string;
	password?: string;
	avatar?: string;
	backgroundImage?: string;
	bio?: string;
	gender?: 'male' | 'female' | 'other';
	birthday?: string; // YYYY-MM-DD format
	location?: string;
	occupation?: string;
	emailVerified?: boolean;
	emailVerifiedAt?: Date;
	createdAt?: Date;
	updatedAt?: Date;
}

export interface Brand {
	_id: string;
	name: string;
	created_at: string;
	logo_url: string;
	updated_at: string;
	website_url: string;
	desc?: string; // Add missing description field
}

export interface ProductNameRecord {
	_id:string;
	name:string;
}

export interface BrandNameRecord {
	_id:string;
	name:string;
}

export interface Product {
	_id: string;
	name: string;
	brand: string;
	product_type: string;
	image_url: string;
	product_url: string;
	created_at: string;
	updated_at: string;
	quality_ingredients: string[];
	questionable_ingredients: string[];
	allergen_ingredients: string[];
	est_calories: string;
	guaranteed_analysis: Record<string, any>;
	dry_matter_analysis: Record<string, any>;
	__v?: number;
}

export interface Organization {
	id: string;
	name: string;
	status: "enable" | "disable";
	desc?: string;
	order?: number;
	children?: Organization[];
}

export interface Permission {
	id: string;
	parentId: string;
	name: string;
	label: string;
	type: PermissionType;
	route: string;
	status?: BasicStatus;
	order?: number;
	icon?: string;
	component?: string;
	hide?: boolean;
	hideTab?: boolean;
	frameSrc?: URL;
	newFeature?: boolean;
	children?: Permission[];
}

export interface Role {
	_id?: string;
	id: string;
	name: string;
	label: string;
	status: BasicStatus;
	order?: number;
	desc?: string;
	permission?: Permission[] | string[];
}

export type SearchResult = {
    SearchProducts: SearchProduct[];
    SearchBrands: SearchBrand[];
    SearchThreads: SearchThreads[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
}

export type SearchProduct = {
    id: string;
    name: string;
    brand: string;
    image_url: string;
    product_type: string;
    rating: number;
    review_count: number;
}

export type SearchBrand = {
    id: string;
    name: string;
    image_url: string;
    product_count: number;
}

export type SearchThreads = {
    thread_id: string;
    title?: string;
    content: string;
    user_id: string;
    like_count: number;
    reply_count: number;
    created_at: string;
    user: {
        id: string;
        username: string;
        avatar?: string;
    };
}

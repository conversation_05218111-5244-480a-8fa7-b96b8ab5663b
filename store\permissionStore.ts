import permissionService from "@/api/services/permissionService";
import type { Permission } from "@/types/entity";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { create } from "zustand";

type PermissionStore = {
	permissionList: Permission[];
	actions: {
		setPermissionList: (permissions: Permission[]) => void;
		clearPermissionList: () => void;
	};
};

const usePermissionStore = create<PermissionStore>()((set) => ({
	permissionList: [],
	actions: {
		setPermissionList: (permissions) => {
			set({
				permissionList: permissions,
			});
		},
		clearPermissionList: () => {
			set({
				permissionList: [],
			});
		},
	},
}));

export const usePermissionList = () => usePermissionStore((state) => state.permissionList);
export const usePermissionAction = () => usePermissionStore((state) => state.actions);

export const usePermissions = () => {
	const { setPermissionList } = usePermissionAction();
	const queryClient = useQueryClient();

	const query = useQuery<Permission[]>({
		queryKey: ["permissions"],
		queryFn: async () => {
			try {
				const response = await permissionService.getPermissionTree();

				if (!Array.isArray(response)) {
					throw new Error("Invalid response format: expected array of permissions");
				}

				setPermissionList(response);

				return response;
			} catch (error) {
				console.error("Permission fetch error:", error);
				throw error;
			}
		},
		placeholderData: (previousData) => previousData,
		staleTime: 10 * 100,
	});

	const createMutation = useMutation({
		mutationFn: (permissionData: Partial<Permission>) => permissionService.createPermission(permissionData),
		onSuccess: () => {
			toast.success("权限路由创建成功");
			queryClient.invalidateQueries({ queryKey: ["permissions"] });
		},
		onError: (error: any) => {
			toast.error(error.message || "权限路由创建失败");
		},
	});

	const updateMutation = useMutation({
		mutationFn: ({ id, data }: { id: string; data: Partial<Permission> }) =>
			permissionService.updatePermission(id, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["permissions"] });
		},
	});

	const deleteMutation = useMutation({
		mutationFn: (id: string) => permissionService.deletePermission(id),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["permissions"] });
		},
	});

	return {
		data: query.data || [],
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		createPermission: createMutation.mutate,
		updatePermission: updateMutation.mutate,
		deletePermission: deleteMutation.mutate,
		isCreating: createMutation.isPending,
		isUpdating: updateMutation.isPending,
		isDeleting: deleteMutation.isPending,
	};
};

export default usePermissionStore;

import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Keyboard,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface Product {
  _id: string;
  name: string;
  brand?: string;
  image?: string;
  category?: string;
  rating?: number | string;
  review_count?: number;
}

interface Brand {
  _id: string;
  name: string;
  products?: number;
  image?: string;
}

interface SearchResults {
  products: Product[];
  brands: Brand[];
}

interface SearchPreviewProps {
  searchResults: SearchResults;
  isSearching: boolean;
  onProductPress: (productId: string) => void;
  onBrandPress: (brandId: string) => void;
  onSearchItem?: (query: string) => void;
}

const SearchPreview: React.FC<SearchPreviewProps> = ({
  searchResults,
  isSearching,
  onProductPress,
  onBrandPress,
  onSearchItem,
}) => {
  if (isSearching) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>搜索中...</Text>
      </View>
    );
  }

  if (searchResults.products.length === 0 && searchResults.brands.length === 0) {
    return (
      <View style={styles.noResultsContainer}>
        <View style={styles.noResultsIcon}>
          <Ionicons name="search-outline" size={32} color="#ccc" />
        </View>
        <Text style={styles.noResultsText}>暂无相关结果</Text>
        <Text style={styles.noResultsSubtext}>试试其他关键词</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.scrollView}
      contentContainerStyle={styles.searchResultsContainer}
      onScrollBeginDrag={() => Keyboard.dismiss()}
      keyboardShouldPersistTaps="handled"
    >
      {/* 品牌列表 */}
      {searchResults.brands.length > 0 && (
        <View style={styles.previewSection}>
          <Text style={styles.previewSectionTitle}>品牌</Text>
          {searchResults.brands.map((item) => (
            <TouchableOpacity
              key={item._id}
              style={styles.previewItem}
              onPress={() => {
                onBrandPress(item._id);
                onSearchItem?.(item.name);
              }}
            >
              <Ionicons name='search-outline' size={18} color="#666" style={[styles.searchIcon, { marginRight: 12 }]} />
              <Text style={styles.previewItemText}>{item.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* 产品列表 */}
      {searchResults.products.length > 0 && (
        <View style={[styles.previewSection, { marginTop: searchResults.brands.length > 0 ? 24 : 0 }]}>
          <Text style={styles.previewSectionTitle}>产品</Text>
          {searchResults.products.map((item) => (
            <TouchableOpacity
              key={item._id}
              style={styles.previewItem}
              onPress={() => {
                onProductPress(item._id);
                onSearchItem?.(item.name);
              }}
            >
              <Ionicons name='search-outline' size={18} color="#666" style={[styles.searchIcon, { marginRight: 12 }]} />
              <Text style={styles.previewItemText}>{item.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  searchResultsContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  noResultsIcon: {
    marginBottom: 16,
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  previewSection: {
    marginBottom: 16,
  },
  previewSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  previewItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
  },
  searchIcon: {
    marginRight: 8,
  },
  previewItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
});

export default SearchPreview;

import type { UserInfo, UserToken } from "@/types/entity";
import apiClient from "../apiClient";

export interface SignInReq {
	email: string;
	password: string;
}

export interface SignUpReq {
	username: string;
	email: string;
	password: string;
}

export interface VerifyEmailReq {
	email: string;
	verificationCode: string;
}

export interface ResendVerificationReq {
	email: string;
}

export interface VerifyTokenReq {
	token: string;
}

export interface UploadImageReq {
	image: any;
	type: 'avatar' | 'background';
}

export interface UploadImageRes {

	imageUrl: string;

}

export type SignInRes = UserToken & { user: UserInfo };

export interface UserRes {
	success: boolean;
	status?: number;
	message?: string;
	totalCount?: number;
	totalPages?: number;
	currentPage?: number;
	users?: UserInfo[];
	user?: UserInfo;
}

export enum UserApi {
	SignIn = "/font-auth/signin-email",
	SignUp = "/font-auth/signup",
	VerifyEmail = "/font-auth/verify-email",
	ResendVerification = "/font-auth/resend-verification",
	VerifyToken = "/font-auth/verify-token",
	Users = "/font-user",
	UpdateProfile = "/font-user",
	UploadImage = "/font-user/upload-image",
}

const signin = (data: SignInReq) => apiClient.post<SignInRes>({ url: UserApi.SignIn, data });
const signup = (data: SignUpReq) => apiClient.post<UserInfo>({ url: UserApi.SignUp, data });
const verifyEmail = (data: VerifyEmailReq) => apiClient.post<SignInRes>({ url: UserApi.VerifyEmail, data });
const resendVerification = (data: ResendVerificationReq) => apiClient.post({ url: UserApi.ResendVerification, data });
const verifyToken = (data: VerifyTokenReq) => apiClient.post({ url: UserApi.VerifyToken, data });

const getUsers = async () => {
	try {
		const response = await apiClient.get<UserInfo[]>({
			url: UserApi.Users,
		});
		return response;
	} catch (error) {
		console.error("Users API error:", error);
		throw error;
	}
};

const getUserById = async (id: string) => {
	try {
		const response = await apiClient.get<UserInfo>({
			url: `${UserApi.Users}/${id}`,
		});
		return response;
	} catch (error) {
		console.error("User API error:", error);
		throw error;
	}
};

const createUser = async (data: Partial<UserInfo>) => {
	try {
		const response = await apiClient.post<UserInfo>({
			url: UserApi.Users,
			data,
		});
		return response;
	} catch (error) {
		console.error("User Create API error:", error);
		throw error;
	}
};

const updateUser = async (id: string, data: Partial<UserInfo>) => {
	try {
		const response = await apiClient.put<UserInfo>({
			url: `${UserApi.Users}/${id}`,
			data,
		});
		return response;
	} catch (error) {
		console.error("User Update API error:", error);
		throw error;
	}
};

const deleteUser = async (id: string) => {
	try {
		const response = await apiClient.delete<UserInfo>({
			url: `${UserApi.Users}/${id}`,
		});
		return response;
	} catch (error) {
		console.error("User Delete API error:", error);
		throw error;
	}
};

const initializeUsers = async () => {
	try {
		const response = await apiClient.get({
			url: `${UserApi.Users}/initialize`,
		});
		return response;
	} catch (error) {
		console.error("User Initialize API error:", error);
		throw error;
	}
};

const updateUserProfile = async (id: string, data: Partial<UserInfo>) => {
	try {
		console.log('Updating user profile:', { id, data });
		const response = await apiClient.put<UserInfo>({
			url: `${UserApi.UpdateProfile}/${id}/profile`,
			data,
		});
		console.log('Profile update response:', response);
		return response;
	} catch (error) {
		console.error("User Profile Update API error:", error);
		throw error;
	}
};

const uploadImage = async (data: FormData) => {
	try {
		const response = await apiClient.post<UploadImageRes>({
			url: UserApi.UploadImage,
			data,
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		});
		return response;
	} catch (error) {
		console.error("Image Upload API error:", error);
		throw error;
	}
};

export interface UserBasicInfo {
  id: string;
  username: string;
  avatar?: string;
}

// 添加内存缓存
class UserCache {
  private cache = new Map<string, { data: UserBasicInfo; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  get(userId: string): UserBasicInfo | null {
    const cached = this.cache.get(userId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }
    return null;
  }

  set(userId: string, data: UserBasicInfo): void {
    this.cache.set(userId, {
      data,
      timestamp: Date.now()
    });
  }

  clear(): void {
    this.cache.clear();
  }
}

const userCache = new UserCache();

const getUserBasicInfo = async (userId: string): Promise<UserBasicInfo | null> => {
  // 先检查缓存
  const cached = userCache.get(userId);
  if (cached) {
    console.log(`Using cached user info for ${userId}`);
    return cached;
  }

  try {
    console.log(`Fetching user info for ${userId}`);
    const response = await apiClient.get<UserBasicInfo>({
      url: `/font-user/${userId}/basic`
    });
    
    // 缓存结果
    if (response) {
      userCache.set(userId, response);
    }
    
    return response;
  } catch (error) {
    console.warn(`User ${userId} not found, will render as anonymous user`);
    // 不返回 null，而是返回匿名用户信息
    const anonymousUser = {
      id: userId,
      username: '匿名用户',
      avatar: undefined
    };
    
    // 也缓存匿名用户信息，避免重复请求
    userCache.set(userId, anonymousUser);
    return anonymousUser;
  }
};

const getUsersBasicInfo = async (userIds: string[]): Promise<Record<string, UserBasicInfo>> => {
  try {
    const userMap: Record<string, UserBasicInfo> = {};
    const uncachedIds: string[] = [];
    
    // 先检查缓存
    userIds.forEach(id => {
      const cached = userCache.get(id);
      if (cached) {
        userMap[id] = cached;
      } else {
        uncachedIds.push(id);
      }
    });
    
    // 批量获取未缓存的用户信息
    if (uncachedIds.length > 0) {
      const promises = uncachedIds.map(id => getUserBasicInfo(id));
      const results = await Promise.all(promises);
      
      results.forEach((user, index) => {
        if (user) {
          userMap[uncachedIds[index]] = user;
        }
      });
    }
    
    return userMap;
  } catch (error) {
    console.error('Failed to batch fetch user info:', error);
    return {};
  }
};

// 清除缓存的方法
const clearUserCache = () => {
  userCache.clear();
};

export default {
	signin,
	signup,
	verifyEmail,
	resendVerification,
	verifyToken,
	getUsers,
	getUserById,
	createUser,
	updateUser,
	updateUserProfile,
	deleteUser,
	initializeUsers,
	uploadImage,
	getUserBasicInfo,
	getUsersBasicInfo,
	clearUserCache,
};

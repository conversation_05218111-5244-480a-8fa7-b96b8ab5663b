import { DrawerContentScrollView, DrawerItem } from '@react-navigation/drawer';
import { useRouter } from 'expo-router';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const drawerItems = [
  {
    label: '我的评论',
    icon: 'comment-outline',
    route: 'mycomments',
  },
  {
    label: '浏览历史',
    icon: 'history',
    route: 'history',
  },
  {
    label: '设置',
    icon: 'cog-outline',
    route: '/(stack)/settings',
  },
  {
    label: '帮助与反馈',
    icon: 'help-circle-outline',
    route: 'help',
  },
];

export default function CustomDrawerContent(props: any) {
  const router = useRouter();

  const handleItemPress = (route: string) => {
    props.navigation.closeDrawer();
    router.push(route as any);
  };

  return (
    <DrawerContentScrollView {...props} style={styles.container}>


      {/* Menu Items */}
      <View style={styles.menuSection}>
        {drawerItems.map((item, index) => (
          <DrawerItem
            key={index}
            label={item.label}
            icon={({ color, size }) => (
              <Icon name={item.icon} size={size} color={color} />
            )}
            onPress={() => handleItemPress(item.route)}
            labelStyle={styles.itemLabel}
            style={styles.drawerItem}
            activeBackgroundColor="rgba(0, 122, 255, 0.1)"
            activeTintColor="#007AFF"
            inactiveTintColor="#666"
          />
        ))}
      </View>
    </DrawerContentScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 12,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  divider: {
    height: 1,
    backgroundColor: '#e9ecef',
    marginVertical: 8,
  },
  menuSection: {
    flex: 1,
    paddingTop: 8,
  },
  drawerItem: {
    marginHorizontal: 12,
    marginVertical: 2,
    borderRadius: 8,
  },
  itemLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  footerText: {
    fontSize: 12,
    color: '#999',
  },
});

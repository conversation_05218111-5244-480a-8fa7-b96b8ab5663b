import React from 'react';
import { Dimensions, Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface AgreementModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export default function AgreementModal({ visible, onCancel, onConfirm }: AgreementModalProps) {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>用户协议</Text>
          <Text style={styles.modalText}>
            请先阅读并同意《用户协议》和《隐私政策》后再进行登录
          </Text>
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={onCancel}
            >
              <Text style={styles.modalCancelText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.modalConfirmButton}
              onPress={onConfirm}
            >
              <Text style={styles.modalConfirmText}>同意并登录</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Math.max(20, screenWidth * 0.05),
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: Math.max(24, screenWidth * 0.06),
    width: '90%',
    maxWidth: Math.min(350, screenWidth * 0.9),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 8,
  },
  modalTitle: {
    fontSize: Math.max(18, screenWidth * 0.048),
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: Math.max(16, screenHeight * 0.02),
  },
  modalText: {
    fontSize: Math.max(14, screenWidth * 0.037),
    color: '#666',
    lineHeight: Math.max(20, screenWidth * 0.053),
    textAlign: 'center',
    marginBottom: Math.max(24, screenHeight * 0.03),
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    paddingVertical: Math.max(12, screenHeight * 0.015),
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
    minHeight: Math.max(44, screenHeight * 0.055),
    justifyContent: 'center',
  },
  modalCancelText: {
    color: '#666',
    fontSize: Math.max(15, screenWidth * 0.04),
  },
  modalConfirmButton: {
    flex: 1,
    paddingVertical: Math.max(12, screenHeight * 0.015),
    borderRadius: 8,
    backgroundColor: '#3498db',
    alignItems: 'center',
    minHeight: Math.max(44, screenHeight * 0.055),
    justifyContent: 'center',
    shadowColor: '#3498db',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalConfirmText: {
    color: '#fff',
    fontSize: Math.max(15, screenWidth * 0.04),
    fontWeight: '600',
  },
});

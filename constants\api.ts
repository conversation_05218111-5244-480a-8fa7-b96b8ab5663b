import { Platform } from 'react-native';
import Constants from 'expo-constants';

// 从环境变量获取 API URL
const getApiBaseUrl = () => {
  // 1. 优先使用 .env 文件中的 EXPO_PUBLIC_API_URL
  const envApiUrl = process.env.EXPO_PUBLIC_API_URL;

  if (!envApiUrl) {
    console.error('❌ EXPO_PUBLIC_API_URL 未设置！');
    return 'http://localhost:3000/api'; // 默认值
  }

  // 如果 URL 不包含 /api，则添加
  return envApiUrl.endsWith('/api') ? envApiUrl : `${envApiUrl}/api`;
};

export const API_BASE_URL = getApiBaseUrl();

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
};

// 添加日志以验证URL
console.log('🌐 API_BASE_URL:', API_BASE_URL);
console.log('📱 Platform:', Platform.OS);
console.log('🔧 Is Development:', __DEV__);
console.log('🔗 EXPO_PUBLIC_API_URL from env:', process.env.EXPO_PUBLIC_API_URL);

// API端点
export const API_ENDPOINTS = {
  // 用户相关
  USERS: '/font-user',
  AUTH: '/font-auth',

  // 帖子相关
  THREADS: '/threads',

  // 私信相关
  MESSAGES: '/messages',
  CONVERSATIONS: '/messages/conversations',

  // 品牌和产品
  BRANDS: '/brands',
  PRODUCTS: '/products',
  REVIEWS: '/reviews',

  // 其他
  I18N: '/i18n',
} as const;

// 请求超时时间
export const REQUEST_TIMEOUT = 10000; // 10秒

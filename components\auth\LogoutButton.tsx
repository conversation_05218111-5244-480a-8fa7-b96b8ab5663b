import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSignOut } from '@/store/userStore';

interface LogoutButtonProps {
  style?: any;
  textStyle?: any;
  showIcon?: boolean;
}

/**
 * 登出按钮组件
 * 用于测试全局认证保护系统
 */
export const LogoutButton: React.FC<LogoutButtonProps> = ({
  style,
  textStyle,
  showIcon = true,
}) => {
  const signOut = useSignOut();

  const handleLogout = () => {
    Alert.alert(
      '确认登出',
      '您确定要退出登录吗？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '确定',
          style: 'destructive',
          onPress: signOut,
        },
      ]
    );
  };

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={handleLogout}
      activeOpacity={0.7}
    >
      {showIcon && (
        <Icon name="logout" size={20} color="#fff" style={styles.icon} />
      )}
      <Text style={[styles.text, textStyle]}>退出登录</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ff4757',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
  },
  icon: {
    marginRight: 8,
  },
  text: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default LogoutButton;

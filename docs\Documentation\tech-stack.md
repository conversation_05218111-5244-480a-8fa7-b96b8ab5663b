# 技术栈文档

## 前端

### 核心框架与构建工具
- React Native
- TypeScript

### UI与样式
- Tailwind CSS用于实用优先的样式设计
- daisyui UI用于核心组件

### 状态管理
- React Context API用于全局状态
- 自定义钩子用于本地状态管理
- React Query用于服务器状态管理

### 表单与验证
- React Hook Form用于表单管理
- Zod用于模式验证
- TypeScript用于类型检查

## 后端

### 核心框架
- Spring Boot用于REST API
- Java 17 (LTS)
- 内置Tomcat作为应用服务器

### 数据库与ORM
- 在Railway上运行的MySQL
- Spring Data JPA/Hibernate作为ORM
- Flyway用于数据库迁移处理

### 身份验证
- Spring Security用于认证与授权
- JWT令牌管理
- 社交登录（wechat sdk）

## 基础设施


### 开发工具
- Git用于版本控制
- GitHub用于代码仓库
- GitHub Actions用于CI/CD
- ESLint和Prettier用于代码格式化

## 安全
- HTTPS强制执行
- Spring Security安全功能
- API速率限制
- 输入验证
- SQL注入预防
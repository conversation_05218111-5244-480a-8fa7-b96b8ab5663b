import { PopularProduct } from '@/api/services/productReviewService';
import ProductCard from '@/components/core/explore/show/ProductCard';
import { usePopularProducts } from '@/store/productStore';
import { router } from 'expo-router';
import React, { useMemo } from 'react';
import { ActivityIndicator, FlatList, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

export default function AllPopularProductsScreen() {
  const { data: popularProducts, isLoading, error } = usePopularProducts();
  
  const products = useMemo(() => {
    if (!Array.isArray(popularProducts) || popularProducts.length === 0) {
      return [];
    }
    
    return popularProducts.map((product: PopularProduct) => ({
      _id: product.product_id,
      brand: product.brand || 'Unknown Brand',
      image_url: product.image_url || '',
      name: product.name || 'Unknown Product',
      product_type: product.product_type || '猫粮',
      est_calories: product.est_calories || undefined,
      average_rating: typeof product.average_rating === 'number' ? product.average_rating : 0,
      review_count: product.review_count || 0
    }));
  }, [popularProducts]);

  const handleBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (error || products.length === 0) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Icon name="alert-circle-outline" size={60} color="#e74c3c" style={styles.errorIcon} />
        <Text style={styles.errorText}>无法加载产品数据</Text>
        <Text style={styles.errorSubtext}>请检查网络连接或稍后再试</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      
      <FlatList
        data={products}
        renderItem={({ item }) => (
          <View style={styles.singleItemWrapper}>
            <ProductCard product={item} singleRow={true} />
          </View>
        )}
        keyExtractor={item => item._id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 15,
  },
  singleItemWrapper: {
    width: '100%',
    marginBottom: 1,
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#3498db',
  },
});

import { Alert } from 'react-native';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

/**
 * 标准化错误接口
 */
export interface StandardError {
  type: ErrorType;
  message: string;
  originalError?: any;
  statusCode?: number;
  timestamp: Date;
}

/**
 * 错误消息映射
 */
const ERROR_MESSAGES = {
  [ErrorType.NETWORK]: '网络连接失败，请检查网络设置',
  [ErrorType.VALIDATION]: '输入数据格式不正确',
  [ErrorType.AUTHENTICATION]: '登录已过期，请重新登录',
  [ErrorType.AUTHORIZATION]: '没有权限执行此操作',
  [ErrorType.NOT_FOUND]: '请求的资源不存在',
  [ErrorType.SERVER]: '服务器错误，请稍后重试',
  [ErrorType.UNKNOWN]: '发生未知错误，请稍后重试',
};

/**
 * 根据错误状态码判断错误类型
 */
export const getErrorType = (statusCode?: number, message?: string): ErrorType => {
  if (!statusCode) {
    if (message?.toLowerCase().includes('network')) {
      return ErrorType.NETWORK;
    }
    return ErrorType.UNKNOWN;
  }

  switch (statusCode) {
    case 400:
      return ErrorType.VALIDATION;
    case 401:
      return ErrorType.AUTHENTICATION;
    case 403:
      return ErrorType.AUTHORIZATION;
    case 404:
      return ErrorType.NOT_FOUND;
    case 500:
    case 502:
    case 503:
    case 504:
      return ErrorType.SERVER;
    default:
      if (statusCode >= 400 && statusCode < 500) {
        return ErrorType.VALIDATION;
      }
      if (statusCode >= 500) {
        return ErrorType.SERVER;
      }
      return ErrorType.UNKNOWN;
  }
};

/**
 * 标准化错误对象
 */
export const standardizeError = (error: any): StandardError => {
  const statusCode = error?.status || error?.response?.status;
  const originalMessage = error?.message || error?.response?.data?.message || '未知错误';
  const errorType = getErrorType(statusCode, originalMessage);

  return {
    type: errorType,
    message: ERROR_MESSAGES[errorType],
    originalError: error,
    statusCode,
    timestamp: new Date(),
  };
};

/**
 * 获取用户友好的错误消息
 */
export const getUserFriendlyMessage = (error: any): string => {
  const standardError = standardizeError(error);
  return standardError.message;
};

/**
 * 显示错误提示
 */
export const showErrorAlert = (error: any, title: string = '错误') => {
  const message = getUserFriendlyMessage(error);
  Alert.alert(title, message);
};

/**
 * 记录错误日志
 */
export const logError = (error: any, context?: string) => {
  const standardError = standardizeError(error);
  
  console.error('Error Log:', {
    context,
    type: standardError.type,
    message: standardError.message,
    statusCode: standardError.statusCode,
    timestamp: standardError.timestamp,
    originalError: standardError.originalError,
  });

  // 在生产环境中，这里可以发送到错误监控服务
  // 例如: Sentry, Bugsnag, 或自定义错误收集服务
};

/**
 * 安全执行异步函数，自动处理错误
 */
export const safeAsync = async <T>(
  asyncFn: () => Promise<T>,
  onError?: (error: StandardError) => void,
  showAlert: boolean = true
): Promise<T | null> => {
  try {
    return await asyncFn();
  } catch (error) {
    const standardError = standardizeError(error);
    
    logError(error, 'safeAsync');
    
    if (onError) {
      onError(standardError);
    } else if (showAlert) {
      showErrorAlert(error);
    }
    
    return null;
  }
};

/**
 * 重试机制
 */
export const retryAsync = async <T>(
  asyncFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await asyncFn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        throw error;
      }
      
      // 指数退避延迟
      const retryDelay = delay * Math.pow(2, i);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
  
  throw lastError;
};

import ThreadItem from '@/components/list-item/ThreadItem';
import { SearchThread } from '@/types/search-type';
import React from 'react';
import {
  FlatList,
  StyleSheet,
  Text,
  View,
} from 'react-native';

interface ThreadsListProps {
  threads: SearchThread[];
  onThreadPress: (threadId: string) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoadingMore?: boolean;
}

const ThreadsList: React.FC<ThreadsListProps> = ({
  threads,
  onThreadPress,
  onLoadMore,
  hasMore = false,
  isLoadingMore = false,
}) => {
  const renderThreadItem = ({ item }: { item: SearchThread }) => (
    <ThreadItem item={item} onThreadPress={onThreadPress} />
  );

  if (threads.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>暂无相关帖子</Text>
      </View>
    );
  }

  const handleLoadMore = () => {
    if (hasMore && !isLoadingMore && onLoadMore) {
      onLoadMore();
    }
  };

  const renderFooter = () => {
    if (isLoadingMore) {
      return (
        <View style={styles.loadingFooter}>
          <Text style={styles.loadingText}>加载更多帖子...</Text>
        </View>
      );
    }
    if (!hasMore && threads.length > 0) {
      return (
        <View style={styles.endOfListContainer}>
          <Text style={styles.endOfListText}>没有更多帖子了</Text>
        </View>
      );
    }
    return null;
  };

  return (
    <FlatList
      data={threads}
      renderItem={renderThreadItem}
      keyExtractor={(item) => item.thread_id}
      contentContainerStyle={styles.listContainer}
      showsVerticalScrollIndicator={false}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      ListFooterComponent={renderFooter}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
    />
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  threadItem: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  threadInfo: {
    flex: 1,
  },
  threadTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  threadContent: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  threadMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  threadAuthor: {
    fontSize: 14,
    color: '#666',
  },
  threadStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  threadStatText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
  loadingFooter: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
  },
  endOfListContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  endOfListText: {
    fontSize: 14,
    color: '#999',
  },
});

export default ThreadsList;

import ProductDetailHeader from '@/components/core/explore/product-content/ProductDetailHeader';
import ReviewSection from '@/components/review/ReviewSection';
import SubmitReviewBottomBar from '@/components/review/SubmitReviewButtomBar';
import { useProductReviews } from '@/store/productReviewStore';
import { useProductDetail } from '@/store/productStore';
import { useUserInfo } from '@/store/userStore';
import { recordProductHistory } from '@/utils/historyUtils';
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { getProductImageUrlSync } from '@/utils/imageUtils';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import StackHeader from '@/components/ui/StackHeader';
export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [reviewType, setReviewType] = useState<'all' | 'long' | 'short'>('all');
  const [sortBy, setSortBy] = useState<'latest' | 'hot'>('latest');

  const userInfo = useUserInfo();
  const {
    product,
    isLoading: isProductLoading,
    error: productError
  } = useProductDetail(id);

  const {
    reviews,
    isLoading: isReviewsLoading,
    isLoadingMore,
    isRefreshing,
    error: reviewsError,
    hasMore,
    refreshReviews,
    loadMoreReviews
  } = useProductReviews(id, 5, reviewType, sortBy);

  const handleReviewTypeChange = (newType: 'all' | 'long' | 'short') => {
    setReviewType(newType);
  };

  const handleSortByChange = (newSortBy: 'latest' | 'hot') => {
    setSortBy(newSortBy);
  };

  // 记录产品浏览历史
  useEffect(() => {
    if (userInfo?.id && product && !isProductLoading) {
      // 延迟记录，确保页面已经完全加载
      const timer = setTimeout(() => {
        recordProductHistory(
          userInfo?.id || '',
          product._id,
          product.name,
          undefined, // Product doesn't have description field
          product.image_url
        );
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [userInfo?.id, product, isProductLoading]);

  const getImageUrl = (): string => {
    if (!product!._id) {
      return getProductImageUrlSync(product!._id);
    }
    return product!.image_url || ""
  };


  if (isProductLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>加载产品信息中...</Text>
      </View>
    );
  }

  if (productError || !product || !id) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Icon name="alert-circle-outline" size={60} color="#e74c3c" style={styles.errorIcon} />
        <Text style={styles.errorText}>无法加载产品信息</Text>
        <Text style={styles.errorSubtext}>
          {!id ? '产品ID缺失' : productError?.message || '请检查网络连接或稍后再试'}
        </Text>
        {__DEV__ && (
          <Text style={styles.debugText}>
            Product ID: {id || 'undefined'}
            {'\n'}Error: {JSON.stringify(productError, null, 2)}
          </Text>
        )}
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StackHeader blank={true} />
      <ReviewSection
        entityType="product"
        entityId={id}
        reviews={reviews}
        isLoading={isReviewsLoading}
        isLoadingMore={isLoadingMore}
        isRefreshing={isRefreshing}
        error={reviewsError}
        reviewType={reviewType}
        onReviewTypeChange={handleReviewTypeChange}
        sortBy={sortBy}
        onSortByChange={handleSortByChange}
        hasMore={hasMore}
        onRefresh={refreshReviews}
        onLoadMore={loadMoreReviews}
        brandHeader={<ProductDetailHeader product={product} />}
      />
      <SubmitReviewBottomBar
        entityType="product"
        entityId={id}
        isFavorited={false}
        onFavoritePress={() => { }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white'
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#3498db',
  },
  debugText: {
    fontSize: 10,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    fontFamily: 'monospace',
    paddingHorizontal: 20,
  },
});

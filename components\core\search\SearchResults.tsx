import {
  useSearchBrandsInfinite,
  useSearchProductsInfinite,
  useSearchThreadsInfinite
} from '@/hooks/useSearch';
import { SearchBrand, SearchProduct, SearchThread } from '@/types/search-type';
import React, { useMemo } from 'react';
import {
  StyleSheet,
  Text,
  View
} from 'react-native';
import BrandsList from './BrandsList';
import ProductsList from './ProductsList';
import SearchTabs, { TabType } from './SearchTabs';
import ThreadsList from './ThreadsList';
interface SearchResultsProps {
  searchQuery: string;
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  onProductPress: (productId: string) => void;
  onBrandPress: (brandId: string) => void;
  onThreadPress: (threadId: string) => void;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  activeTab,
  searchQuery,
  onTabChange,
  onProductPress,
  onBrandPress,
  onThreadPress,
}) => {

  // 使用独立的搜索hooks
  const productsQuery = useSearchProductsInfinite(searchQuery || '');
  const brandsQuery = useSearchBrandsInfinite(searchQuery || '');
  const threadsQuery = useSearchThreadsInfinite(searchQuery || '');

  // 扁平化数据
  const flattenedData = useMemo(() => {
    switch (activeTab) {
      case 'products':
        if (!productsQuery.data?.pages) return [];
        return productsQuery.data.pages.flatMap(page => page.products || []);
      case 'brands':
        if (!brandsQuery.data?.pages) return [];
        return brandsQuery.data.pages.flatMap(page => page.brands || []);
      case 'threads':
        if (!threadsQuery.data?.pages) return [];
        return threadsQuery.data.pages.flatMap(page => page.threads || []);
      default:
        return [];
    }
  }, [activeTab, productsQuery.data, brandsQuery.data, threadsQuery.data]);



  // 获取当前tab的数据和状态
  const getCurrentTabData = () => {
    switch (activeTab) {
      case 'products':
        return {
          data: flattenedData,
          isLoading: productsQuery.isLoading,
          error: productsQuery.error,
          fetchNextPage: productsQuery.fetchNextPage,
          hasNextPage: productsQuery.hasNextPage,
          isFetchingNextPage: productsQuery.isFetchingNextPage,
        };
      case 'brands':
        return {
          data: flattenedData,
          isLoading: brandsQuery.isLoading,
          error: brandsQuery.error,
          fetchNextPage: brandsQuery.fetchNextPage,
          hasNextPage: brandsQuery.hasNextPage,
          isFetchingNextPage: brandsQuery.isFetchingNextPage,
        };
      case 'threads':
        return {
          data: flattenedData,
          isLoading: threadsQuery.isLoading,
          error: threadsQuery.error,
          fetchNextPage: threadsQuery.fetchNextPage,
          hasNextPage: threadsQuery.hasNextPage,
          isFetchingNextPage: threadsQuery.isFetchingNextPage,
        };
      default:
        return {
          data: [],
          isLoading: false,
          error: null,
          fetchNextPage: () => { },
          hasNextPage: false,
          isFetchingNextPage: false,
        };
    }
  };

  const currentTabData = getCurrentTabData();

  if (currentTabData.isLoading && !currentTabData.data.length) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>努力加载中...</Text>
      </View>
    );
  }

  if (currentTabData.error) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>搜索出错了</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Tab导航 */}
      <SearchTabs
        activeTab={activeTab}
        onTabChange={onTabChange}
      />

      {/* Tab内容 */}
      {activeTab === 'brands' && (
        <BrandsList
          brands={flattenedData as SearchBrand[]}
          onBrandPress={onBrandPress}
          onLoadMore={currentTabData.fetchNextPage}
          hasMore={currentTabData.hasNextPage}
          isLoadingMore={currentTabData.isFetchingNextPage}
        />
      )}
      {activeTab === 'products' && (
        <ProductsList
          products={flattenedData as SearchProduct[]}
          onProductPress={onProductPress}
          onLoadMore={currentTabData.fetchNextPage}
          hasMore={currentTabData.hasNextPage}
          isLoadingMore={currentTabData.isFetchingNextPage}
        />
      )}
      {activeTab === 'threads' && (
        <ThreadsList
          threads={flattenedData as SearchThread[]}
          onThreadPress={onThreadPress}
          onLoadMore={currentTabData.fetchNextPage}
          hasMore={currentTabData.hasNextPage}
          isLoadingMore={currentTabData.isFetchingNextPage}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  sortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  resultCount: {
    fontSize: 14,
    color: '#666',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f8f8f8',
    borderRadius: 16,
  },
  sortText: {
    fontSize: 14,
    color: '#666',
    marginRight: 4,
  },
});

export default SearchResults;

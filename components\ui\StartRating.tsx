import React from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface StarRatingProps {
  score: number; // 0-10 scale
  style?: ViewStyle;
}

const StarRating: React.FC<StarRatingProps> = ({ 
  score, 
  style,
}) => {
  const starRating = Math.max(0, Math.min(5, score * 0.5));
  
  const fullStars = Math.floor(starRating);
  const hasHalfStar = starRating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  const renderStars = () => {
    const stars = [];
    
    // Full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Icon 
          key={`full-${i}`} 
          name="star" 
          size={16} 
          color="#FFD700" 
        />
      );
    }
    
    // Half star
    if (hasHalfStar) {
      stars.push(
        <Icon 
          key="half" 
          name="star-half" 
          size={16} 
          color="#FFD700" 
        />
      );
    }
    
    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Icon 
          key={`empty-${i}`} 
          name="star-border" 
          size={16} 
          color="#E0E0E0" 
        />
      );
    }
    
    return stars;
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.starsContainer}>
        {renderStars()}
      </View>
      {score !== 0 && (
        <Text style={styles.scoreText}>
          {score.toFixed(1)}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreText: {
    marginLeft: 6,
    fontSize: 12,
    color: '#666',
  },
});

export default StarRating;

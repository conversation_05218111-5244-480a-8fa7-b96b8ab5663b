import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { HistoryItem as HistoryItemType } from '@/types/history';
import { buildImageUrl } from '@/utils/imageUtils';

interface HistoryItemProps {
  item: HistoryItemType;
  isSelected?: boolean;
  isSelectionMode?: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
  onToggleSelect?: () => void;
  onDelete?: () => void;
}

export default function HistoryItem({
  item,
  isSelected = false,
  isSelectionMode = false,
  onPress,
  onLongPress,
  onToggleSelect,
  onDelete,
}: HistoryItemProps) {
  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  // 获取类型图标和颜色
  const getTypeInfo = () => {
    switch (item.item_type) {
      case 'thread':
        return { icon: 'chatbubble-outline', color: '#007AFF', label: '帖子' };
      case 'product':
        return { icon: 'cube-outline', color: '#34C759', label: '产品' };
      case 'brand':
        return { icon: 'business-outline', color: '#FF9500', label: '品牌' };
      default:
        return { icon: 'document-outline', color: '#666', label: '未知' };
    }
  };

  const typeInfo = getTypeInfo();

  const handlePress = () => {
    if (isSelectionMode) {
      onToggleSelect?.();
    } else {
      onPress?.();
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isSelected && styles.selectedContainer,
      ]}
      onPress={handlePress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      {/* 选择模式下的复选框 */}
      {isSelectionMode && (
        <TouchableOpacity
          style={styles.checkbox}
          onPress={onToggleSelect}
        >
          <Ionicons
            name={isSelected ? "checkbox" : "square-outline"}
            size={24}
            color={isSelected ? "#007AFF" : "#ccc"}
          />
        </TouchableOpacity>
      )}

      {/* 内容图片 */}
      <View style={styles.imageContainer}>
        {item.item_image ? (
          <Image
            source={{ uri: buildImageUrl(item.item_image) }}
            style={styles.itemImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.placeholderImage, { backgroundColor: typeInfo.color + '20' }]}>
            <Ionicons
              name={typeInfo.icon as any}
              size={24}
              color={typeInfo.color}
            />
          </View>
        )}
      </View>

      {/* 内容信息 */}
      <View style={styles.contentContainer}>
        <View style={styles.header}>
          <View style={styles.typeTag}>
            <Ionicons
              name={typeInfo.icon as any}
              size={12}
              color={typeInfo.color}
            />
            <Text style={[styles.typeText, { color: typeInfo.color }]}>
              {typeInfo.label}
            </Text>
          </View>
          <Text style={styles.timeText}>{formatTime(item.visited_at)}</Text>
        </View>

        <Text style={styles.title} numberOfLines={2}>
          {item.item_title}
        </Text>

        {item.item_description && (
          <Text style={styles.description} numberOfLines={2}>
            {item.item_description}
          </Text>
        )}
      </View>

      {/* 删除按钮 */}
      {!isSelectionMode && (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={onDelete}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="close" size={20} color="#999" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedContainer: {
    backgroundColor: '#f0f8ff',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  checkbox: {
    marginRight: 12,
    justifyContent: 'center',
  },
  imageContainer: {
    marginRight: 12,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    lineHeight: 22,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  deleteButton: {
    padding: 4,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
});

import { getProductImageUrlSync } from '@/utils/imageUtils';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import React, { useMemo, useState } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface ProductCardProps {
  product: {
    _id: string;
    name: string;
    brand: string;
    image_url: string;
    product_type?: string;
    est_calories?: string;
    average_rating?: number | string;
    review_count?: number;
  };
  onViewAll?: () => void;
  totalCount?: number;
  singleRow?: boolean;
}


const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onViewAll,
  totalCount,
  singleRow = false
}) => {
  const router = useRouter();
  const [imageLoadError, setImageLoadError] = useState(false);

  // 直接使用产品ID构建本地图片URL
  const imageUrl = useMemo(() => {
    if (!imageLoadError && product._id) {
      return getProductImageUrlSync(product._id);
    }
    return '';
  }, [product._id, imageLoadError]);

  // 清理产品名称，去除品牌名称
  const cleanProductName = useMemo(() => {
    if (!product.name || !product.brand) return product.name;
    if (product.name.startsWith(product.brand)) {
      return product.name.substring(product.brand.length).replace(/^[\s-_]+/, '');
    }

    return product.name;
  }, [product.name, product.brand]);

  // 导航到产品详情
  const handlePress = () => {
    router.push({
      pathname: "/(stack)/product-detail",
      params: { id: product._id }
    });
  };

  // 格式化评分显示
  const ratingDisplay = useMemo(() => {
    if (typeof product.average_rating === "string") {
      return product.average_rating === "暂无评分" ? "暂无评分" : product.average_rating;
    } else if (typeof product.average_rating === 'number') {
      return product.average_rating.toFixed(1);
    } else {
      return "暂无评分";
    }
  }, [product.average_rating]);

  const handleImageError = () => {
    setImageLoadError(true);
  };

  const handleImageLoad = () => {

  };

  return (
    <Pressable
      style={({ pressed }) => [
        styles.card,
        singleRow ? styles.singleRowCard : {},
        pressed ? styles.cardPressed : {}
      ]}
      onPress={handlePress}
    >
      <View style={[styles.imageContainer, singleRow && styles.singleRowImageContainer]}>
        <Image
          source={{ uri: imageUrl }}
          style={styles.image}
          contentFit="contain"
          transition={200}
          onError={handleImageError}
          onLoad={handleImageLoad}
        />
      </View>

      <View style={[styles.infoContainer, singleRow && styles.singleRowInfoContainer]}>
        <Text style={styles.brand}>{product.brand}</Text>
        <Text
          style={[styles.name, singleRow && styles.singleRowName]}
          numberOfLines={singleRow ? 2 : 1}
        >
          {cleanProductName}
        </Text>

        <View style={styles.metaContainer}>
          <View style={styles.ratingContainer}>
            <Text style={styles.rating}>
              {ratingDisplay}
            </Text>
            {typeof product.average_rating === 'number' && product.average_rating > 0 &&
              <Icon name="star" size={singleRow ? 14 : 10} color="#ff9900" />
            }
          </View>
          <Text style={styles.type}>{product.product_type || '猫粮'}</Text>
        </View>

        <View style={styles.statsContainer}>
          {product.est_calories && (
            <View style={styles.statItem}>
              <Icon name="fire" size={singleRow ? 14 : 10} color="#ff6b6b" />
              <Text style={[styles.statText, singleRow && styles.singleRowStatText]}>
                {product.est_calories}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '31%',
    backgroundColor: 'white',
    borderRadius: 10,
    marginBottom: 12,
  },
  cardPressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  imageContainer: {
    width: '100%',
    height: 80,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  headerContainer: {
    position: 'absolute',
    top: 4,
    left: 4,
    right: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  badge: {
    backgroundColor: '#ff9900',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 9,
    fontWeight: 'bold',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.8)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  viewAllText: {
    fontSize: 9,
    color: '#666',
    marginRight: 2,
  },
  infoContainer: {
    padding: 8,
  },
  brand: {
    fontSize: 10,
    color: '#666',
    marginBottom: 2,
  },
  name: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#ff9900',
    marginRight: 2,
  },
  type: {
    fontSize: 9,
    color: '#888',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 3,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 9,
    color: '#888',
    marginLeft: 2,
  },
  favoriteButton: {
    position: 'absolute',
    top: 6,
    right: 6,
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  singleRowCard: {
    width: '100%',
    flexDirection: 'row',
    height: 120,
  },
  singleRowImageContainer: {
    width: 120,
    height: '100%',
  },
  singleRowInfoContainer: {
    flex: 1,
    justifyContent: 'space-between',
    height: '100%',
  },
  singleRowName: {
    fontSize: 14,
    height: 'auto',
  },
  singleRowStatText: {
    fontSize: 12,
  },
  singleRowFavoriteButton: {
    top: 10,
    right: 10,
    width: 32,
    height: 32,
  },
});


export default ProductCard;

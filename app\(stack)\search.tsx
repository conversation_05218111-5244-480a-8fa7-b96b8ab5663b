import { BrandNameRecord, ProductNameRecord } from '@/types/entity';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  StyleSheet,
  TextInput,
} from 'react-native';
import { searchService } from '../../api/services/searchService';
import SearchHeader from '../../components/core/search/SearchHeader';
import SearchPreview from '../../components/core/search/SearchPreview';
import SearchResults from '../../components/core/search/SearchResults';
import SearchSuggestions from '../../components/core/search/SearchSuggestions';
import type { TabType } from '../../components/core/search/SearchTabs';
import { SafeAreaView } from 'react-native-safe-area-context';

interface SearchPreviewResults {
  products: ProductNameRecord[];
  brands: BrandNameRecord[];
}

export default function SearchTab() {
  const [searchText, setSearchText] = useState('');
  const [searchHistory, setSearchHistory] = useState(['皇家猫粮', '希尔斯', '冠能狗粮']);
  const [hotSearches, setHotSearches] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<SearchPreviewResults>({ products: [], brands: [] });
  const [isSearching, setIsSearching] = useState(false);
  const [showDetailedResults, setShowDetailedResults] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('products');
  const textInputRef = useRef<TextInput>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // 根据fadeAnim属性来执行淡入效果
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 700,
      useNativeDriver: true,
    }).start();

    // 拉起键盘
    setTimeout(() => {
      textInputRef.current?.focus();
    }, 100);

    // 加载热门搜索词
    const loadHotSearches = async () => {
      try {
        const hotSearches = await searchService.getHotSearches();
        setHotSearches(hotSearches);
      } catch (error) {
        console.error('Failed to load hot searches:', error);
      }
    };

    loadHotSearches();
  }, [fadeAnim]);

  // 实时搜索
  useEffect(() => {
    if (showDetailedResults) return; // 不在详细结果页时才进行实时搜索

    if (searchText.trim().length > 0) {
      setIsSearching(true);
      // 防抖计时器
      const timer = setTimeout(async () => {
        try {
          const results = await searchService.searchPreview(searchText);
          setSearchResults(results);
        } catch (error) {
          console.error('Search preview failed:', error);
          setSearchResults({ products: [], brands: [] });
        } finally {
          setIsSearching(false);
        }
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setSearchResults({ products: [], brands: [] });
      setIsSearching(false);
    }
  }, [searchText, showDetailedResults]);

  const handleProductPress = (productId: string) => {
    router.push({
      pathname: '/product-detail',
      params: { id: productId }
    });
  };

  const handleBrandPress = (brandId: string) => {
    router.push({
      pathname: '/brand-detail',
      params: { id: brandId }
    });
  };

  const handleThreadPress = (threadId: string) => {
    console.log('Thread pressed:', threadId);
    router.push({
      pathname: '/thread-detail',
      params: { threadId }
    })
  };

  const handleSearch = (query: string) => {
    if (query.trim()) {
      //添加历史 -> 跳转到详细结果 -> 收起键盘
      setSearchHistory(prev => [query, ...prev.filter(item => item !== query)].slice(0, 10));
      setSearchText(query);
      setShowDetailedResults(true);
      textInputRef.current?.blur();
    }
  };

  const handleBack = () => {

    router.back();

  };

  const handleKeyBoardOnFocused = () => {
    if (showDetailedResults) {
      setShowDetailedResults(false);
    }
  };

  const clearHistory = () => {
    setSearchHistory([]);
  };

  return (

    <SafeAreaView style={styles.container}>
      {/* 统一的搜索框 */}
      <SearchHeader
        searchText={searchText}
        onSearchTextChange={setSearchText}
        onBack={handleBack}
        onFocus={handleKeyBoardOnFocused}
        onSubmit={handleSearch}
        textInputRef={textInputRef}
      />

      <Animated.View style={[styles.contentContainer, { opacity: fadeAnim }]}>
        {showDetailedResults ? (
          <SearchResults
            searchQuery={searchText}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onProductPress={handleProductPress}
            onBrandPress={handleBrandPress}
            onThreadPress={handleThreadPress}
          />
        ) : searchText.trim() ? (
          <SearchPreview
            searchResults={searchResults}
            isSearching={isSearching}
            onProductPress={handleProductPress}
            onBrandPress={handleBrandPress}
            onSearchItem={handleSearch}
          />
        ) : (
          <SearchSuggestions
            searchHistory={searchHistory}
            hotSearches={hotSearches}
            onHistoryItemPress={handleSearch}
            onHotSearchPress={handleSearch}
            onClearHistory={clearHistory}
          />
        )}
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex:1,
    backgroundColor: 'white',
  },
  contentContainer: {
    flex: 1,
  },
});

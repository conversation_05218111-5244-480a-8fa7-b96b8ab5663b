import { SearchThread } from '@/types/search-type';
import { getAvatarUrl } from '@/utils/userUtils';
import { Image } from 'expo-image';
import React from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Icon from 'react-native-vector-icons/EvilIcons';

interface ThreadItemProps {
    item: SearchThread;
    onThreadPress: (threadId: string) => void;
}

const ThreadItem: React.FC<ThreadItemProps> = ({
    item,
    onThreadPress
}) => {
    const threadId = item.thread_id || '';

    // 格式化时间
    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

        if (diffInHours < 1) {
            return '刚刚';
        } else if (diffInHours < 24) {
            return `${diffInHours}小时前`;
        } else {
            const diffInDays = Math.floor(diffInHours / 24);
            if (diffInDays < 7) {
                return `${diffInDays}天前`;
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        }
    };

    return (
        <TouchableOpacity style={styles.threadItem} onPress={() => onThreadPress(threadId)}>
            <View style={styles.threadImageContainer}>
                <Image
                    source={{ uri: getAvatarUrl(item.user.avatar) }}
                    style={styles.threadImage}
                    contentFit="cover"
                    transition={200}
                />
            </View>
            <View style={styles.threadInfo}>
                {item.title && (
                    <Text style={styles.threadTitle} numberOfLines={2}>
                        {item.title}
                    </Text>
                )}
                <Text style={styles.threadContent} numberOfLines={2}>{item.content}</Text>
                <View style={styles.threadMetaContainer}>
                    <Text style={styles.threadAuthor}>{item.user.username}</Text>
                    <Text style={styles.threadTime}>{formatTime(item.created_at)}</Text>
                </View>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    threadItem: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginVertical: 8,
    },
    threadImageContainer: {
        width: 40,
        height: 40,
        borderRadius: 40,
        overflow: 'hidden',
    },
    threadImage: {
        width: '100%',
        height: '100%',
    },
    threadImagePlaceholder: {
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9f9f9',
        borderRadius: 40,
    },
    threadInfo: {
        flex: 1,
        marginLeft: 12,
        justifyContent: 'space-between',
    },
    threadTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    threadContent: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
        lineHeight: 20,
    },
    threadMetaContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    threadAuthor: {
        fontSize: 12,
        marginRight: 12,
        color: '#999',
    },
    threadTime: {
        fontSize: 12,
        color: '#999',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
    },
    loadingFooter: {
        paddingVertical: 20,
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: '#666',
    },
    endOfListContainer: {
        paddingVertical: 20,
        alignItems: 'center',
    },
    endOfListText: {
        fontSize: 14,
        color: '#999',
    },
});

export default ThreadItem;

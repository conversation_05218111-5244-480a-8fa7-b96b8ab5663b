#!/usr/bin/env node

/**
 * APK 调试脚本
 * 用于查看打包后应用的启动日志
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkADB() {
  try {
    execSync('adb version', { stdio: 'pipe' });
    log('✅ ADB 已安装', 'green');
    return true;
  } catch (error) {
    log('❌ ADB 未安装或不在 PATH 中', 'red');
    log('请安装 Android SDK Platform Tools', 'yellow');
    log('下载地址: https://developer.android.com/studio/releases/platform-tools', 'cyan');
    return false;
  }
}

function checkDevices() {
  try {
    const output = execSync('adb devices', { encoding: 'utf8' });
    const lines = output.split('\n').filter(line => line.trim() && !line.includes('List of devices'));
    
    if (lines.length === 0) {
      log('❌ 没有检测到连接的设备', 'red');
      log('请确保:', 'yellow');
      log('  1. 设备已连接并开启 USB 调试', 'cyan');
      log('  2. 已授权计算机进行调试', 'cyan');
      return false;
    }
    
    log(`✅ 检测到 ${lines.length} 个设备:`, 'green');
    lines.forEach(line => {
      const [deviceId, status] = line.split('\t');
      log(`  📱 ${deviceId} - ${status}`, 'cyan');
    });
    return true;
  } catch (error) {
    log(`❌ 检查设备失败: ${error.message}`, 'red');
    return false;
  }
}

function startLogging(packageName = 'com.lingwcyovo.petfood') {
  log('\n🔍 开始监控应用日志...', 'magenta');
  log('按 Ctrl+C 停止监控', 'yellow');
  log('='.repeat(60), 'cyan');
  
  // 清除之前的日志
  try {
    execSync('adb logcat -c', { stdio: 'pipe' });
  } catch (error) {
    // 忽略清除日志的错误
  }
  
  // 创建日志文件
  const logFile = `petfood_debug_${new Date().toISOString().replace(/[:.]/g, '-')}.log`;
  const logStream = fs.createWriteStream(logFile);
  
  log(`📝 日志将保存到: ${logFile}`, 'blue');
  
  // 启动 logcat
  const logcat = spawn('adb', [
    'logcat',
    '-v', 'time',
    // 过滤条件
    '*:E',  // 错误
    '*:W',  // 警告
    'ReactNativeJS:*',  // React Native JS 日志
    'ExpoModulesCore:*',  // Expo 模块日志
    'ReactNative:*',  // React Native 原生日志
    `${packageName}:*`,  // 应用包名日志
  ]);
  
  logcat.stdout.on('data', (data) => {
    const logLine = data.toString();
    
    // 写入文件
    logStream.write(logLine);
    
    // 控制台输出（带颜色）
    if (logLine.includes('E/')) {
      process.stdout.write(colors.red + logLine + colors.reset);
    } else if (logLine.includes('W/')) {
      process.stdout.write(colors.yellow + logLine + colors.reset);
    } else if (logLine.includes('ReactNativeJS')) {
      process.stdout.write(colors.green + logLine + colors.reset);
    } else if (logLine.includes(packageName)) {
      process.stdout.write(colors.cyan + logLine + colors.reset);
    } else {
      process.stdout.write(logLine);
    }
  });
  
  logcat.stderr.on('data', (data) => {
    log(`ADB 错误: ${data}`, 'red');
  });
  
  logcat.on('close', (code) => {
    logStream.end();
    log(`\n📄 日志已保存到: ${logFile}`, 'blue');
    log(`日志监控结束 (退出码: ${code})`, 'yellow');
  });
  
  // 处理 Ctrl+C
  process.on('SIGINT', () => {
    log('\n🛑 停止日志监控...', 'yellow');
    logcat.kill();
    process.exit(0);
  });
}

function showAppInfo(packageName = 'com.lingwcyovo.petfood') {
  try {
    log('\n📱 应用信息:', 'blue');
    
    // 检查应用是否已安装
    const packages = execSync(`adb shell pm list packages | grep ${packageName}`, { encoding: 'utf8' });
    if (packages.trim()) {
      log(`✅ 应用已安装: ${packageName}`, 'green');
      
      // 获取应用版本信息
      try {
        const versionInfo = execSync(`adb shell dumpsys package ${packageName} | grep versionName`, { encoding: 'utf8' });
        log(`📋 ${versionInfo.trim()}`, 'cyan');
      } catch (error) {
        // 忽略版本信息获取错误
      }
    } else {
      log(`❌ 应用未安装: ${packageName}`, 'red');
      log('请先安装 APK 文件', 'yellow');
    }
  } catch (error) {
    log(`❌ 获取应用信息失败: ${error.message}`, 'red');
  }
}

function showHelp() {
  log('\n📱 PetFood APK 调试工具', 'bold');
  log('='.repeat(40), 'cyan');
  
  log('\n使用方法:', 'yellow');
  log('  node scripts/debug-apk.js [选项]', 'cyan');
  
  log('\n选项:', 'yellow');
  log('  --check      检查 ADB 和设备连接', 'cyan');
  log('  --info       显示应用信息', 'cyan');
  log('  --log        开始监控日志 (默认)', 'cyan');
  log('  --help       显示帮助信息', 'cyan');
  
  log('\n使用步骤:', 'yellow');
  log('  1. 连接 Android 设备并开启 USB 调试', 'cyan');
  log('  2. 安装 APK: adb install your-app.apk', 'cyan');
  log('  3. 运行此脚本: node scripts/debug-apk.js', 'cyan');
  log('  4. 启动应用并查看日志', 'cyan');
  
  log('\n常用 ADB 命令:', 'yellow');
  log('  adb devices                    - 查看连接的设备', 'cyan');
  log('  adb install app.apk           - 安装 APK', 'cyan');
  log('  adb uninstall com.package.name - 卸载应用', 'cyan');
  log('  adb logcat                    - 查看所有日志', 'cyan');
  log('  adb shell am start -n com.package.name/.MainActivity - 启动应用', 'cyan');
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    showHelp();
    return;
  }
  
  log('🔧 PetFood APK 调试工具', 'bold');
  log('='.repeat(50), 'cyan');
  
  // 检查 ADB
  if (!checkADB()) {
    process.exit(1);
  }
  
  // 检查设备
  if (!checkDevices()) {
    process.exit(1);
  }
  
  if (args.includes('--check')) {
    log('\n✅ 环境检查完成', 'green');
    return;
  }
  
  if (args.includes('--info')) {
    showAppInfo();
    return;
  }
  
  // 默认开始日志监控
  showAppInfo();
  startLogging();
}

main();

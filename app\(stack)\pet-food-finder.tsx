import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { usePetFoodFilter } from '@/hooks/usePetFoodFilter';
import { useFilteredProducts } from '@/hooks/useFilteredProducts';
import FilterChips from '@/components/core/pet-food-finder/FilterChips';
import SortOptions from '@/components/core/pet-food-finder/SortOptions';
import ProductGrid from '@/components/core/pet-food-finder/ProductGrid';
import FilterModal from '@/components/core/pet-food-finder/FilterModal';

export default function PetFoodFinderScreen() {
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSortOptions, setShowSortOptions] = useState(false);

  // 筛选状态管理
  const {
    filterState,
    hasActiveFilters,
    activeFilterCount,
    updateSearchQuery,
    updateBrands,
    updateProductTypes,
    updateNutritionRange,
    updateQualityFilters,
    updateAllergens,
    updateRatingFilters,
    updateSort,
    resetFilters,
    resetFilter,
  } = usePetFoodFilter();

  // 获取筛选后的产品数据
  const {
    products,
    totalCount,
    isLoading,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useFilteredProducts(filterState);

  // 处理产品点击
  const handleProductPress = useCallback((productId: string) => {
    router.push({
      pathname: '/product-detail',
      params: { id: productId }
    });
  }, []);

  // 处理筛选条件移除
  const handleRemoveFilter = useCallback((filterKey: keyof typeof filterState.filters, value?: string) => {
    if (value && (filterKey === 'brands' || filterKey === 'productTypes' || filterKey === 'excludeAllergens')) {
      // 对于数组类型的筛选，移除特定值
      const currentValues = filterState.filters[filterKey] as string[];
      const newValues = currentValues.filter(v => v !== value);
      
      if (filterKey === 'brands') {
        updateBrands(newValues);
      } else if (filterKey === 'productTypes') {
        updateProductTypes(newValues);
      } else if (filterKey === 'excludeAllergens') {
        updateAllergens(newValues);
      }
    } else {
      // 对于单值类型的筛选，直接重置
      resetFilter(filterKey);
    }
  }, [filterState.filters, updateBrands, updateProductTypes, updateAllergens, resetFilter]);

  // 处理返回
  const handleBack = () => {
    router.back();
  };

  // 模拟可用品牌数据（实际应该从API获取）
  const availableBrands = [
    'Royal Canin', 'Hill\'s', 'Purina Pro Plan', 'Blue Buffalo', 
    'Wellness', 'Orijen', 'Acana', 'Taste of the Wild'
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        <View style={styles.titleContainer}>
          <Text style={styles.title}>找宠粮</Text>
          {totalCount > 0 && (
            <Text style={styles.subtitle}>共 {totalCount} 款产品</Text>
          )}
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity 
            onPress={() => setShowSortOptions(!showSortOptions)} 
            style={styles.actionButton}
          >
            <Ionicons name="swap-vertical" size={20} color="#333" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            onPress={() => setShowFilterModal(true)} 
            style={[styles.actionButton, hasActiveFilters && styles.activeActionButton]}
          >
            <Ionicons 
              name="options" 
              size={20} 
              color={hasActiveFilters ? "#4d920f" : "#333"} 
            />
            {activeFilterCount > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{activeFilterCount}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* 排序选项 */}
      {showSortOptions && (
        <SortOptions
          sortBy={filterState.sortBy}
          sortOrder={filterState.sortOrder}
          onSortChange={(sortBy, sortOrder) => {
            updateSort(sortBy, sortOrder);
            setShowSortOptions(false);
          }}
        />
      )}

      {/* 已选筛选条件 */}
      {hasActiveFilters && (
        <FilterChips
          filters={filterState.filters}
          onRemoveFilter={handleRemoveFilter}
          onClearAll={resetFilters}
        />
      )}

      {/* 产品列表 */}
      <ProductGrid
        products={products}
        onLoadMore={fetchNextPage}
        hasMore={hasNextPage}
        isLoadingMore={isFetchingNextPage}
        isLoading={isLoading}
      />

      {/* 筛选弹窗 */}
      <FilterModal
        visible={showFilterModal}
        filters={filterState.filters}
        onClose={() => setShowFilterModal(false)}
        onFiltersChange={{
          updateSearchQuery,
          updateBrands,
          updateProductTypes,
          updateNutritionRange,
          updateQualityFilters,
          updateAllergens,
          updateRatingFilters,
        }}
        onResetFilters={resetFilters}
        availableBrands={availableBrands}
        activeFilterCount={activeFilterCount}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  subtitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    position: 'relative',
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  activeActionButton: {
    backgroundColor: '#e8f5e8',
  },
  filterBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: '#4d920f',
    borderRadius: 8,
    paddingHorizontal: 4,
    paddingVertical: 1,
    minWidth: 16,
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '600',
  },
});

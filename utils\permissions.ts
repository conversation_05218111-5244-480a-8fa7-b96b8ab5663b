import * as ImagePicker from 'expo-image-picker';
import { Alert, Linking, Platform } from 'react-native';

/**
 * 请求相机权限
 * @returns Promise<boolean> 是否获得权限
 */
export const requestCameraPermission = async (): Promise<boolean> => {
  try {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert(
        '需要相机权限',
        '请在设置中允许访问相机以拍摄照片',
        [
          { text: '取消', style: 'cancel' },
          {
            text: '去设置',
            onPress: () => {
              if (Platform.OS === 'ios') {
                Linking.openURL('app-settings:');
              } else {
                Linking.openSettings();
              }
            }
          }
        ]
      );
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error requesting camera permission:', error);
    return false;
  }
};

/**
 * 请求媒体库权限
 * @returns Promise<boolean> 是否获得权限
 */
export const requestMediaLibraryPermission = async (): Promise<boolean> => {
  try {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert(
        '需要相册权限',
        '请在设置中允许访问相册以选择照片',
        [
          { text: '取消', style: 'cancel' },
          {
            text: '去设置',
            onPress: () => {
              if (Platform.OS === 'ios') {
                Linking.openURL('app-settings:');
              } else {
                Linking.openSettings();
              }
            }
          }
        ]
      );
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error requesting media library permission:', error);
    return false;
  }
};

/**
 * 显示图片选择器选项
 * @param onCamera 选择拍照的回调
 * @param onLibrary 选择相册的回调
 */
export const showImagePickerOptions = (
  onCamera: () => void,
  onLibrary: () => void
): void => {
  Alert.alert(
    '选择图片',
    '请选择图片来源',
    [
      { text: '取消', style: 'cancel' },
      { text: '拍照', onPress: onCamera },
      { text: '相册', onPress: onLibrary },
    ]
  );
};

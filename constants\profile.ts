import regionsData from './regions.json';
import occupationsData from './occupations.json';

/**
 * 地区选项常量 - 从JSON文件加载
 */
export const LOCATION_OPTIONS = regionsData.allRegions as readonly string[];

/**
 * 职业选项常量 - 从JSON文件加载
 */
export const OCCUPATION_OPTIONS = occupationsData.occupations as readonly string[];

/**
 * 性别选项常量
 */
export const GENDER_OPTIONS = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' },
  { label: '其他', value: 'other' },
] as const;

/**
 * 图片类型常量
 */
export const IMAGE_TYPES = {
  AVATAR: 'avatar',
  BACKGROUND: 'background',
} as const;

export type ImageType = typeof IMAGE_TYPES[keyof typeof IMAGE_TYPES];

/**
 * 图片选择配置
 */
export const IMAGE_PICKER_CONFIG = {
  [IMAGE_TYPES.AVATAR]: {
    aspect: [1, 1] as [number, number],
    quality: 0.8,
  },
  [IMAGE_TYPES.BACKGROUND]: {
    aspect: [16, 9] as [number, number],
    quality: 0.8,
  },
} as const;

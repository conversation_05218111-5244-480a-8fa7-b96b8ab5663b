import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { useVerifyEmail, useResendVerification } from '@/store/userStore';

export default function VerifyCodeScreen() {
  const router = useRouter();
  const { phone, email, type } = useLocalSearchParams<{
    phone?: string;
    email?: string;
    type?: 'phone' | 'email'
  }>();
  const [verifyCode, setVerifyCode] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(600);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const verifyEmailMutation = useVerifyEmail();
  const resendVerificationMutation = useResendVerification();

  const isEmailVerification = type === 'email' || email;
  const contactInfo = isEmailVerification ? email : phone;

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleCodeChange = (text: string, index: number) => {
    const newCode = [...verifyCode];
    newCode[index] = text;
    setVerifyCode(newCode);

    if (text && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {

    if (key === 'Backspace' && !verifyCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleResendCode = async () => {
    if (!contactInfo) {
      Alert.alert('错误', '联系方式不能为空');
      return;
    }

    setIsLoading(true);
    try {
      if (isEmailVerification) {
        await resendVerificationMutation.mutateAsync({ email: contactInfo });
        Alert.alert('成功', '验证码已重新发送至您的邮箱');
      } else {
        Alert.alert('成功', '验证码已重新发送至您的手机');
      }

      setCountdown(600);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error) {
      console.error('Resend verification error:', error);
      Alert.alert('错误', '发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    const code = verifyCode.join('');
    if (code.length !== 6) {
      Alert.alert('错误', '请输入完整的6位验证码');
      return;
    }

    if (!contactInfo) {
      Alert.alert('错误', '联系方式不能为空');
      return;
    }

    setIsLoading(true);
    try {
      if (isEmailVerification) {
        await verifyEmailMutation.mutateAsync({
          email: contactInfo,
          verificationCode: code
        });

        Alert.alert(
          '验证成功',
          '邮箱验证成功，您现在可以正常使用账户了',
          [
            {
              text: '确定',
              onPress: () => router.replace('/(tabs)')
            }
          ]
        );
      } else {

        Alert.alert('验证失败', '手机验证功能暂未实现');
      }
    } catch (error) {
      console.error('Verification error:', error);
      Alert.alert('验证失败', '验证码错误，请重新输入');
    } finally {
      setIsLoading(false);
    }
  };

  const formatContact = (contact: string, isEmail: boolean) => {
    if (!contact) return '';
    if (isEmail) {
      const [localPart, domain] = contact.split('@');
      if (localPart.length <= 3) return contact;
      return `${localPart.slice(0, 2)}***@${domain}`;
    } else {
      return contact.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
    }
  };

  const isCodeComplete = verifyCode.every(digit => digit !== '');

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <Icon name="arrow-left" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>输入验证码</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView 
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.subtitle}>
          验证码已发送至 {formatContact(contactInfo || '', !!isEmailVerification)}
        </Text>

        {/* Countdown Timer */}
        <View style={styles.countdownContainer}>
          <Text style={styles.countdownText}>
            {countdown > 0 ? `${countdown}s` : '00s'}
          </Text>
          <Text style={styles.countdownLabel}>
            {countdown > 0 ? '后可重新发送' : '验证码已过期'}
          </Text>
        </View>

        {/* Six Code Input Boxes */}
        <View style={styles.codeInputContainer}>
          {verifyCode.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => { inputRefs.current[index] = ref; }}
              style={[
                styles.codeInput,
                digit && styles.codeInputFilled
              ]}
              value={digit}
              onChangeText={(text) => handleCodeChange(text.slice(-1), index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="number-pad"
              maxLength={1}
              autoFocus={index === 0}
              selectTextOnFocus
            />
          ))}
        </View>

        <TouchableOpacity
          style={[styles.verifyButton, (!isCodeComplete || isLoading) && styles.verifyButtonDisabled]}
          onPress={handleVerifyCode}
          disabled={!isCodeComplete || isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.verifyButtonText}>验证并登录</Text>
          )}
        </TouchableOpacity>

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>没有收到验证码？</Text>
          <TouchableOpacity 
            onPress={handleResendCode}
            disabled={countdown > 0}
            style={[styles.resendButtonContainer, countdown > 0 && styles.resendButtonDisabled]}
          >
            <Text style={[styles.resendButton, countdown > 0 && styles.resendButtonTextDisabled]}>
              重新发送
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 40,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  countdownContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  countdownText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 8,
  },
  countdownLabel: {
    fontSize: 14,
    color: '#999',
  },
  codeInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
    paddingHorizontal: 10,
  },
  codeInput: {
    width: 45,
    height: 45,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  codeInputFilled: {
    borderColor: '#3498db',
    backgroundColor: '#f8f9ff',
  },
  verifyButton: {
    backgroundColor: '#3498db',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 30,
  },
  verifyButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  verifyButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  resendContainer: {
    alignItems: 'center',
  },
  resendText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 12,
  },
  resendButtonContainer: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  resendButton: {
    fontSize: 16,
    color: '#3498db',
    fontWeight: '600',
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendButtonTextDisabled: {
    color: '#bdc3c7',
  },
});

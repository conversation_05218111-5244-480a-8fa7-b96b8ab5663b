import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  Dimensions,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Thread } from '@/api/services/threadsService';

interface ThreadOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  thread: Thread | null;
  currentUserId: string;
  onEdit?: () => void;
  onShare?: () => void;
  onReport?: () => void;
  onDelete?: () => void;
}

const { height: screenHeight } = Dimensions.get('window');

export default function ThreadOptionsModal({
  visible,
  onClose,
  thread,
  currentUserId,
  onEdit,
  onShare,
  onReport,
  onDelete,
}: ThreadOptionsModalProps) {
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // 显示模态框
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // 隐藏模态框
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, slideAnim, backdropOpacity]);

  const handleBackdropPress = () => {
    onClose();
  };

  const handleEditPress = () => {
    onEdit?.();
    onClose();
  };

  const handleSharePress = () => {
    onShare?.();
    onClose();
  };

  const handleReportPress = () => {
    onReport?.();
    onClose();
  };

  const handleDeletePress = () => {
    onDelete?.();
    onClose();
  };

  if (!visible) {
    return null;
  }

  // 检查是否是帖子作者
  const isAuthor = thread?.user_id === currentUserId;

  return (
    <View style={styles.container}>
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View 
          style={[
            styles.backdrop,
            {
              opacity: backdropOpacity,
            },
          ]} 
        />
      </TouchableWithoutFeedback>
      
      <Animated.View 
        style={[
          styles.modalContainer,
          {
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* 拖拽指示器 */}
        <View style={styles.dragIndicator} />
        
        {/* 选项列表 */}
        <View style={styles.optionsContainer}>
          {/* 编辑选项 - 仅对作者可见 */}
          {isAuthor && (
            <TouchableOpacity style={styles.optionItem} onPress={handleEditPress}>
              <Ionicons name="create-outline" size={24} color="#333" />
              <Text style={styles.optionText}>编辑</Text>
            </TouchableOpacity>
          )}

          {/* 删除选项 - 仅对作者可见 */}
          {isAuthor && (
            <TouchableOpacity style={styles.optionItem} onPress={handleDeletePress}>
              <Ionicons name="trash-outline" size={24} color="#ff4757" />
              <Text style={[styles.optionText, { color: '#ff4757' }]}>删除</Text>
            </TouchableOpacity>
          )}

          {/* 分享选项 */}
          <TouchableOpacity style={styles.optionItem} onPress={handleSharePress}>
            <Ionicons name="share-outline" size={24} color="#333" />
            <Text style={styles.optionText}>分享</Text>
          </TouchableOpacity>
          
          {/* 举报选项 - 仅对非作者可见 */}
          {!isAuthor && (
            <TouchableOpacity style={styles.optionItem} onPress={handleReportPress}>
              <Ionicons name="flag-outline" size={24} color="#ff4757" />
              <Text style={[styles.optionText, { color: '#ff4757' }]}>举报</Text>
            </TouchableOpacity>
          )}
        </View>
        
        {/* 取消按钮 */}
        <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
          <Text style={styles.cancelText}>取消</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // 为iPhone底部安全区域留出空间
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#ddd',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 20,
  },
  optionsContainer: {
    paddingHorizontal: 20,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 16,
    fontWeight: '500',
  },
  cancelButton: {
    marginTop: 20,
    marginHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
});

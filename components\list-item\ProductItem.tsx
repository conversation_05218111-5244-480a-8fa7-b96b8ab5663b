import StarRating from '@/components/ui/StartRating';
import { SearchProduct } from '@/types/search-type';
import { getRatingScore } from '@/utils/commonUtils';
import { getProductImageUrlSync } from '@/utils/imageUtils';
import { Image } from 'expo-image';
import React, { useState } from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Icon from 'react-native-vector-icons/EvilIcons';


interface ProductItemProps {
    item: SearchProduct;
    onProductPress: (productId: string) => void;
}

const ProductItem: React.FC<ProductItemProps> = ({
    item,
    onProductPress
}) => {
    const [imageLoadError, setImageLoadError] = useState(false);
    const productId = item.id || '';
    const imageUrl = productId && !imageLoadError ? getProductImageUrlSync(productId) : '';

    return (
        <TouchableOpacity style={styles.productItem} onPress={() => onProductPress(productId)}>
            <View style={styles.productImageContainer}>
                {imageUrl ? (
                    <Image
                        source={{ uri: imageUrl }}
                        style={styles.productImage}
                        contentFit="contain"
                        transition={200}
                        onError={() => {
                            setImageLoadError(true);
                        }}
                    />
                ) : (
                    <View style={styles.productImagePlaceholder}>
                        <Icon name="image" size={24} color="#ccc" />
                    </View>
                )}
            </View>
            <View style={styles.productInfo}>
                <Text style={styles.productName} numberOfLines={2}>{item.name}</Text>
                <Text style={styles.productBrand}>{item.brand}</Text>
                <View style={styles.productRatingContainer}>
                    <StarRating
                        score={getRatingScore(item.rating)}
                    />
                    <Text style={styles.productReviewCount}>
                        {item.review_count ? `${item.review_count}条评论` : '暂无评论'}
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    );
};


const styles = StyleSheet.create({
    listContainer: {
        paddingHorizontal: 0,
    },
    productItem: {
        flexDirection: 'row',
        justifyContent: 'center',
        padding: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    productImageContainer: {
        width: 80,
        height: 80,
        borderRadius: 8,
        overflow: 'hidden',
    },
    productImage: {
        width: '100%',
        height: '100%',
    },
    productImagePlaceholder: {
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9f9f9',
    },
    productInfo: {
        flex: 1,
        marginLeft: 12,
        justifyContent: 'space-between',
    },
    productName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    productBrand: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
    },
    productRatingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        flexWrap: 'wrap',
    },
    productRating: {
        fontSize: 14,
        color: '#666',
        marginLeft: 4,
        marginRight: 12,
    },
    productReviewCount: {
        fontSize: 14,
        color: '#999',
        marginLeft: 8,
    },
    productCategoryContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    productCategory: {
        fontSize: 12,
        color: '#666',
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
    },
    loadingFooter: {
        paddingVertical: 20,
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: '#666',
    },
    endOfListContainer: {
        paddingVertical: 20,
        alignItems: 'center',
    },
    endOfListText: {
        fontSize: 14,
        color: '#999',
    },
});


export default ProductItem;
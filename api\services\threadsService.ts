import apiClient from '../apiClient';
import { logError, retryAsync } from '@/utils/errorUtils';

export interface Thread {
  _id: string;
  thread_id: string;
  user_id: string;
  title: string;
  content: string;
  images?: string[];
  parent_thread_id?: string;
  root_thread_id?: string;
  reply_to_user_id?: string;
  reply_level: number;
  liked_by: string[];
  bookmarked_by: string[];
  like_count: number;
  reply_count: number;
  bookmark_count: number;
  status: 'visible' | 'hidden' | 'deleted';
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
    bio?: string;
  };
  reply_to_user?: {
    id: string;
    username: string;
    avatar?: string;
  };
  replay_tread?:Thread[]
}

// getThreads
export interface ThreadListResponse {
  threads: Thread[];
  total: number;
  page: number;
  limit: number;
}

// /api/threads/:id
export interface ThreadDetailResponse {
  thread: Thread;
  comments: {
    items: Thread[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface CreateThreadRequest {
  title?: string; // 对于回复来说是可选的
  content: string;
  images?: string[];
  parent_thread_id?: string;
  reply_to_user_id?: string;
}

export interface UpdateThreadRequest {
  title?: string;
  content?: string;
  images?: string[];
  status?: 'visible' | 'hidden' | 'deleted';
}

export enum ThreadApi {
  Threads = "threads",
}

// 定义排序类型
export type ThreadSortBy = 'latest' | 'updated' | 'hot' | 'following';

// 获取所有帖子，支持分页和排序
const getThreads = async (page = 1, limit = 10, sortBy: ThreadSortBy = 'latest', userId?: string) => {
  try {
    const headers: Record<string, string> = {};

    // 如果是获取关注动态，需要传递用户认证
    if (sortBy === 'following' && userId) {
      headers['user-id'] = userId;
    }

    const response = await apiClient.get<ThreadListResponse>({
      url: `${ThreadApi.Threads}`,
      params: { page, limit, sort_by: sortBy },
      headers: Object.keys(headers).length > 0 ? headers : undefined,
    });
    return response;
  } catch (error) {
    console.error("Threads API error:", error);
    throw error;
  }
};

// 根据ID获取帖子详情，包含评论
const getThreadById = async (threadId: string, commentsPage = 1, commentsLimit = 10) => {
  try {
    const response = await apiClient.get<ThreadDetailResponse>({
      url: `${ThreadApi.Threads}/${threadId}`,
      params: { comments_page: commentsPage, comments_limit: commentsLimit },
    });
    return response;
  } catch (error) {
    console.error(`Thread API error for ID ${threadId}:`, error);
    throw error;
  }
};

// 根据用户ID获取该用户发布的帖子
const getThreadsByUserId = async (userId: string, page = 1, limit = 10) => {
  try {
    const response = await apiClient.get<ThreadListResponse>({
      url: `${ThreadApi.Threads}/users/${userId}/threads`,
      params: { page, limit },
    });
    return response;
  } catch (error) {
    console.error(`User threads API error for user ${userId}:`, error);
    throw error;
  }
};

// 获取用户点赞的帖子列表
const getUserLikedThreads = async (userId: string, page = 1, limit = 10) => {
  try {
    const response = await apiClient.get<ThreadListResponse>({
      url: `${ThreadApi.Threads}/users/${userId}/likes`,
      params: { page, limit },
    });
    return response;
  } catch (error) {
    console.error(`User liked threads API error for user ${userId}:`, error);
    throw error;
  }
};

// 获取用户收藏的帖子列表
const getUserBookmarkedThreads = async (userId: string, page = 1, limit = 10) => {
  try {
    const response = await apiClient.get<ThreadListResponse>({
      url: `${ThreadApi.Threads}/users/${userId}/bookmarks`,
      params: { page, limit },
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error(`User bookmarked threads API error for user ${userId}:`, error);
    throw error;
  }
};

// 创建新帖子
const createThread = async (threadData: CreateThreadRequest, userId: string) => {
  try {
    const response = await apiClient.post<Thread>({
      url: `${ThreadApi.Threads}`,
      data: threadData,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error("Thread creation API error:", error);
    throw error;
  }
};

// 更新帖子内容
const updateThread = async (threadId: string, threadData: UpdateThreadRequest, userId: string) => {
  try {
    const response = await apiClient.patch<Thread>({
      url: `${ThreadApi.Threads}/${threadId}`,
      data: threadData,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error(`Thread update API error for ID ${threadId}:`, error);
    throw error;
  }
};

// 删除帖子
const deleteThread = async (threadId: string, userId: string) => {
  try {
    const response = await apiClient.delete<{ success: boolean; message: string }>({
      url: `${ThreadApi.Threads}/${threadId}`,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error(`Thread delete API error for ID ${threadId}:`, error);
    throw error;
  }
};

// 切换帖子点赞状态
const toggleLike = async (threadId: string, userId: string) => {
  return retryAsync(async () => {
    try {
      const response = await apiClient.post<{ liked: boolean; like_count: number }>({
        url: `${ThreadApi.Threads}/${threadId}/like`,
        headers: { 'user-id': userId },
      });
      return response;
    } catch (error) {
      logError(error, `toggleLike - threadId: ${threadId}, userId: ${userId}`);
      throw error;
    }
  }, 2, 1000); // 最多重试2次，延迟1秒
};

// 切换帖子收藏状态
const toggleBookmark = async (threadId: string, userId: string) => {
  return retryAsync(async () => {
    try {
      const response = await apiClient.post<{ bookmarked: boolean; bookmark_count: number }>({
        url: `${ThreadApi.Threads}/${threadId}/bookmark`,
        headers: { 'user-id': userId },
      });
      return response;
    } catch (error) {
      logError(error, `toggleBookmark - threadId: ${threadId}, userId: ${userId}`);
      throw error;
    }
  }, 2, 1000); // 最多重试2次，延迟1秒
};

// 更新帖子状态（仅管理员）
const updateThreadStatus = async (threadId: string, status: 'visible' | 'hidden' | 'deleted') => {
  try {
    const response = await apiClient.patch<Thread>({
      url: `${ThreadApi.Threads}/${threadId}/status`,
      data: { status },
    });
    return response;
  } catch (error) {
    console.error(`Thread status update API error for ID ${threadId}:`, error);
    throw error;
  }
};

// 上传帖子图片
const uploadThreadImage = async (formData: FormData) => {
  try {
    const response = await apiClient.post<{ imageUrl: string; filename: string }>({
      url: `${ThreadApi.Threads}/upload-image`,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  } catch (error) {
    console.error("Thread image upload API error:", error);
    throw error;
  }
};

// Get user's comments (replies to threads)
const getUserComments = async (userId: string, page = 1, limit = 10) => {
  try {
    const response = await apiClient.get<ThreadListResponse>({
      url: `${ThreadApi.Threads}/users/${userId}/comments`,
      params: { page, limit },
    });
    return response;
  } catch (error) {
    console.error(`User comments API error for user ${userId}:`, error);
    throw error;
  }
};

export default {
  getThreads,
  getThreadById,
  getThreadsByUserId,
  getUserLikedThreads,
  getUserBookmarkedThreads,
  getUserComments,
  createThread,
  updateThread,
  deleteThread,
  toggleLike,
  toggleBookmark,
  updateThreadStatus,
  uploadThreadImage,
};

import { useRouter } from 'expo-router';
import React, { useCallback, useMemo } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { ConversationData } from '@/api/services/messageService';
import { useConversations, useUnreadCount } from '@/store/useMessages';
import { useUserInfo } from '@/store/userStore';
import { formatRelativeTime } from '@/utils/dateUtils';

interface ConversationItemProps {
  conversation: ConversationData;
  onPress: () => void;
}

const ConversationItem: React.FC<ConversationItemProps> = ({ conversation, onPress }) => {
  const { other_user, last_message_content, last_message_time, unread_count = 0 } = conversation;

  // 安全地获取用户信息
  const username = other_user?.username || '未知用户';
  const avatar = other_user?.avatar || 'https://via.placeholder.com/50x50/cccccc/ffffff?text=U';
  const lastContent = last_message_content || '暂无消息';
  const unreadCountNumber = typeof unread_count === 'number' ? unread_count : 0;

  return (
    <TouchableOpacity style={styles.conversationItem} onPress={onPress}>
      <View style={styles.avatarContainer}>
        <Image
          source={{ uri: avatar }}
          style={styles.avatar}
        />
        {unreadCountNumber > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadText}>
              {unreadCountNumber > 99 ? '99+' : unreadCountNumber.toString()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.conversationContent}>
        <View style={styles.conversationHeader}>
          <Text style={styles.username} numberOfLines={1}>
            {username}
          </Text>
          {last_message_time && (
            <Text style={styles.timestamp}>
              {formatRelativeTime(last_message_time)}
            </Text>
          )}
        </View>

        <Text 
          style={[
            styles.lastMessage,
            unreadCountNumber > 0 && styles.unreadMessage
          ]} 
          numberOfLines={2}
        >
          {lastContent}
        </Text>
      </View>

      <Icon name="chevron-right" size={20} color="#ccc" />
    </TouchableOpacity>
  );
};

export default function MessagesScreen() {
  const router = useRouter();
  const userInfo = useUserInfo();
  
  const {
    data: conversationsData,
    isLoading,
    isFetching,
    hasNextPage,
    fetchNextPage,
    refetch,
    error
  } = useConversations(userInfo?.id || '', !!userInfo?.id);

  const { data: unreadData } = useUnreadCount(userInfo?.id || '', !!userInfo?.id);

  const conversations = useMemo(() => {
    if (!conversationsData?.pages) return [];
    
    const allConversations = conversationsData.pages.flatMap(page => {
      // 确保 page.data 存在且有 items
      if (!page.data || !Array.isArray(page.data.items)) return [];
      return page.data.items;
    });
    
    // 过滤掉没有 other_user 的对话
    return allConversations.filter(conv => conv && conv.other_user);
  }, [conversationsData]);

  const handleConversationPress = useCallback((conversation: ConversationData) => {
    if (!conversation.other_user) {
      console.error('Invalid conversation data:', conversation);
      return;
    }
    
    router.push({
      pathname: '/(stack)/chat',
      params: {
        conversationId: conversation.conversation_id,
        otherUserId: conversation.other_user.id,
        otherUserName: conversation.other_user.username || '未知用户',
        otherUserAvatar: conversation.other_user.avatar || ''
      }
    });
  }, [router]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetching, fetchNextPage]);

  const renderConversationItem = useCallback(({ item }: { item: ConversationData }) => {
    // 确保数据完整性
    if (!item || !item.other_user) {
      return null;
    }
    
    return (
      <ConversationItem
        conversation={item}
        onPress={() => handleConversationPress(item)}
      />
    );
  }, [handleConversationPress]);

  const renderFooter = useCallback(() => {
    if (!isFetching) return null;
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color="#007AFF" />
      </View>
    );
  }, [isFetching]);

  const renderEmpty = useCallback(() => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.emptyTitle}>加载中...</Text>
        </View>
      );
    }
    
    return (
      <View style={styles.emptyContainer}>
        <Icon name="message-outline" size={64} color="#ccc" />
        <Text style={styles.emptyTitle}>暂无私信</Text>
        <Text style={styles.emptySubtitle}>开始与其他用户聊天吧</Text>
      </View>
    );
  }, [isLoading]);

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Icon name="arrow-left" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>私信</Text>
          <View style={styles.headerRight} />
        </View>
        
        <View style={styles.errorContainer}>
          <Icon name="alert-circle-outline" size={48} color="#ff4444" />
          <Text style={styles.errorText}>加载失败</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryText}>重试</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // 安全地获取未读数量
  const totalUnread = unreadData?.total_unread || 0;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Icon name="arrow-left" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>私信</Text>
        <View style={styles.headerRight}>
          {totalUnread > 0 && (
            <View style={styles.totalUnreadBadge}>
              <Text style={styles.totalUnreadText}>
                {totalUnread > 99 ? '99+' : totalUnread.toString()}
              </Text>
            </View>
          )}
        </View>
      </View>

      <FlatList
        data={conversations}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.conversation_id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
            colors={['#007AFF']}
          />
        }
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={conversations.length === 0 ? styles.emptyList : undefined}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingTop: 50,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerRight: {
    width: 24,
    alignItems: 'center',
  },
  totalUnreadBadge: {
    backgroundColor: '#ff4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  totalUnreadText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  conversationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
  },
  unreadBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#ff4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    borderWidth: 2,
    borderColor: '#fff',
  },
  unreadText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  conversationContent: {
    flex: 1,
    marginRight: 8,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  username: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  unreadMessage: {
    fontWeight: '500',
    color: '#333',
  },
  loadingFooter: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#333',
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

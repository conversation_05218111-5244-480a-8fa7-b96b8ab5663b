import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

interface StackHeaderProps {
    title?: string;
    rightComponent?: React.ReactNode;
    blank?: boolean;
}

export default function StackHeader({ title,
    rightComponent = null,
    blank = false
}: StackHeaderProps) {
    const router = useRouter();
    return (
        <View style={[styles.header, blank && styles.blankHeader]}>
            <TouchableOpacity onPress={() => router.back()}>
                <Ionicons name="chevron-back" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, blank && styles.blankText]}>{title}</Text>
            <View style={[styles.rightContainer, blank && styles.blankContainer]}>
                {rightComponent}
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 12,
        borderBottomColor: '#E9ECEF',
        borderBottomWidth: 1,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,

    },
    headerTitle: {
        flex: 1,
        fontSize: 13,
        color: 'black',
        textAlign: 'center',
    },
    rightContainer: {
        width: 24,
        alignItems: 'center',
    },
    blankHeader: {
        backgroundColor: 'transparent',
        borderBottomWidth: 0,
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 15,
        height: 120,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 100,
    },
    blankText: {
        opacity: 0,
    },
    blankContainer: {
        opacity: 0,
    }
});

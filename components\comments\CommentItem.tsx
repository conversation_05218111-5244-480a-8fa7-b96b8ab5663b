import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Thread } from '@/api/services/threadsService';
import { getAvatarUrl } from '@/utils/userUtils';
import { buildImageUrl } from '@/utils/imageUtils';

interface CommentItemProps {
  comment: Thread;
  onPress?: () => void;
  onUserPress?: () => void;
  onLike?: () => void;
  isLiking?: boolean;
}

export default function CommentItem({
  comment,
  onPress,
  onUserPress,
  onLike,
  isLiking = false,
}: CommentItemProps) {
  const isLiked = comment.liked_by?.includes(comment.user.id) || false;

  // 本地时间格式化函数
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* 用户信息 */}
      <TouchableOpacity
        style={styles.userInfo}
        onPress={onUserPress}
        activeOpacity={0.7}
      >
        <Image
          source={{ uri: getAvatarUrl(comment.user.avatar) }}
          style={styles.avatar}
        />
        <View style={styles.userDetails}>
          <Text style={styles.username}>{comment.user.username}</Text>
          <Text style={styles.timeAgo}>{formatTimeAgo(comment.created_at)}</Text>
        </View>
      </TouchableOpacity>

      {/* 评论内容 */}
      <View style={styles.contentContainer}>
        <Text style={styles.content} numberOfLines={3}>
          {comment.content}
        </Text>

        {/* 如果有图片，显示第一张图片 */}
        {comment.images && comment.images.length > 0 && (
          <Image
            source={{ uri: buildImageUrl(comment.images[0]) }}
            style={styles.commentImage}
            resizeMode="cover"
          />
        )}
      </View>

      {/* 操作按钮 */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onLike}
          disabled={isLiking}
        >
          <Ionicons
            name={isLiked ? "heart" : "heart-outline"}
            size={18}
            color={isLiked ? "#ff4757" : "#666"}
          />
          <Text style={[styles.actionText, isLiked && styles.likedText]}>
            {comment.like_count || 0}
          </Text>
        </TouchableOpacity>

        <View style={styles.actionButton}>
          <Ionicons name="chatbubble-outline" size={18} color="#666" />
          <Text style={styles.actionText}>{comment.reply_count || 0}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  timeAgo: {
    fontSize: 12,
    color: '#666',
  },
  contentContainer: {
    marginBottom: 12,
  },
  content: {
    fontSize: 15,
    lineHeight: 22,
    color: '#333',
    marginBottom: 8,
  },
  commentImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginTop: 8,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  actionText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  likedText: {
    color: '#ff4757',
  },
});

import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useState } from 'react';
import { StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
export default function SearchButton() {
    const [text, onChangeText] = useState('');

    const handleSearchPress = () => {
        router.push('/search');
    };

    return (
        <TouchableOpacity
            style={styles.searchContainer}
            onPress={handleSearchPress}
        >
            <View style={styles.searchInput}>
                <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
                <TextInput
                    style={styles.textInput}
                    onChangeText={onChangeText}
                    value={text}
                    placeholder="搜索宠粮、品牌、帖子..."
                    placeholderTextColor="#999"
                    editable={false}
                    pointerEvents="none"
                />
            </View>
        </TouchableOpacity>
    )
}

const styles = StyleSheet.create({
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        width:'75%',
        marginRight: 8
    },
    searchInput: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#E0E0E0',
        borderRadius: 12,
        paddingHorizontal: 12,
        height: 36
    },
    searchIcon: {
        marginRight: 8,
    },
    textInput: {
        flex: 1,
        fontSize: 14,
        color: '#333',
        padding: 0,
    }
});
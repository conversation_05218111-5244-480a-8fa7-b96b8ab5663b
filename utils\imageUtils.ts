import { ImageSourcePropType, Platform } from 'react-native';

// Default placeholder image
export const PLACEHOLDER_IMAGE = require('@/assets/images/placeholder.png');

// List of domains known to have CORS restrictions
const CORS_RESTRICTED_DOMAINS = [
  'catfooddb.com',
  'cors-restricted-domain.com'
];

/**
 * Checks if a URL is likely to have CORS issues
 * @param url The image URL to check
 * @returns True if the URL is likely to have CORS issues
 */
export const isCORSRestrictedURL = (url: string): boolean => {
  if (!url) return true;
  
  // Check if the URL contains any of the known problematic domains
  return CORS_RESTRICTED_DOMAINS.some(domain => url.includes(domain));
};

/**
 * Gets a safe image source that handles CORS issues
 * @param uri The original image URI
 * @param fallback Optional custom fallback image
 * @returns An image source that will work even with CORS restrictions
 */
export const getSafeImageSource = (
  uri?: string, 
  fallback: ImageSourcePropType = PLACEHOLDER_IMAGE
): ImageSourcePropType => {
  // If no URI or empty URI, return fallback
  if (!uri || uri.trim() === '') {
    return fallback;
  }

  // On web, we need to handle CORS differently than on native
  if (Platform.OS === 'web' && isCORSRestrictedURL(uri)) {
    // For web, we could try to use a CORS proxy (commented out as it requires server setup)
    // return { uri: `https://your-cors-proxy.com/?url=${encodeURIComponent(uri)}` };
    
    // For now, just return the fallback
    return fallback;
  }

  // For native platforms or non-CORS-restricted URLs, return the original URI
  return { uri };
};

const IMAGE_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

/**
 * 获取产品图片URL（同步版本）
 * @param productId 产品ID
 * @param extension 图片扩展名，默认为png
 * @returns 完整的图片URL
 */
export const getProductImageUrlSync = (productId: string, extension: string = 'png'): string => {
  if (!productId) return '';
  return `${IMAGE_BASE_URL}/assets/product_iamges/${productId}.${extension}`;
};

/**
 * 获取品牌logo URL（同步版本）
 * @param brandId 品牌ID
 * @param extension 图片扩展名，默认为png
 * @returns 完整的图片URL
 */
export const getBrandLogoUrlSync = (brandId: string, extension: string = 'png'): string => {
  if (!brandId) return '';
  return `${IMAGE_BASE_URL}/assets/brand_logo/${brandId}.${extension}`;
};

/**
 * 获取用户头像URL（同步版本）
 * @param userId 用户ID
 * @param extension 图片扩展名，默认为jpg
 * @returns 完整的图片URL
 */
export const getUserAvatarUrlSync = (userId: string, extension: string = 'jpg'): string => {
  if (!userId) return '';
  return `${IMAGE_BASE_URL}/uploads/users/${userId}_avatar.${extension}`;
};

/**
 * 获取用户背景图片URL（同步版本）
 * @param userId 用户ID
 * @param extension 图片扩展名，默认为jpg
 * @returns 完整的图片URL
 */
export const getUserBackgroundUrlSync = (userId: string, extension: string = 'jpg'): string => {
  if (!userId) return '';
  return `${IMAGE_BASE_URL}/uploads/users/${userId}_background.${extension}`;
};

/**
 * 从API返回的相对路径构建完整URL
 * @param relativePath API返回的相对路径
 * @returns 完整的图片URL
 */
export const buildImageUrl = (relativePath: string): string => {
  if (!relativePath) return '';
  if (relativePath.startsWith('http')) return relativePath;

  // 确保路径不以斜杠开头，避免双斜杠
  const cleanPath = relativePath.startsWith('/') ? relativePath.slice(1) : relativePath;
  const fullUrl = `${IMAGE_BASE_URL}/${cleanPath}`;

  return fullUrl;
};

/**
 * 检查是否为有效的图片URL
 * @param url 图片URL
 * @returns 是否为有效URL
 */
export const isValidImageUrl = (url: string): boolean => {
  if (!url) return false;
  return url.startsWith(IMAGE_BASE_URL) || url.startsWith('http');
};

/**
 * 获取图片基础URL
 */
export const getImageBaseUrl = (): string => {
  return IMAGE_BASE_URL;
};

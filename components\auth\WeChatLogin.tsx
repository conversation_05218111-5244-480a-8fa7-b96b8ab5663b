import React from 'react';
import { ActivityIndicator, Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { BrandBox } from '../ui/BrandBox';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
interface WeChatLoginProps {
  isLoading: boolean;
  onWeChatLogin: () => void;
  onPhoneLogin: () => void;
}

export default function WeChatLogin({ isLoading, onWeChatLogin, onPhoneLogin }: WeChatLoginProps) {
  return (
    <View style={styles.wechatWrapper}>
      <View style={styles.header}>
        <BrandBox />
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.wechatButton, isLoading && styles.wechatButtonLoading]}
          onPress={onWeChatLogin}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Icon name="wechat" size={24} color="#fff" style={styles.buttonIcon} />
              <Text style={styles.wechatButtonText}>微信登录</Text>
            </>
          )}
        </TouchableOpacity>

        <TouchableOpacity style={styles.phoneButton} onPress={onPhoneLogin}>
          <Icon name="phone-outline" size={20} color="#666" style={styles.buttonIcon} />
          <Text style={styles.phoneButtonText}>手机号登录</Text>
        </TouchableOpacity>
      </View>

    </View>
  );
}

const styles = StyleSheet.create({
  wechatWrapper: {
    flex: 1,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    justifyContent: 'center',
    minHeight: screenHeight * 0.4, // 头部区域占40%屏幕高度
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#999',
  },
  buttonContainer: {
    paddingBottom: Math.max(20, screenHeight * 0.05), // 响应式底部内边距
  },
  wechatButton: {
    backgroundColor: '#07C160',
    paddingVertical: Math.max(16, screenHeight * 0.02),
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    shadowColor: '#07C160',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  wechatButtonLoading: {
    backgroundColor: '#05A050',
  },
  wechatButtonText: {
    color: '#fff',
    fontSize: Math.max(15, screenWidth * 0.04),
    fontWeight: '600',
  },
  phoneButton: {
    backgroundColor: '#fff',
    paddingVertical: Math.max(16, screenHeight * 0.02),
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    flexDirection: 'row',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  phoneButtonText: {
    color: '#666',
    fontSize: Math.max(15, screenWidth * 0.04),
    fontWeight: '600',
  },
  buttonIcon: {
    marginRight: 8,
  },
});

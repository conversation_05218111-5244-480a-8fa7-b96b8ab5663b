import {
    StyleSheet,
    Text,
    View
} from 'react-native';
import { getResponsiveFontSize, getResponsiveHeight, getResponsivePadding } from '@/utils/responsive';

export const BrandBox: React.FC = () => {
    return (
        <View style={styles.Container}>
            <Text style={styles.title}>好宠粮</Text>
            <Text style={styles.loadingText}>宠物的健康指南</Text>
        </View>
    )
}

const styles = StyleSheet.create({
    Container: {
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: Math.max(20, getResponsiveHeight(3)),
        minHeight: getResponsiveHeight(25), // 最小高度为屏幕高度的25%
    },
    title: {
        fontSize: getResponsiveFontSize(48), // 从57减少到48，更适合移动端
        fontWeight: 'bold',
        color: '#006400',
        textAlign: 'center',
        fontFamily: 'monospace',
        lineHeight: getResponsiveFontSize(48) * 1.2,
    },
    loadingText: {
        fontSize: getResponsiveFontSize(16), // 从18减少到16
        marginTop: 10,
        color: '#4A4A4A',
        textAlign: 'center',
        lineHeight: getResponsiveFontSize(16) * 1.4,
    },
});

export default BrandBox
import { IMAGE_PICKER_CONFIG, ImageType } from '@/constants/profile';
import { uploadUserImage } from '@/utils/fileSystemUpload';
import { buildImageUrl } from '@/utils/imageUtils';
import {
  requestCameraPermission,
  requestMediaLibraryPermission,
  showImagePickerOptions
} from '@/utils/permissions';
import * as ImagePicker from 'expo-image-picker';
import { useState } from 'react';
import { Alert } from 'react-native';

interface UseImagePickerProps {
  userId: string;
  onImageUpdate: (type: ImageType, imageUrl: string) => void;
  onLoadingChange: (type: ImageType, isLoading: boolean) => void;
}

interface UseImagePickerReturn {
  isUploading: Record<ImageType, boolean>;
  showImagePicker: (type: ImageType) => void;
}

export const useImagePicker = ({
  userId,
  onImageUpdate,
  onLoadingChange,
}: UseImagePickerProps): UseImagePickerReturn => {
  const [isUploading, setIsUploading] = useState<Record<ImageType, boolean>>({
    avatar: false,
    background: false,
  });

  /**
   * 更新上传状态
   */
  const updateUploadingState = (type: ImageType, loading: boolean) => {
    setIsUploading(prev => ({ ...prev, [type]: loading }));
    onLoadingChange(type, loading);
  };

  /**
   * 拍照
   */
  const takePicture = async (type: ImageType) => {
    const hasCameraPermission = await requestCameraPermission();
    if (!hasCameraPermission) return;

    try {
      const config = IMAGE_PICKER_CONFIG[type];
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: config.aspect,
        quality: config.quality,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (!asset.uri) {
          Alert.alert('错误', '无法获取图片信息，请重试。');
          return;
        }
        await uploadImage(asset, type);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('错误', '拍照时出现错误，请重试。');
    }
  };

  /**
   * 从相册选择图片
   */
  const pickFromLibrary = async (type: ImageType) => {
    const hasPermission = await requestMediaLibraryPermission();
    if (!hasPermission) return;

    try {
      const config = IMAGE_PICKER_CONFIG[type];
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: config.aspect,
        quality: config.quality,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (!asset.uri) {
          Alert.alert('错误', '无法获取图片信息，请重试。');
          return;
        }
        await uploadImage(asset, type);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('错误', '选择图片时出现错误，请重试。');
    }
  };

  /**
   * 上传图片
   */
  const uploadImage = async (asset: ImagePicker.ImagePickerAsset, type: ImageType) => {
    try {
      updateUploadingState(type, true);

      // 立即显示本地预览
      onImageUpdate(type, asset.uri);

      // 使用 Expo FileSystem 上传图片
      const result = await uploadUserImage(asset, type, userId);

      // 只传递相对路径，不构建完整URL
      // 这样存储到数据库的就是相对路径，避免IP变化导致图片失效
      onImageUpdate(type, result.imageUrl);
      Alert.alert('成功', '图片上传成功');

    } catch (error: any) {
      console.error('Image upload error:', error);
      Alert.alert('提示', '图片已预览，但上传至服务器失败，您仍可保存个人资料');
    } finally {
      updateUploadingState(type, false);
    }
  };

  /**
   * 显示图片选择器
   */
  const showImagePicker = (type: ImageType) => {
    showImagePickerOptions(
      () => takePicture(type),
      () => pickFromLibrary(type)
    );
  };

  return {
    isUploading,
    showImagePicker,
  };
};

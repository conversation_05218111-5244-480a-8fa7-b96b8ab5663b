import AsyncStorage from '@react-native-async-storage/async-storage';
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Alert } from "react-native";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import userService from "@/api/services/userService";
import type { UserInfo, UserToken } from "@/types/entity";
import { StorageEnum } from "@/types/enum";

type UserStore = {
	userInfo: Partial<UserInfo>;
	userToken: UserToken;
	actions: {
		setUserInfo: (userInfo: UserInfo) => void;
		setUserToken: (token: UserToken) => void;
		clearUserInfoAndToken: () => void;
	};
};

const useUserStore = create<UserStore>()(
	persist(
		(set) => ({
			userInfo: {},
			userToken: {},
			actions: {
				setUserInfo: (userInfo) => {
					set({ userInfo });
				},
				setUserToken: (userToken) => {
					set({ userToken });
				},
				clearUserInfoAndToken() {
					set({ userInfo: {}, userToken: {} });
				}
			},
		}),
		{
			name: "userStore",
			storage: createJSONStorage(() => AsyncStorage),
			partialize: (state) => ({
				[StorageEnum.UserInfo]: state.userInfo,
				[StorageEnum.UserToken]: state.userToken,
			}),
		},
	),
);

export const useUserInfo = () => useUserStore((state) => state.userInfo);
export const useUserToken = () => useUserStore((state) => state.userToken);
export const useUserActions = () => useUserStore((state) => state.actions);

export const useUserDetail = (userId?: string) => {
	const { data: apiResponse, isLoading, error } = useQuery<UserInfo>({
		queryKey: ['user', userId],
		queryFn: () => userService.getUserById(userId || ''),
		enabled: !!userId,
		staleTime: 5 * 60 * 1000,
	});

	return {
		user: apiResponse,
		isLoading,
		error,
		apiResponse,
	};
};


export const useUpdateUser = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ id, data }: { id: string; data: Partial<UserInfo> }) => userService.updateUser(id, data),
		onSuccess: () => {
			Alert.alert("成功", "用户信息更新成功");
			queryClient.invalidateQueries({ queryKey: ["users"] });
		},
		onError: (error: any) => {
			const message = error?.message || "更新错误";
			Alert.alert("错误", message);
		},
	});
};

export const useSignIn = () => {
	const queryClient = useQueryClient();
	const { setUserToken, setUserInfo } = useUserActions();

	return useMutation({
		mutationFn: userService.signin,
		onSuccess: (res) => {
			const { user, accessToken, refreshToken } = res;

			// 设置用户信息和令牌
			setUserToken({ accessToken, refreshToken });
			setUserInfo(user);

			// 清除相关查询缓存
			queryClient.invalidateQueries({ queryKey: ["users"] });
			queryClient.invalidateQueries({ queryKey: ["user", user.id] });
		},
		onError: (error: any) => {
			console.error('Login error:', error);
			const message = error?.response?.data?.message || error?.message || "请检查您的登录信息";
			Alert.alert("登录失败", message);
		},
	});
};

/**
 * 登出Hook
 */
export const useSignOut = () => {
	const queryClient = useQueryClient();
	const { clearUserInfoAndToken } = useUserActions();

	return () => {
		// 清除用户信息和令牌
		clearUserInfoAndToken();

		// 清除所有查询缓存
		queryClient.clear();

		// 不需要手动跳转，AuthGuard会自动处理重定向
		Alert.alert("提示", "您已成功登出");
	};
};

export const useSignUp = () => {
	return useMutation({
		mutationFn: userService.signup,
		onSuccess: () => {
			// 不在这里显示成功消息，让组件自己处理
			console.log("Registration successful");
		},
		onError: (error: any) => {
			const message = error?.message || "注册过程中出现错误";
			Alert.alert("注册失败", message);
		},
	});
};

export const useVerifyEmail = () => {
	const queryClient = useQueryClient();
	const { setUserToken, setUserInfo } = useUserActions();

	return useMutation({
		mutationFn: userService.verifyEmail,
		onSuccess: (res) => {
			const { user, accessToken, refreshToken } = res;

			// 设置用户信息和令牌
			setUserToken({ accessToken, refreshToken });
			setUserInfo(user);

			// 清除相关查询缓存
			queryClient.invalidateQueries({ queryKey: ["users"] });
			queryClient.invalidateQueries({ queryKey: ["user", user.id] });
		},
		onError: (error: any) => {
			console.error('Email verification error:', error);
			const message = error?.response?.data?.message || error?.message || "验证失败，请检查验证码";
			Alert.alert("验证失败", message);
		},
	});
};

export const useResendVerification = () => {
	return useMutation({
		mutationFn: userService.resendVerification,
		onSuccess: () => {
			// 不在这里显示成功消息，让组件自己处理
			console.log("Verification code resent successfully");
		},
		onError: (error: any) => {
			const message = error?.message || "重发验证码失败";
			Alert.alert("错误", message);
		},
	});
};

export const useVerifyToken = () => {
	return useMutation({
		mutationFn: userService.verifyToken,
		onError: (error: any) => {
			console.log("Token verification failed:", error);
		},
	});
};

export const useUpdateUserProfile = () => {
	const queryClient = useQueryClient();
	const { setUserInfo } = useUserActions();
	const currentUserInfo = useUserInfo();

	return useMutation({
		mutationFn: ({ id, data }: { id: string; data: Partial<UserInfo> }) => 
			userService.updateUserProfile(id, data),
		onSuccess: (updatedUser) => {
			if (updatedUser.id === currentUserInfo.id) {
				setUserInfo(updatedUser);
			}
			queryClient.invalidateQueries({ queryKey: ["users"] });
			queryClient.invalidateQueries({ queryKey: ["user", updatedUser.id] });
		},
		onError: (error: any) => {
			console.error('Profile update error:', error);
			throw error;
		},
	});
};

export const useUploadImage = () => {
	return useMutation({
		mutationFn: userService.uploadImage,
		onError: (error: any) => {
			console.error('Image upload error:', error);
			throw error; 
		},
	});
};

export const useCurrentUserData = () => {
	const userInfo = useUserInfo();
	const userToken = useUserToken();
	const isLoggedIn = !!userInfo.id && !!userToken.accessToken;

	const query = useQuery<UserInfo>({
		queryKey: ['user', userInfo.id],
		queryFn: () => userService.getUserById(userInfo.id!),
		enabled: isLoggedIn && !!userInfo.id,
		staleTime: 5 * 60 * 1000,
	});


	return query;
};

export default useUserStore;

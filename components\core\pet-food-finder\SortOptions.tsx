import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SORT_OPTIONS } from '@/types/pet-food-filter';

interface SortOptionsProps {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sortBy: string, sortOrder?: 'asc' | 'desc') => void;
}

const SortOptions: React.FC<SortOptionsProps> = ({
  sortBy,
  sortOrder,
  onSortChange,
}) => {
  const handleSortPress = (optionKey: string) => {
    if (sortBy === optionKey) {
      // 如果是当前排序，切换排序方向
      const newOrder = sortOrder === 'desc' ? 'asc' : 'desc';
      onSortChange(optionKey, newOrder);
    } else {
      // 如果是新的排序，使用默认方向
      onSortChange(optionKey);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>排序方式</Text>
      <View style={styles.optionsContainer}>
        {SORT_OPTIONS.map((option) => {
          const isActive = sortBy === option.key;
          const showOrder = isActive && (
            option.key === 'quality' || 
            option.key === 'protein' || 
            option.key === 'fat' || 
            option.key === 'rating' || 
            option.key === 'reviews'
          );
          
          return (
            <TouchableOpacity
              key={option.key}
              style={[styles.option, isActive && styles.activeOption]}
              onPress={() => handleSortPress(option.key)}
            >
              <Text style={[styles.optionText, isActive && styles.activeOptionText]}>
                {option.label}
              </Text>
              {isActive && showOrder && (
                <Ionicons
                  name={sortOrder === 'desc' ? 'arrow-down' : 'arrow-up'}
                  size={16}
                  color="#4d920f"
                />
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#f8f9fa',
    gap: 4,
  },
  activeOption: {
    backgroundColor: '#4d920f',
    borderColor: '#4d920f',
  },
  optionText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeOptionText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default SortOptions;

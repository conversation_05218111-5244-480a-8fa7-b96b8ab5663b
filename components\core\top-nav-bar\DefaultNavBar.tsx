import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';

import ExtendSideBarButton from './ExtendSideBarButton';

import PrivateMessageButton from './PrivateMessageButton';
import SearchButton from './SearchButton';

export default function DefaultNavBar() {
  return (
    <View style={styles.container}>
      <ExtendSideBarButton />
      <SearchButton />
      <PrivateMessageButton />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 48 : 36,
    paddingVertical: 15,
    width: '100%',
    height: 100,
    zIndex: 100,
  },
});

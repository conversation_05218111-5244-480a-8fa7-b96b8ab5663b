import RatingSection from '@/components/ui/RatingSection';
import GlassBottle from '@/components/ui/GlassBottle';
import { useProductReviewSummary } from '@/store/productRatingStore';
import { getProductImageUrlSync } from '@/utils/imageUtils';
import React, { useState } from 'react';
import { Pressable, ScrollView, StyleSheet, Text, View, Dimensions } from 'react-native';
import { Image } from 'expo-image';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface ProductDetailHeaderProps {
  product: {
    _id: string;
    name: string;
    brand: string;
    image_url?: string;
    product_type?: string;
    est_calories?: string;
    guaranteed_analysis?: {
      protein?: string;
      fat?: string;
      fiber?: string;
      moisture?: string;
      carbs?: string;
      ash?: string;
    };
    dry_matter_analysis?: {
      protein?: string;
      fat?: string;
      fiber?: string;
      carbs?: string;
      ash?: string;
      calories?: string;
    };
    quality_ingredients?: string[];
    questionable_ingredients?: string[];
    allergen_ingredients?: string[];
  };
}
const { width: screenWidth, height: screenHeight } = Dimensions.get('window')
export default function ProductDetailHeader({ product }: ProductDetailHeaderProps) {
  const [activeAnalysisTab, setActiveAnalysisTab] = useState<'guaranteed' | 'dryMatter'>('guaranteed');
  const [imageLoadError, setImageLoadError] = useState(false);

  // 获取产品评论摘要
  const { data: summary } = useProductReviewSummary(product._id);

  const getImageUrl = (): string => {
    if (!imageLoadError && product._id) {
      return getProductImageUrlSync(product._id);
    }
    return product.image_url || ""
  };

  const handleImageError = () => {
    setImageLoadError(true);
  };



  const renderAnalysisContent = () => {
    const guaranteedAnalysis = product.guaranteed_analysis || {};
    const dryMatterAnalysis = product.dry_matter_analysis || {};

    if (activeAnalysisTab === 'guaranteed') {
      return (
        <View style={styles.analysisContainer}>
          <AnalysisItem
            label="蛋白质"
            value={guaranteedAnalysis.protein || 'N/A'}
            icon="bee"
            color="#4CAF50"
          />
          <AnalysisItem
            label="脂肪"
            value={guaranteedAnalysis.fat || 'N/A'}
            icon="oil"
            color="#FF9800"
          />
          <AnalysisItem
            label="纤维"
            value={guaranteedAnalysis.fiber || 'N/A'}
            icon="grain"
            color="#9C27B0"
          />
          <AnalysisItem
            label="碳水化合物"
            value={guaranteedAnalysis.carbs || 'N/A'}
            icon="barley"
            color="#3F51B5"
          />
          <AnalysisItem
            label="灰分"
            value={guaranteedAnalysis.ash || 'N/A'}
            icon="flask"
            color="#607D8B"
          />
          <AnalysisItem
            label="水分"
            value={guaranteedAnalysis.moisture || 'N/A'}
            icon="water"
            color="#00BCD4"
          />
        </View>
      );
    } else {
      return (
        <View style={styles.analysisContainer}>
          <AnalysisItem
            label="蛋白质"
            value={dryMatterAnalysis.protein || 'N/A'}
            icon="bee"
            color="#4CAF50"
          />
          <AnalysisItem
            label="脂肪"
            value={dryMatterAnalysis.fat || 'N/A'}
            icon="oil"
            color="#FF9800"
          />
          <AnalysisItem
            label="纤维"
            value={dryMatterAnalysis.fiber || 'N/A'}
            icon="grain"
            color="#9C27B0"
          />
          <AnalysisItem
            label="碳水化合物"
            value={dryMatterAnalysis.carbs || 'N/A'}
            icon="barley"
            color="#3F51B5"
          />
          <AnalysisItem
            label="灰分"
            value={dryMatterAnalysis.ash || 'N/A'}
            icon="flask"
            color="#607D8B"
          />
          <AnalysisItem
            label="卡路里"
            value={dryMatterAnalysis.calories || 'N/A'}
            icon="fire"
            color="#FF5722"
          />
        </View>
      );
    }
  };
  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.heroWrapper}>
        <View style={styles.heroContainer}>
          <Image
            source={{ uri: getImageUrl() }}
            style={styles.productImage}
            contentFit='contain'
            onError={handleImageError}

          />
          <View style={styles.heroTitleContainer}>
            <Text style={styles.heroMainTitle}>
              伊乔斯坦 德玛
            </Text>
            <Text style={styles.heroThinTitle} numberOfLines={2} ellipsizeMode="tail">
              {product.name.replace(new RegExp(`^${product.brand}\\s*`, 'i'), "")}
            </Text>
          </View>
        </View>

        <View style={styles.heroHubContainer}>
          {/* 用户评分 Section */}
          {summary && summary.review_count > 0 && (
            <RatingSection
              summary={summary}
              showReviewTypes={false}
            />
          )}
        </View>

        <View style={styles.heroAttributeContainer}>
          <View style={styles.attributeCol}>
            <Text style={styles.attributeText}>品牌: {product.brand}</Text>
            <Text style={styles.attributeText}>种类: {product.product_type === "Cat Food" ? '猫粮' : '狗粮'}</Text>
          </View>
          <View style={styles.attributeCol}>
            <Text style={styles.attributeText}>热量: {product.est_calories || 'N/A'}</Text>
            <Text style={styles.attributeText}>评分: {summary?.average_rating?.toFixed(1) || 'N/A'}</Text>
          </View>
        </View>
      </View>

      {/* Nutrition Analysis Section */}
      <View style={styles.card}>
        <View style={styles.tabContainer}>
          <Pressable
            style={[styles.tab, activeAnalysisTab === 'guaranteed' && styles.activeTab]}
            onPress={() => setActiveAnalysisTab('guaranteed')}
          >
            <Text style={activeAnalysisTab === 'guaranteed' ? styles.activeTabText : styles.tabText}>
              保证分析
            </Text>
          </Pressable>
          <Pressable
            style={[styles.tab, activeAnalysisTab === 'dryMatter' && styles.activeTab]}
            onPress={() => setActiveAnalysisTab('dryMatter')}
          >
            <Text style={activeAnalysisTab === 'dryMatter' ? styles.activeTabText : styles.tabText}>
              干物质分析
            </Text>
          </Pressable>
        </View>

        {renderAnalysisContent()}
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>成分评价</Text>

        <View style={styles.ingredientSection}>
          <View style={styles.ingredientHeader}>
            <Icon name="check-circle" size={20} color="#4CAF50" />
            <Text style={styles.ingredientTitle}>优质成分</Text>
          </View>
          <View style={styles.ingredientsList}>
            {(product.quality_ingredients || []).map((ingredient, index) => (
              <View key={index} style={styles.ingredientItem}>
                <Icon name="circle-small" size={20} color="#4CAF50" />
                <Text style={styles.ingredientText}>{ingredient}</Text>
              </View>
            ))}
          </View>
        </View>

        {(product.questionable_ingredients || []).length > 0 && (
          <View style={styles.ingredientSection}>
            <View style={styles.ingredientHeader}>
              <Icon name="alert-circle" size={20} color="#FFC107" />
              <Text style={styles.ingredientTitle}>可疑成分</Text>
            </View>
            <View style={styles.ingredientsList}>
              {(product.questionable_ingredients || []).map((ingredient, index) => (
                <View key={index} style={styles.ingredientItem}>
                  <Icon name="circle-small" size={20} color="#FFC107" />
                  <Text style={styles.ingredientText}>{ingredient}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        <View style={styles.ingredientSection}>
          <View style={styles.ingredientHeader}>
            <Icon name="alert-octagon" size={20} color="#F44336" />
            <Text style={styles.ingredientTitle}>潜在过敏原</Text>
          </View>
          <View style={styles.ingredientsList}>
            {(product.allergen_ingredients || []).map((ingredient, index) => (
              <View key={index} style={styles.ingredientItem}>
                <Icon name="circle-small" size={20} color="#F44336" />
                <Text style={styles.ingredientText}>{ingredient}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>

    </ScrollView>
  );
}

const AnalysisItem = ({ label, value, icon, color }: { label: string; value: string; icon: string; color: string }) => {
  return (
    <View style={styles.analysisItemContainer}>
      <View style={styles.analysisItem}>
        <View style={styles.analysisItemTitle}>
          <Icon name={icon} size={16} color={color} style={styles.analysisIcon} />
          <Text style={styles.analysisLabel}>{label}</Text>
        </View>
        <View style={styles.analysisItemContent}>
          <GlassBottle value={value} color={color} />
          <Text style={[styles.analysisValue, { color }]}>{value}</Text>
        </View>
      </View>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  heroHubContainer: {
    paddingHorizontal: screenWidth * 0.02,
  },
  heroWrapper: {
    height: 'auto',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    rowGap: screenHeight * 0.02
  },
  heroAttributeContainer: {
    backgroundColor: 'white',
    elevation: 2,
    padding: 13,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    width: '92%',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  attributeCol: {
    flexDirection: 'column',
    display: 'flex',
  },
  attributeText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 25

  },
  heroContainer: {
    height: screenHeight * 0.3,
    marginTop: screenHeight * 0.06,
    width: '50%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    rowGap: '7%',
  },
  heroTitleContainer: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  heroMainTitle: {
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
    textShadowColor: 'grey',
    letterSpacing: -0.5,
    color: '#333332c9'
  },
  heroThinTitle: {
    fontSize: 10,
    textAlign: 'center',
    flexWrap: 'wrap',
    letterSpacing: 1,
    color: '#61615ec9'
  },
  productImage: {
    width: '100%',
    height: '60%',
    borderRadius: 10,
    display: 'flex',
    justifyContent: 'center'
  },
  infoContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 10,
    backgroundColor: 'white',
    marginTop: -30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  brandBadge: {
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  brandBadgeText: {
    color: '#1976d2',
    fontSize: 12,
    fontWeight: '600',
  },
  productName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  productMeta: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  metaText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  primaryButton: {
    backgroundColor: '#3498db',
  },
  primaryButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 4,
  },
  secondaryButton: {
    backgroundColor: '#ecf0f1',
  },
  secondaryButtonText: {
    color: '#3498db',
    fontWeight: '600',
    marginLeft: 4,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 0,
    marginHorizontal: 0,
    marginTop: 16,
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewAllText: {
    fontSize: 14,
    color: '#3498db',
  },
  nutritionVisual: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eeeeee',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionChart: {
    width: 40,
    height: 100,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    justifyContent: 'flex-end',
    overflow: 'hidden',
    marginBottom: 8,
  },
  nutritionFill: {
    width: '100%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  nutritionLabel: {
    fontSize: 12,
    color: '#616161',
    marginBottom: 2,
  },
  nutritionValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#212121',
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 20,
  },
  activeTab: {
    backgroundColor: '#e3f2fd',
  },
  tabText: {
    color: '#757575',
    fontSize: 14,
  },
  activeTabText: {
    color: '#1976d2',
    fontWeight: '600',
    fontSize: 14,
  },
  analysisContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  analysisItem: {
    width: '100%',
    height: '100%',
    padding: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  analysisItemContainer: {
    width: '30%',
    height: screenHeight * 0.15,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  analysisItemTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    width: '100%',
  },
  analysisItemContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  analysisIcon: {
    marginRight: 4,
  },
  analysisLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  analysisValue: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  ingredientSection: {
    marginBottom: 20,
  },
  ingredientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ingredientTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    color: '#444',
  },
  ingredientsList: {
    marginLeft: 8,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ingredientText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
  similarProducts: {
    marginTop: 8,
  },
  similarProductItem: {
    width: 120,
    marginRight: 12,
  },
  similarProductImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    marginBottom: 8,
  },
  similarProductName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#212121',
    marginBottom: 4,
    height: 40,
  },
  similarProductRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  similarProductScore: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#ff9900',
    marginRight: 4,
  },

});
  
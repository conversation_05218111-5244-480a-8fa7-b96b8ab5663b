import { useLocalSearchParams } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import ReviewItem from '@/components/review/ReviewItem';
import { useBrandReviewDetail } from '@/store/brandReviewStore';
import { useReviewDetail } from '@/store/productReviewStore';

export default function ReviewDetailScreen() {
  const { reviewId, entityType } = useLocalSearchParams<{ 
    reviewId: string;
    entityType: 'product' | 'brand';
    entityId: string;
  }>();
  
  const [replyText, setReplyText] = useState('');
  const [isReplying, setIsReplying] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  
  const productReviewHook = useReviewDetail(
    entityType === 'product' ? reviewId || '' : ''
  );
  
  const brandReviewHook = useBrandReviewDetail(
    entityType === 'brand' ? reviewId || '' : ''
  );
  
  const {
    review,
    replies,
    isLoading,
    error,
    submitReply
  } = entityType === 'brand' ? brandReviewHook : productReviewHook;


  const handleSubmitReply = async () => {
    if (!replyText.trim()) return;
    
    setIsReplying(true);
    try {
      await submitReply(replyText.trim());
      setReplyText('');
      Keyboard.dismiss();
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Failed to submit reply:', error);
    } finally {
      setIsReplying(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3498db" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </View>
    );
  }

  if (error || !review) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="alert-circle-outline" size={40} color="#e74c3c" />
          <Text style={styles.errorText}>无法加载评论详情</Text>
          <Text style={styles.errorSubtext}>
            评论可能已被删除或不存在
          </Text>
          {__DEV__ && (
            <Text style={styles.debugText}>
              Review ID: {reviewId}{'\n'}
              Entity Type: {entityType}{'\n'}
              Error: {error ? error.toString() : 'No review found'}
            </Text>
          )}
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* Content */}
      <FlatList
        ref={flatListRef}
        style={styles.content}
        data={[review, ...replies]}
        keyExtractor={(item, index) => index === 0 ? 'main-review' : item._id || item.review_id}
        renderItem={({ item, index }) => (
          <ReviewItem
            review={item}
            entityType={entityType}
            isReply={index > 0}
            compact={false}
            showRepliesButton={false}
            showRating={index === 0}
          />
        )}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        ListFooterComponent={() => replies.length > 0 && (
          <View style={styles.listFooter}>
            <Text style={styles.repliesTitle}>
              全部回复 ({replies.length})
            </Text>
          </View>
        )}
        contentContainerStyle={styles.listContainer}
      />


      <View style={styles.replyContainer}>
        <TextInput
          style={styles.replyInput}
          placeholder="写回复..."
          value={replyText}
          onChangeText={setReplyText}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[
            styles.submitButton,
            (!replyText.trim() || isReplying) && styles.submitButtonDisabled
          ]}
          onPress={handleSubmitReply}
          disabled={!replyText.trim() || isReplying}
        >
          {isReplying ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Icon name="send" size={16} color="white" />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 48,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  listHeader: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  listFooter: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    marginTop: 8,
  },
  repliesTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  separator: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: 8,
    marginHorizontal: 16,
  },
  replyContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  replyInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxHeight: 100,
    fontSize: 14,
    backgroundColor: '#f8f8f8',
  },
  submitButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: '#3498db',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    color: '#e74c3c',
  },
  errorSubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#888',
  },
  debugText: {
    marginTop: 16,
    fontSize: 12,
    color: '#999',
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    alignSelf: 'stretch',
    marginHorizontal: 20,
  },
  listContainer: {
    paddingBottom: 16,
  },
});

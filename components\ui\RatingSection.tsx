import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface ReviewSummary {
  review_count: number;
  long_review_count: number;
  short_review_count: number;
  average_rating: number;
  total_rating: number;
}

interface RatingSectionProps {
  summary: ReviewSummary | null | undefined;
  title?: string;
  showReviewTypes?: boolean;
}

const RatingSection: React.FC<RatingSectionProps> = ({
  summary,
  title = "",
  showReviewTypes = true
}) => {
  // 渲染星级
  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating / 2);
    const halfStar = rating % 2 >= 1;
    const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

    return (
      <View style={styles.starsWrapper}>
        {[...Array(fullStars)].map((_, i) => (
          <Icon key={`full-${i}`} name="star" size={16} color="#ff9900" />
        ))}
        {halfStar && <Icon name="star-half" size={16} color="#ff9900" />}
        {[...Array(emptyStars)].map((_, i) => (
          <Icon key={`empty-${i}`} name="star-outline" size={16} color="#ff9900" />
        ))}
      </View>
    );
  };

  // 计算星级分布百分比
  const calculatePercentages = (summary: ReviewSummary | null | undefined): number[] => {
    if (!summary || summary.review_count === 0) return [0, 0, 0, 0, 0];

    // 由于当前没有星级分布数据，我们基于平均评分来估算分布
    // 这是一个临时解决方案，理想情况下应该在后端统计真实的星级分布
    const avgRating = summary.average_rating;
    const totalReviews = summary.review_count;

    // 将10分制转换为5分制
    const avgRating5Scale = avgRating / 2;

    // 基于平均评分估算分布（这是一个简化的算法）
    if (totalReviews === 1) {
      // 如果只有一个评论，根据评分确定星级
      const starLevel = Math.round(avgRating5Scale);
      const percentages = [0, 0, 0, 0, 0];
      if (starLevel >= 1 && starLevel <= 5) {
        percentages[5 - starLevel] = 100; // 数组索引：0=5星, 1=4星, 2=3星, 3=2星, 4=1星
      }
      return percentages;
    }

    // 对于多个评论，创建一个合理的分布
    // 大部分评论集中在平均分附近
    const centerStar = Math.round(avgRating5Scale);
    const percentages = [0, 0, 0, 0, 0];

    if (centerStar >= 1 && centerStar <= 5) {
      const centerIndex = 5 - centerStar; // 转换为数组索引
      percentages[centerIndex] = 60; // 主要评分占60%

      // 分配其余40%到相邻星级
      if (centerIndex > 0) {
        percentages[centerIndex - 1] = 25; // 高一星级25%
      }
      if (centerIndex < 4) {
        percentages[centerIndex + 1] = 15; // 低一星级15%
      }
    }

    return percentages;
  };

  // 使用估算逻辑计算星级分布
  const percentages = calculatePercentages(summary);

  if (!summary) {
    return null;
  }

  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
      </View>
      <Text style={styles.cardTitle}>好宠粮评分</Text>
      <View style={styles.ratingContainer}>
        <View style={styles.overallRating}>
          <Text style={styles.ratingScore}>{summary.average_rating.toFixed(1)}</Text>
          {renderStars(summary.average_rating)}
          <Text style={styles.ratingCount}>{summary.review_count}人评分</Text>
        </View>

        <View style={styles.ratingBreakdown}>
          <RatingBar label={5} percentage={percentages[0]} />
          <RatingBar label={4} percentage={percentages[1]} />
          <RatingBar label={3} percentage={percentages[2]} />
          <RatingBar label={2} percentage={percentages[3]} />
          <RatingBar label={1} percentage={percentages[4]} />
        </View>
      </View>

      {showReviewTypes && (
        <View style={styles.reviewTypeContainer}>
          <View style={styles.reviewTypeItem}>
            <Text style={styles.reviewTypeCount}>{summary.long_review_count}</Text>
            <Text style={styles.reviewTypeLabel}>长评</Text>
          </View>
          <View style={styles.reviewTypeItem}>
            <Text style={styles.reviewTypeCount}>{summary.short_review_count}</Text>
            <Text style={styles.reviewTypeLabel}>短评</Text>
          </View>
          <View style={styles.reviewTypeItem}>
            <Text style={styles.reviewTypeCount}>{summary.total_rating}</Text>
            <Text style={styles.reviewTypeLabel}>总评分</Text>
          </View>
        </View>
      )}
    </View>
  );
};

// Rating bar component
const RatingBar = ({ label, percentage }: { label: number; percentage: number }) => {
  const renderStars = (rating: number) => {
    const fullStars = rating;
    return (
      <View style={styles.starsWrapper}>
        {[...Array(fullStars)].map((_, i) => (
          <Icon key={`full-${i}`} name="star" size={12} color="#ff9900" />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.ratingBarContainer}>
      <View style={styles.ratingBarLabel}>
        {renderStars(label)}
      </View>
      <View style={styles.ratingBarWrapper}>
        <View style={[styles.ratingBarFill, { width: `${percentage}%` }]} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    padding: 5,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,

  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginLeft: 7
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  overallRating: {
    width: '30%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRightWidth: 1,
    borderColor: '#eeeeee',
    paddingRight: 8,
  },
  ratingScore: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#ff9900',
    lineHeight: 30,
  },
  starsWrapper: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  ratingCount: {
    fontSize: 11,
    color: '#666',
    marginTop: 2,
  },
  ratingBreakdown: {
    width: '65%',
    justifyContent: 'center',
    paddingLeft: 1,
  },
  ratingBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    height: 10,
  },
  ratingBarLabel: {
    width: 70,
    justifyContent: 'center',
  },
  ratingBarWrapper: {
    flex: 1,
    height: 6,
    backgroundColor: '#eeeeee',
    borderRadius: 3,
    marginHorizontal: 8,
  },
  ratingBarFill: {
    height: '100%',
    backgroundColor: '#ff9900',
    borderRadius: 3,
  },
  reviewTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#eeeeee',
    paddingTop: 10,
  },
  reviewTypeItem: {
    alignItems: 'center',
  },
  reviewTypeCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  reviewTypeLabel: {
    fontSize: 11,
    color: '#666',
    marginTop: 2,
  },
});

export default RatingSection;

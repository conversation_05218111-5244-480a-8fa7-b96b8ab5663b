import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getAvatarUrl, formatUserId } from '@/utils/userUtils';
import { useUserInfo } from '@/store/userStore';
import { useFollowStatus, useToggleFollow } from '@/store/userStatsStore';

interface UserListItemProps {
  user: {
    follower_id?: string;
    following_id?: string;
    created_at?: string;
    user?: {
      id: string;
      username: string;
      avatar_url?: string;
      bio?: string;
    };
  };
  onPress?: () => void;
}

export default function UserListItem({ user, onPress }: UserListItemProps) {
  const currentUser = useUserInfo();

  // 确定用户ID和用户信息
  const userId = user.user?.id || user.follower_id || user.following_id || '';
  const userInfo = user.user || {
    id: userId,
    username: `用户${userId.slice(-6)}`,
    avatar_url: null,
    bio: '这个人很懒，什么都没有留下...',
  };

  // 获取关注状态
  const { data: followStatus } = useFollowStatus(userId, currentUser.id);
  const toggleFollowMutation = useToggleFollow(currentUser.id);

  // 是否是当前用户自己
  const isCurrentUser = userId === currentUser.id;

  // 处理关注按钮点击
  const handleFollowPress = async (e: any) => {
    e.stopPropagation(); // 防止触发用户点击事件

    if (!currentUser.id || isCurrentUser) return;

    try {
      await toggleFollowMutation.mutateAsync(userId);
    } catch (error) {
      console.error('Follow error:', error);
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      {/* 用户头像 */}
      <Image
        source={{ uri: getAvatarUrl(userInfo.avatar_url || undefined) }}
        style={styles.avatar}
      />

      {/* 用户信息 */}
      <View style={styles.userInfo}>
        <Text style={styles.username} numberOfLines={1}>
          {userInfo.username}
        </Text>
        <Text style={styles.userId} numberOfLines={1}>
          ID: {formatUserId(userInfo.id)}
        </Text>
        {userInfo.bio && (
          <Text style={styles.bio} numberOfLines={2}>
            {userInfo.bio}
          </Text>
        )}
      </View>
      
      {/* 关注按钮 */}
      {!isCurrentUser && (
        <TouchableOpacity
          style={[
            styles.followButton,
            followStatus?.isFollowing && styles.followingButton
          ]}
          onPress={handleFollowPress}
          disabled={toggleFollowMutation.isPending}
        >
          {toggleFollowMutation.isPending ? (
            <Text style={[styles.followButtonText, styles.loadingText]}>
              处理中...
            </Text>
          ) : (
            <>
              <Ionicons
                name={followStatus?.isFollowing ? 'checkmark' : 'add'}
                size={16}
                color={followStatus?.isFollowing ? '#666' : '#fff'}
                style={styles.followIcon}
              />
              <Text
                style={[
                  styles.followButtonText,
                  followStatus?.isFollowing && styles.followingButtonText
                ]}
              >
                {followStatus?.isFollowing ? '已关注' : '关注'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
  },
  userInfo: {
    flex: 1,
    marginLeft: 12,
    marginRight: 12,
  },
  username: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  userId: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4,
  },
  bio: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  followButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 80,
    justifyContent: 'center',
  },
  followingButton: {
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  followIcon: {
    marginRight: 4,
  },
  followButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  followingButtonText: {
    color: '#666',
  },
  loadingText: {
    color: '#999',
  },
});

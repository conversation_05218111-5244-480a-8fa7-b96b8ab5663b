import { DrawerActions, useNavigation } from '@react-navigation/native';
import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

export default function ExtendSideBarButton() {
  const navigation = useNavigation();

  const handlePress = () => {
    try {
      navigation.dispatch(DrawerActions.openDrawer());
    } catch (error) {
      console.log('Error opening drawer:', error);
    }
  };

  return (
    <TouchableOpacity style={styles.button} onPress={handlePress}>
      <Icon name="menu" size={24} color="#666" />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    padding: 0,
    marginRight: 8,
  },
});
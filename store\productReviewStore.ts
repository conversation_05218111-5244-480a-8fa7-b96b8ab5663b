import type { ProductReviewSummary, ProductReviewSummaryResponse, Review } from "@/api/services/productReviewService";
import reviewService from "@/api/services/productReviewService";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useState } from 'react';
import { create } from "zustand";
import { useUserInfo } from "./userStore";


type ProductReviewStore = {
	summaryItems: ProductReviewSummary[];
	actions: {
		setSummaryItems: (
			summaryItems: ProductReviewSummary[],
			pagination?: { totalCount: number; totalPages: number; currentPage: number },
		) => void;
		clearSummaryItems: () => void;
	};
};

const useProductReviewStore = create<ProductReviewStore>()((set) => ({
	summaryItems: [],
	actions: {
		setSummaryItems: (summaryItems) => {
			set({
				summaryItems: summaryItems,
			});
		},
		clearSummaryItems: () => {
			set({
				summaryItems: [],
			});
		},
	},
}));

// Review summary management hook
export function useReviewSummaryAdmin() {
	const [currentPage, setCurrentPage] = useState(1);
	const [sortBy, setSortBy] = useState<string>("review_count");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

	const queryClient = useQueryClient();

	const query = useQuery<ProductReviewSummaryResponse>({
		queryKey: ["reviews", "summaries", currentPage, sortBy, sortOrder],
		queryFn: async () => {
			try {
				const response = await reviewService.getProductReviewSummaries(currentPage, 10, sortBy, sortOrder);
				if (typeof response !== "object" || !response) {
					throw new Error("Invalid response format");
				}

				return response;
			} catch (error) {
				console.error("Review summary fetch error:", error);
				throw error;
			}
		},
		placeholderData: (previousData: ProductReviewSummaryResponse | undefined) => previousData,
		staleTime: 10 * 100,
	});

	// Get reviews for a specific product
	const useProductReviews = (productId: string, page = 1, limit = 10) => {
		return useQuery({
			queryKey: ["reviews", "product", productId, page, limit],
			queryFn: () => reviewService.getReviewsByProductId(productId, page, limit),
			enabled: !!productId,
		});
	};

	// Handle sort changes
	const handleSortChange = (newSortBy: string, newSortOrder: "asc" | "desc") => {
		setSortBy(newSortBy);
		setSortOrder(newSortOrder);
	};

	// Update review status
	const updateReviewStatusMutation = useMutation({
		mutationFn: ({ reviewId, status }: { reviewId: string; status: boolean }) =>
			reviewService.updateReviewStatus(reviewId, status),
		onSuccess: () => {
			// Invalidate review queries to refetch data
			queryClient.invalidateQueries({ queryKey: ["reviews"] });
		},
	});

	// Update review content
	const updateReviewContentMutation = useMutation({
		mutationFn: ({ reviewId, data }: { reviewId: string; data: Partial<Review> }) =>
			reviewService.updateReviewContent(reviewId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["reviews"] });
		},
	});

	// Delete review
	const deleteReviewMutation = useMutation({
		mutationFn: (reviewId: string) => reviewService.deleteReview(reviewId),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["reviews"] });
		},
	});

	// Create review
	const createReviewMutation = useMutation({
		mutationFn: (data: Partial<Review>) => reviewService.createReview(data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["reviews"] });
		},
	});

	return {
		data: query.data?.products,
		pagination: query.data
			? {
					totalCount: Number(query.data.totalCount) || 0,
					totalPages: Number(query.data.totalPages) || 1,
					currentPage: Number(query.data.currentPage) || 1,
					sortBy: query.data.sortBy,
					sortOrder: query.data.sortOrder,
				}
			: undefined,
		setPage: setCurrentPage,
		useProductReviews,
		handleSortChange,
		sortBy,
		sortOrder,
		isLoading: query.isLoading || query.isFetching,
		updateReviewStatus: updateReviewStatusMutation.mutate,
		updateReviewContent: updateReviewContentMutation.mutate,
		deleteReview: deleteReviewMutation.mutate,
		createReview: createReviewMutation.mutate,
		isUpdatingStatus: updateReviewStatusMutation.isPending,
		isUpdatingContent: updateReviewContentMutation.isPending,
		isDeleting: deleteReviewMutation.isPending,
		isCreating: createReviewMutation.isPending,
	};
}

interface UseReviewDetailReturn {
  review: Review | null;
  replies: Review[];
  isLoading: boolean;
  error: string | null;
  submitReply: (content: string) => Promise<void>;
  refreshReview: () => Promise<void>;
}

export const useReviewDetail = (reviewId: string): UseReviewDetailReturn => {
  const userInfo = useUserInfo();
  const queryClient = useQueryClient();
  const [review, setReview] = useState<Review | null>(null);
  const [replies, setReplies] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReviewDetail = useCallback(async () => {
    if (!reviewId) {
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
    
      const reviewData = await reviewService.getReviewById(reviewId);
      
      if (reviewData) {
        setReview(reviewData);
        
        // 使用review_id字段获取回复，与品牌评论保持一致
        const replyId = reviewData.review_id || reviewData._id;
        
        try {
          const repliesData = await reviewService.getReviewReplies(replyId, 1, 100);

          if (repliesData && repliesData.reviews) {
            setReplies(repliesData.reviews);
          } else {
            setReplies([]);
          }
        } catch (repliesError) {
          setReplies([]);
          // Don't set error state here to allow showing the main review
        }
      } else {
        throw new Error('Product review not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load product review details');
      setReview(null);
    } finally {
      setIsLoading(false);
    }
  }, [reviewId]);

  const submitReply = async (content: string): Promise<void> => {
    if (!userInfo.id) {
      throw new Error('用户未登录，无法回复');
    }

    try {
      
      const replyData = {
        content,
        parent_review_id: review?.review_id || reviewId, // 使用review_id字段
        user_id: userInfo.id,
        product_id: review?.product_id,
        rating: 1,
        is_long_review: false
      };

      const newReply = await reviewService.createReview(replyData);
      
      if (newReply) {
        // 确保新回复使用正确的用户ID
        const correctedReply = {
          ...newReply,
          user_id: userInfo.id // 强制使用当前用户ID
        };
        
        setReplies(prev => [...prev, correctedReply]);

        // 更新主评论的回复计数
        setReview(prev => prev ? {
          ...prev,
          reply_count: (prev.reply_count || 0) + 1
        } : null);

        // 刷新产品评论列表查询，这样产品详情页面的回复计数会更新
        if (review?.product_id) {
          queryClient.invalidateQueries({ 
            queryKey: ['reviews', 'product', review.product_id] 
          });
        }

        // 也刷新所有相关的产品评论查询
        queryClient.invalidateQueries({ 
          queryKey: ['reviews'] 
        });
      }
    } catch (err) {
      console.error('Error submitting product reply:', err);
      throw err;
    }
  };

  const refreshReview = async (): Promise<void> => {
    await fetchReviewDetail();
  };

  useEffect(() => {
    if (reviewId) {
      fetchReviewDetail();
    }
  }, [reviewId, fetchReviewDetail]);

  return {
    review,
    replies,
    isLoading,
    error,
    submitReply,
    refreshReview,
  };
};

/**
 * Hook for creating product reviews with automatic refresh
 */
export const useCreateProductReview = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<Review>) => reviewService.createReview(data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch related queries
      if (variables.product_id) {
        // Invalidate product-specific review queries
        queryClient.invalidateQueries({ 
          queryKey: ['reviews', 'product', variables.product_id] 
        });
        
        // Invalidate product review summary
        queryClient.invalidateQueries({ 
          queryKey: ['product-review-summary', variables.product_id] 
        });
      }
      
      // Invalidate general product review queries
      queryClient.invalidateQueries({ queryKey: ['reviews'] });
    },
    onError: (error) => {
      console.error('Create product review error:', error);
    },
  });
};

/**
 * Hook for getting product reviews with pagination and filtering
 */
export const useProductReviews = (
  productId: string,
  limit: number = 5,
  type: 'all' | 'long' | 'short' = 'all',
  sortBy: 'latest' | 'hot' = 'latest'
) => {
  const [allReviews, setAllReviews] = useState<Review[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const query = useQuery({
    queryKey: ['reviews', 'product', productId, currentPage, limit, type, sortBy],
    queryFn: async () => {
      try {
        let result;
  
        switch (type) {
          case 'long':
            result = await reviewService.getLongReviews(productId, currentPage, limit, true, 2, sortBy);
            break;
          case 'short':
            result = await reviewService.getShortReviews(productId, currentPage, limit, true, 2, sortBy);
            break;
          default:
            result = await reviewService.getReviewsByProductId(productId, currentPage, limit, undefined, sortBy);
        }
        
        
        if (result && result.reviews) {
          return {
            reviews: result.reviews,
            pagination: {
              totalCount: result.totalCount || 0,
              totalPages: result.totalPages || 1,
              currentPage: result.currentPage || currentPage,
            }
          };
        }
        
        return {
          reviews: [],
          pagination: {
            totalCount: 0,
            totalPages: 1,
            currentPage: 1,
          }
        };
      } catch (error) {
        console.error('Error fetching product reviews:', error);
        throw error;
      }
    },
    enabled: !!productId,
    staleTime: 1 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error instanceof Error && error.message.includes('404')) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Reset when type, sortBy or productId changes
  useEffect(() => {
    setCurrentPage(1);
    setAllReviews([]);
    setHasMore(true);
  }, [type, sortBy, productId]);

  // Update accumulated reviews when new data arrives
  useEffect(() => {
    if (query.data?.reviews) {
      if (currentPage === 1) {
        // If it's the first page or refresh, replace all data
        setAllReviews(query.data.reviews);
      } else {
        // If loading more, append data, avoiding duplicates
        setAllReviews(prev => {
          const existingIds = new Set(prev.map(review => 
            review.review_id || review._id
          ));
          const newReviews = query.data.reviews.filter((newReview: Review) => 
            !existingIds.has(newReview.review_id || newReview._id)
          );
          return [...prev, ...newReviews];
        });
      }
      
      // Check if there are more pages
      const totalPages = query.data.pagination?.totalPages || 1;
      setHasMore(currentPage < totalPages);
    } else if (query.data && currentPage === 1) {
      // If it's first page and no data, clear list
      setAllReviews([]);
      setHasMore(false);
    }
  }, [query.data, currentPage]);

  const refreshReviews = async () => {
    setIsRefreshing(true);
    setCurrentPage(1);
    setAllReviews([]);
    setHasMore(true);
    await query.refetch();
    setIsRefreshing(false);
  };

  const loadMoreReviews = () => {
    if (hasMore && !query.isFetching && !query.isLoading) {
      setCurrentPage(prev => prev + 1);
    }
  };

  return {
    reviews: allReviews,
    pagination: query.data?.pagination,
    isLoading: query.isLoading && currentPage === 1,
    isLoadingMore: query.isFetching && currentPage > 1,
    isRefreshing,
    error: query.error,
    hasMore,
    refreshReviews,
    loadMoreReviews,
  };
};

export default useProductReviewStore;


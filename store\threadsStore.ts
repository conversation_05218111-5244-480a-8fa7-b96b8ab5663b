import type { CreateThreadRequest, Thread, ThreadSortBy, UpdateThreadRequest } from "@/api/services/threadsService";
import threadsService from "@/api/services/threadsService";
import { keepPreviousData, useInfiniteQuery, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { create } from "zustand";
import { useUserInfo } from "./userStore";

// 帖子数据存储
type ThreadsStore = {
  threads: Thread[];
  selectedThread: Thread | null;
  actions: {
    setThreads: (threads: Thread[]) => void;
    addThread: (thread: Thread) => void;
    updateThread: (threadId: string, threadData: Partial<Thread>) => void;
    removeThread: (threadId: string) => void;
    setSelectedThread: (thread: Thread | null) => void;
    clearThreads: () => void;
  };
};

const useThreadsStore = create<ThreadsStore>()((set) => ({
  threads: [],
  selectedThread: null,
  actions: {
    setThreads: (threads) => {
      set({ threads });
    },
    addThread: (thread) => {
      set((state) => ({ threads: [thread, ...state.threads] }));
    },
    updateThread: (threadId, threadData) => {
      set((state) => ({
        threads: state.threads.map(thread =>
          thread.thread_id === threadId ? { ...thread, ...threadData } : thread
        )
      }));
    },
    removeThread: (threadId) => {
      set((state) => ({
        threads: state.threads.filter(thread => thread.thread_id !== threadId)
      }));
    },
    setSelectedThread: (thread) => {
      set({ selectedThread: thread });
    },
    clearThreads: () => {
      set({ threads: [], selectedThread: null });
    },
  },
}));

// 支持分页和排序的帖子列表钩子
export function useThreads() {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<ThreadSortBy>('latest');
  const [allThreads, setAllThreads] = useState<Thread[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const userInfo = useUserInfo();
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ["threads", currentPage, sortBy, userInfo.id],
    queryFn: async () => {
      try {
        const response = await threadsService.getThreads(currentPage, 10, sortBy, userInfo.id);
        return response;
      } catch (error) {
        console.error("Threads fetch error:", error);
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000,
    retry: 3,
    placeholderData: keepPreviousData,
    // 如果是关注动态但用户未登录，禁用查询
    enabled: sortBy !== 'following' || !!userInfo.id,
  });

  // 创建帖子变更
  const createThreadMutation = useMutation({
    mutationFn: ({ data, userId }: { data: CreateThreadRequest; userId: string }) =>
      threadsService.createThread(data, userId),
    onSuccess: (newThread, { data }) => {
      // 如果是回复，更新父帖子的回复数
      if (data.parent_thread_id) {
        // 更新无限查询缓存中的回复数
        queryClient.setQueryData(
          ["thread", data.parent_thread_id, "comments"],
          (oldData: any) => {
            if (!oldData) return oldData;

            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                thread: page.thread?.thread_id === data.parent_thread_id
                  ? { ...page.thread, reply_count: (page.thread.reply_count || 0) + 1 }
                  : page.thread
              }))
            };
          }
        );
      }

      queryClient.invalidateQueries({ queryKey: ["threads"] });
      queryClient.invalidateQueries({ queryKey: ["popular-threads"] });
      setCurrentPage(1);
      setHasMore(true);
    },
    onError: (error: any) => {
      console.error("Create thread error:", error);
      throw error;
    },
  });

  // 更新帖子变更
  const updateThreadMutation = useMutation({
    mutationFn: ({ threadId, data, userId }: { threadId: string; data: UpdateThreadRequest; userId: string }) =>
      threadsService.updateThread(threadId, data, userId),
    onSuccess: (updatedThread, variables) => {
      // 立即更新本地状态
      const { updateThread } = useThreadsStore.getState().actions;
      updateThread(variables.threadId, updatedThread);

      // 更新无限查询缓存中的帖子详情
      queryClient.setQueryData(
        ["thread", variables.threadId, "comments"],
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              thread: page.thread?.thread_id === variables.threadId
                ? { ...page.thread, ...updatedThread }
                : page.thread
            }))
          };
        }
      );

      // 使相关缓存失效以确保数据一致性
      queryClient.invalidateQueries({ queryKey: ["threads"] });
      queryClient.invalidateQueries({ queryKey: ["popular-threads"] });
    },
  });

  // 删除帖子变更
  const deleteThreadMutation = useMutation({
    mutationFn: ({ threadId, userId }: { threadId: string; userId: string }) =>
      threadsService.deleteThread(threadId, userId),
    onSuccess: (_, { threadId }) => {
      // 立即从本地状态中移除帖子
      setAllThreads(prev => prev.filter(thread => thread.thread_id !== threadId));

      // 移除无限查询缓存中的帖子数据
      queryClient.removeQueries({ queryKey: ["thread", threadId, "comments"] });

      // 使相关缓存失效
      queryClient.invalidateQueries({ queryKey: ["threads"] });
      queryClient.invalidateQueries({ queryKey: ["popular-threads"] });
    },
  });

  // 切换点赞变更
  const toggleLikeMutation = useMutation({
    mutationFn: ({ threadId, userId }: { threadId: string; userId: string }) =>
      threadsService.toggleLike(threadId, userId),
    onSuccess: (data, { threadId, userId }) => {
      // 立即更新本地状态以提升用户体验
      setAllThreads(prev => prev.map(thread =>
        thread.thread_id === threadId
          ? { ...thread, like_count: data.like_count, liked_by: data.liked ? [...thread.liked_by, userId] : thread.liked_by.filter(id => id !== userId) }
          : thread
      ));

      // 更新无限查询缓存中的点赞状态
      queryClient.setQueryData(
        ["thread", threadId, "comments"],
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              thread: page.thread?.thread_id === threadId
                ? {
                    ...page.thread,
                    like_count: data.like_count,
                    liked_by: data.liked
                      ? [...(page.thread.liked_by || []), userId]
                      : (page.thread.liked_by || []).filter((id: string) => id !== userId)
                  }
                : page.thread,
              // 同时更新回复评论的点赞状态
              comments: page.comments?.map((comment: any) =>
                comment.thread_id === threadId
                  ? {
                      ...comment,
                      like_count: data.like_count,
                      liked_by: data.liked
                        ? [...(comment.liked_by || []), userId]
                        : (comment.liked_by || []).filter((id: string) => id !== userId)
                    }
                  : comment
              ) || []
            }))
          };
        }
      );

      // 更新用户相关的查询缓存
      queryClient.setQueriesData(
        { queryKey: ["user"] },
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages?.map((page: any) => ({
              ...page,
              threads: page.threads?.map((thread: any) =>
                thread.thread_id === threadId
                  ? {
                      ...thread,
                      like_count: data.like_count,
                      liked_by: data.liked
                        ? [...(thread.liked_by || []), userId]
                        : (thread.liked_by || []).filter((id: string) => id !== userId)
                    }
                  : thread
              ) || []
            })) || []
          };
        }
      );

      queryClient.invalidateQueries({ queryKey: ["threads"] });
    },
  });

  // 切换收藏变更
  const toggleBookmarkMutation = useMutation({
    mutationFn: ({ threadId, userId }: { threadId: string; userId: string }) =>
      threadsService.toggleBookmark(threadId, userId),
    onSuccess: (data, { threadId, userId }) => {
      // 立即更新本地状态以提升用户体验
      setAllThreads(prev => prev.map(thread =>
        thread.thread_id === threadId
          ? { ...thread, bookmark_count: data.bookmark_count, bookmarked_by: data.bookmarked ? [...thread.bookmarked_by, userId] : thread.bookmarked_by.filter(id => id !== userId) }
          : thread
      ));

      // 更新无限查询缓存中的收藏状态
      queryClient.setQueryData(
        ["thread", threadId, "comments"],
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              thread: page.thread?.thread_id === threadId
                ? {
                    ...page.thread,
                    bookmark_count: data.bookmark_count,
                    bookmarked_by: data.bookmarked
                      ? [...(page.thread.bookmarked_by || []), userId]
                      : (page.thread.bookmarked_by || []).filter((id: string) => id !== userId)
                  }
                : page.thread
            }))
          };
        }
      );

      // 更新用户相关的查询缓存
      queryClient.setQueriesData(
        { queryKey: ["user"] },
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages?.map((page: any) => ({
              ...page,
              threads: page.threads?.map((thread: any) =>
                thread.thread_id === threadId
                  ? {
                      ...thread,
                      bookmark_count: data.bookmark_count,
                      bookmarked_by: data.bookmarked
                        ? [...(thread.bookmarked_by || []), userId]
                        : (thread.bookmarked_by || []).filter((id: string) => id !== userId)
                    }
                  : thread
              ) || []
            })) || []
          };
        }
      );

      queryClient.invalidateQueries({ queryKey: ["threads"] });
    },
  });

  // 根据ID获取帖子钩子
  const useThread = (threadId: string, commentsPage = 1) => {
    return useQuery({
      queryKey: ["thread", threadId, commentsPage],
      queryFn: () => threadsService.getThreadById(threadId, commentsPage, 10),
      enabled: !!threadId,
    });
  };

  // 使用无限查询获取帖子评论
  // useInfiniteQuery 已经内置了数据保持机制 不需要 placeholderData
  const useInfiniteThreadComments = (threadId: string) => {
    return useInfiniteQuery({
      queryKey: ["thread", threadId, "comments"],
      queryFn: async ({ pageParam = 1 }) => {
        const response = await threadsService.getThreadById(threadId, pageParam, 10);
        return {
          comments: response.comments.items,
          total: response.comments.total,
          page: response.comments.page,
          limit: response.comments.limit,
          thread: pageParam === 1 ? response.thread : undefined, // 只在第一页返回主帖数据
        };
      },
      enabled: !!threadId,
      initialPageParam: 1,
      getNextPageParam: (lastPage, allPages) => {
        const totalPages = Math.ceil(lastPage.total / lastPage.limit);
        const nextPage = lastPage.page + 1;
        // 此方法的return的值决定了查询函数中的pageParam参数
        return nextPage <= totalPages ? nextPage : undefined;
      },
      staleTime: 2 * 60 * 1000,
      retry: 3,
    });
  };

  // 用户相关的帖子查询钩子
  const useUserThreads = (userId: string, type: 'posts' | 'likes' | 'bookmarks') => {
    return useInfiniteQuery({
      queryKey: ["user", userId, type],
      queryFn: async ({ pageParam = 1 }) => {
        let response;
        switch (type) {
          case 'posts':
            response = await threadsService.getThreadsByUserId(userId, pageParam, 10);
            break;
          case 'likes':
            response = await threadsService.getUserLikedThreads(userId, pageParam, 10);
            break;
          case 'bookmarks':
            response = await threadsService.getUserBookmarkedThreads(userId, pageParam, 10);
            break;
        }
        return {
          threads: response.threads || [],
          total: response.total || 0,
          page: pageParam,
          hasMore: (response.threads || []).length === 10,
        };
      },
      enabled: !!userId,
      initialPageParam: 1,
      getNextPageParam: (lastPage, allPages) => {
        return lastPage.hasMore ? lastPage.page + 1 : undefined;
      },
      staleTime: 2 * 60 * 1000,
      retry: 3,
      placeholderData: keepPreviousData,
    });
  };

  // 排序方式改变时重置
  useEffect(() => {
    setCurrentPage(1);
    setAllThreads([]);
    setHasMore(true);
  }, [sortBy]);

  // 查询数据改变时更新所有帖子
  useEffect(() => {
    if (query.data?.threads) {
      if (currentPage === 1) {
        // 第一页或刷新 - 替换所有数据
        setAllThreads(query.data.threads);
      } else {
        // 加载更多 - 追加新数据
        setAllThreads(prev => {
          const existingIds = new Set(prev.map(thread => thread.thread_id));
          const newThreads = query.data.threads.filter(thread =>
            !existingIds.has(thread.thread_id)
          );
          return [...prev, ...newThreads];
        });
      }

      // 检查是否还有更多页面
      const totalPages = Math.ceil((query.data.total || 0) / 10);
      setHasMore(currentPage < totalPages);
    }
  }, [query.data, currentPage]);

  const refreshThreads = async () => {
    setIsRefreshing(true);
    setCurrentPage(1);
    setHasMore(true);
    await query.refetch();
    setIsRefreshing(false);
  };

  const loadMoreThreads = () => {
    if (hasMore && !query.isFetching && !query.isLoading) {
      setCurrentPage(prev => prev + 1);
    }
  };

  return {
    threads: allThreads,
    pagination: query.data ? {
      total: query.data.total,
      page: query.data.page,
      limit: query.data.limit,
    } : undefined,

    // 加载状态
    isLoading: query.isLoading && currentPage === 1,
    isLoadingMore: query.isFetching && currentPage > 1,
    isRefreshing,
    isCreating: createThreadMutation.isPending,
    isUpdating: updateThreadMutation.isPending,
    isDeleting: deleteThreadMutation.isPending,
    isLiking: toggleLikeMutation.isPending,
    isBookmarking: toggleBookmarkMutation.isPending,

    // 筛选状态
    currentPage,
    sortBy,
    hasMore,

    // 操作方法
    setSortBy,
    refreshThreads,
    loadMoreThreads,

    // 变更操作
    createThread: createThreadMutation.mutate,
    updateThread: updateThreadMutation.mutateAsync,
    deleteThread: deleteThreadMutation.mutate,
    toggleLike: toggleLikeMutation.mutate,
    toggleBookmark: toggleBookmarkMutation.mutate,

    // 钩子函数
    useThread,
    useInfiniteThreadComments,
    useUserThreads,

    // 错误状态
    error: query.error,
    createError: createThreadMutation.error,
    updateError: updateThreadMutation.error,
    deleteError: deleteThreadMutation.error,
  };
}

export const useThreadsActions = () => useThreadsStore((state) => state.actions);
export const useThreadsList = () => useThreadsStore((state) => state.threads);
export const useSelectedThread = () => useThreadsStore((state) => state.selectedThread);

// 获取热门帖子的hook
export const usePopularThreads = () => {
  const query = useQuery<Thread[]>({
    queryKey: ["popular-threads"],
    queryFn: async () => {
      try {
        const response = await threadsService.getThreads(1, 20, 'hot');
        return response.threads || [];
      } catch (error) {
        console.error("Popular threads fetch error:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  return {
    data: query.data || [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  };
};

export default useThreadsStore;

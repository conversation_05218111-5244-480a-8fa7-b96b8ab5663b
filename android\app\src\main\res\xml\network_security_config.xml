<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 允许明文 HTTP 流量的域名配置 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 您的生产服务器 IP -->
        <domain includeSubdomains="true">*************</domain>
        <!-- 开发环境 IP -->
        <domain includeSubdomains="true">*************</domain>
        <!-- 本地开发环境 -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <!-- 常见的本地网络 IP 范围 -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        <!-- 私有网络 IP 范围 -->
        <domain includeSubdomains="true">10.0.0.0</domain>
        <domain includeSubdomains="true">**********</domain>
        <domain includeSubdomains="true">***********</domain>
    </domain-config>

    <!-- 全局允许明文流量（临时调试用） -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>

    <!-- 基础配置：允许系统证书 -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>

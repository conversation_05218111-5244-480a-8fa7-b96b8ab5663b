{"name": "petfood", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "check": "node scripts/check-errors.js", "build": "node scripts/build.js", "build:check": "node scripts/build.js --check", "build:dev-android": "node scripts/build.js --dev-android", "build:dev-ios": "node scripts/build.js --dev-ios", "build:prod-android": "node scripts/build.js --prod-android", "build:prod-android-aab": "npx eas build --platform android --profile production-aab", "build:prod-android-apk": "npx eas build --platform android --profile production", "build:prod-ios": "node scripts/build.js --prod-ios", "build:web": "node scripts/build.js --web", "type-check": "npx tsc --noEmit", "doctor": "npx expo-doctor", "debug:apk": "node scripts/debug-apk.js", "debug:check": "node scripts/debug-apk.js --check", "debug:info": "node scripts/debug-apk.js --info"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "expo": "53.0.20", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "~17.1.5", "expo-crypto": "~14.1.5", "expo-file-system": "~18.1.11", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "multer": "^2.0.1", "numeral": "^2.0.6", "ramda": "^0.31.3", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.5", "react-native-drawer-layout": "^4.1.10", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.18.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "sonner": "^2.0.4", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/multer": "^1.4.13", "@types/ramda": "^0.30.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "babel-plugin-transform-import-meta": "^2.3.3", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}
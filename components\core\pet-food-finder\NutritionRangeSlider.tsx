import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Slider from '@react-native-community/slider';
import { NutritionRange } from '@/types/pet-food-filter';

interface NutritionRangeSliderProps {
  label: string;
  unit: string;
  min: number;
  max: number;
  step: number;
  value?: NutritionRange;
  onChange: (range: NutritionRange | undefined) => void;
}

const NutritionRangeSlider: React.FC<NutritionRangeSliderProps> = ({
  label,
  unit,
  min,
  max,
  step,
  value,
  onChange,
}) => {
  const [localMin, setLocalMin] = useState(value?.min ?? min);
  const [localMax, setLocalMax] = useState(value?.max ?? max);

  const handleMinChange = useCallback((newMin: number) => {
    const adjustedMin = Math.min(newMin, localMax - step);
    setLocalMin(adjustedMin);
    
    if (adjustedMin === min && localMax === max) {
      onChange(undefined); // 重置为默认范围
    } else {
      onChange({ min: adjustedMin, max: localMax });
    }
  }, [localMax, step, min, max, onChange]);

  const handleMaxChange = useCallback((newMax: number) => {
    const adjustedMax = Math.max(newMax, localMin + step);
    setLocalMax(adjustedMax);
    
    if (localMin === min && adjustedMax === max) {
      onChange(undefined); // 重置为默认范围
    } else {
      onChange({ min: localMin, max: adjustedMax });
    }
  }, [localMin, step, min, max, onChange]);

  const isActive = value !== undefined;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.label, isActive && styles.activeLabel]}>
          {label}
        </Text>
        <Text style={[styles.range, isActive && styles.activeRange]}>
          {localMin}{unit} - {localMax}{unit}
        </Text>
      </View>
      
      <View style={styles.sliderContainer}>
        {/* 最小值滑块 */}
        <View style={styles.sliderWrapper}>
          <Text style={styles.sliderLabel}>最小值</Text>
          <Slider
            style={styles.slider}
            minimumValue={min}
            maximumValue={max}
            step={step}
            value={localMin}
            onValueChange={handleMinChange}
            minimumTrackTintColor={isActive ? '#4d920f' : '#ccc'}
            maximumTrackTintColor="#e0e0e0"
            thumbTintColor={isActive ? '#4d920f' : '#ccc'}
          />
          <Text style={styles.valueText}>{localMin}{unit}</Text>
        </View>
        
        {/* 最大值滑块 */}
        <View style={styles.sliderWrapper}>
          <Text style={styles.sliderLabel}>最大值</Text>
          <Slider
            style={styles.slider}
            minimumValue={min}
            maximumValue={max}
            step={step}
            value={localMax}
            onValueChange={handleMaxChange}
            minimumTrackTintColor={isActive ? '#4d920f' : '#ccc'}
            maximumTrackTintColor="#e0e0e0"
            thumbTintColor={isActive ? '#4d920f' : '#ccc'}
          />
          <Text style={styles.valueText}>{localMax}{unit}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  activeLabel: {
    color: '#4d920f',
  },
  range: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeRange: {
    color: '#4d920f',
    fontWeight: '600',
  },
  sliderContainer: {
    gap: 16,
  },
  sliderWrapper: {
    gap: 8,
  },
  sliderLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  slider: {
    height: 40,
  },
  valueText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default NutritionRangeSlider;

import BrandDetailHeader from '@/components/core/explore/brand-content/BrandDetailHeader';
import ReviewSection from '@/components/review/ReviewSection';
import SubmitReviewBottomBar from '@/components/review/SubmitReviewButtomBar';
import { useBrandReviews } from '@/store/brandReviewStore';
import { useBrandDetail } from '@/store/brandStore';
import { useUserInfo } from '@/store/userStore';
import { recordBrandHistory } from '@/utils/historyUtils';
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import StackHeader from '@/components/ui/StackHeader';
export default function BrandDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();

  const userInfo = useUserInfo();
  const [reviewType, setReviewType] = useState<'all' | 'long' | 'short'>('all');
  const [sortBy, setSortBy] = useState<'latest' | 'hot'>('latest');
  const {
    brand,
    isLoading: isBrandLoading,
    error: brandError
  } = useBrandDetail(id);
  
  const {
    reviews,
    isLoading: isReviewsLoading,
    isLoadingMore,
    isRefreshing,
    error: reviewsError,
    hasMore,
    refreshReviews,
    loadMoreReviews
  } = useBrandReviews(id, 5, reviewType, sortBy);

  const handleReviewTypeChange = (newType: 'all' | 'long' | 'short') => {
    setReviewType(newType);
  };

  const handleSortByChange = (newSortBy: 'latest' | 'hot') => {
    setSortBy(newSortBy);
  };


  // 记录品牌浏览历史
  useEffect(() => {
    if (userInfo?.id && brand && !isBrandLoading) {
      // 延迟记录，确保页面已经完全加载
      const timer = setTimeout(() => {
        recordBrandHistory(
          userInfo?.id || '',
          brand._id,
          brand.name,
          brand.desc,
          brand.logo_url
        );
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [userInfo?.id, brand, isBrandLoading]);

  if (isBrandLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>加载品牌信息中...</Text>
      </View>
    );
  }

  if (brandError || !brand) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Icon name="alert-circle-outline" size={60} color="#e74c3c" style={styles.errorIcon} />
        <Text style={styles.errorText}>无法加载品牌信息</Text>
        <Text style={styles.errorSubtext}>
          {brandError?.message || '请检查网络连接或稍后再试'}
        </Text>
        {__DEV__ && (
          <Text style={styles.debugText}>
            Brand ID: {id}
            {'\n'}Error: {JSON.stringify(brandError, null, 2)}
          </Text>
        )}
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StackHeader title={brand.name}/>
      <View style={styles.content}>
        <ReviewSection
          entityType="brand"
          entityId={id}
          reviews={reviews}
          isLoading={isReviewsLoading}
          isLoadingMore={isLoadingMore}
          isRefreshing={isRefreshing}
          error={reviewsError}
          reviewType={reviewType}
          onReviewTypeChange={handleReviewTypeChange}
          sortBy={sortBy}
          onSortByChange={handleSortByChange}
          hasMore={hasMore}
          onRefresh={refreshReviews}
          onLoadMore={loadMoreReviews}
          brandHeader={<BrandDetailHeader brand={brand} />}
        />
      </View>
      <SubmitReviewBottomBar
        entityType="brand"
        entityId={id}
        isFavorited={false}
        onFavoritePress={() => console.log('收藏品牌:', id)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#3498db',
  },
  debugText: {
    fontSize: 10,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    fontFamily: 'monospace',
    paddingHorizontal: 20,
  },
});


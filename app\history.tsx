import React, { useCallback, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import HistoryItem from '@/components/history/HistoryItem';
import { useHistoryStore } from '@/store/historyStore';
import { HistoryItem as HistoryItemType } from '@/types/history';
import { useUserInfo } from '@/store/userStore';

const FILTER_OPTIONS = [
  { key: 'all', label: '全部', icon: 'list-outline' },
  { key: 'thread', label: '帖子', icon: 'chatbubble-outline' },
  { key: 'product', label: '产品', icon: 'cube-outline' },
  { key: 'brand', label: '品牌', icon: 'business-outline' },
];

export default function HistoryScreen() {
  const router = useRouter();
  const userInfo = useUserInfo();
  const [showFilterModal, setShowFilterModal] = useState(false);

  const {
    items,
    stats,
    loading,
    error,
    hasMore,
    refreshing,
    selectedItems,
    isSelectionMode,
    currentFilter,
    fetchHistory,
    refreshHistory,
    loadMoreHistory,
    deleteHistoryItem,
    clearAllHistory,
    fetchHistoryStats,
    deleteMultipleItems,
    toggleSelection,
    selectAll,
    clearSelection,
    toggleSelectionMode,
    setFilter,
    reset,
  } = useHistoryStore();

  useEffect(() => {
    if (userInfo?.id) {
      fetchHistory(userInfo.id);
      fetchHistoryStats(userInfo.id);
    }

    return () => {
      reset();
    };
  }, [userInfo?.id, fetchHistory, fetchHistoryStats, reset]);

  const handleRefresh = useCallback(() => {
    if (userInfo?.id) {
      refreshHistory(userInfo.id, currentFilter);
      fetchHistoryStats(userInfo.id);
    }
  }, [userInfo?.id, currentFilter, refreshHistory, fetchHistoryStats]);

  const handleLoadMore = useCallback(() => {
    if (userInfo?.id && hasMore && !loading) {
      loadMoreHistory(userInfo.id, currentFilter);
    }
  }, [userInfo?.id, hasMore, loading, currentFilter, loadMoreHistory]);

  const handleItemPress = useCallback((item: HistoryItemType) => {
    // 根据类型跳转到对应页面
    switch (item.item_type) {
      case 'thread':
        router.push(`/(stack)/thread-detail?threadId=${item.item_id}`);
        break;
      case 'product':
        router.push(`/(stack)/product-detail?id=${item.item_id}`);
        break;
      case 'brand':
        router.push(`/(stack)/brand-detail?id=${item.item_id}`);
        break;
    }
  }, [router]);

  const handleItemLongPress = useCallback(() => {
    if (!isSelectionMode) {
      toggleSelectionMode();
    }
  }, [isSelectionMode, toggleSelectionMode]);

  const handleDeleteItem = useCallback((item: HistoryItemType) => {
    if (!userInfo?.id) return;

    Alert.alert(
      '删除记录',
      '确定要删除这条浏览记录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => deleteHistoryItem(userInfo.id!, item.id)
        }
      ]
    );
  }, [userInfo?.id, deleteHistoryItem]);

  const handleClearAll = useCallback(() => {
    if (!userInfo?.id) return;

    Alert.alert(
      '清空历史',
      '确定要清空所有浏览记录吗？此操作不可恢复。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '清空',
          style: 'destructive',
          onPress: () => clearAllHistory(userInfo.id!)
        }
      ]
    );
  }, [userInfo?.id, clearAllHistory]);

  const handleBatchDelete = useCallback(() => {
    if (!userInfo?.id) return;

    const selectedIds = Array.from(selectedItems);
    if (selectedIds.length === 0) return;

    Alert.alert(
      '批量删除',
      `确定要删除选中的 ${selectedIds.length} 条记录吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => deleteMultipleItems(userInfo.id!, selectedIds)
        }
      ]
    );
  }, [userInfo?.id, selectedItems, deleteMultipleItems]);

  const handleFilterSelect = useCallback((filterKey: string) => {
    if (userInfo?.id) {
      setFilter(userInfo.id, filterKey);
    }
    setShowFilterModal(false);
  }, [userInfo?.id, setFilter]);

  const renderHistoryItem = useCallback(({ item }: { item: HistoryItemType }) => (
    <HistoryItem
      item={item}
      isSelected={selectedItems.has(item.id)}
      isSelectionMode={isSelectionMode}
      onPress={() => handleItemPress(item)}
      onLongPress={handleItemLongPress}
      onToggleSelect={() => toggleSelection(item.id)}
      onDelete={() => handleDeleteItem(item)}
    />
  ), [selectedItems, isSelectionMode, handleItemPress, handleItemLongPress, toggleSelection, handleDeleteItem]);

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="time-outline" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>暂无浏览记录</Text>
      <Text style={styles.emptySubtitle}>您的浏览记录将显示在这里</Text>
    </View>
  );

  const renderFooter = () => {
    if (!hasMore) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>没有更多记录了</Text>
        </View>
      );
    }

    if (loading && items.length > 0) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
        </View>
      );
    }

    return null;
  };

  const currentFilterOption = FILTER_OPTIONS.find(option => option.key === currentFilter);

  if (error) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>浏览历史</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#ff4757" />
          <Text style={styles.errorTitle}>加载失败</Text>
          <Text style={styles.errorSubtitle}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryButtonText}>重试</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>浏览历史</Text>
        <TouchableOpacity onPress={() => setShowFilterModal(true)}>
          <Ionicons name="filter-outline" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      {/* 统计信息 */}
      {stats && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            共 {stats.totalItems} 条记录
            {currentFilter !== 'all' && ` · ${currentFilterOption?.label}`}
          </Text>
        </View>
      )}

      {/* 选择模式工具栏 */}
      {isSelectionMode && (
        <View style={styles.selectionToolbar}>
          <View style={styles.selectionInfo}>
            <Text style={styles.selectionText}>
              已选择 {selectedItems.size} 项
            </Text>
          </View>
          <View style={styles.selectionActions}>
            <TouchableOpacity
              style={styles.toolbarButton}
              onPress={selectAll}
            >
              <Text style={styles.toolbarButtonText}>全选</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.toolbarButton}
              onPress={clearSelection}
            >
              <Text style={styles.toolbarButtonText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.toolbarButton, styles.deleteButton]}
              onPress={handleBatchDelete}
              disabled={selectedItems.size === 0}
            >
              <Text style={[styles.toolbarButtonText, styles.deleteButtonText]}>
                删除
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* 历史记录列表 */}
      <FlatList
        data={items}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item.id || (item as any)._id || `${item.item_type}-${item.item_id}-${item.visited_at}`}
        contentContainerStyle={[
          styles.listContainer,
          items.length === 0 && styles.emptyListContainer,
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
            tintColor="#007AFF"
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        ListEmptyComponent={!loading ? renderEmptyComponent : null}
        ListFooterComponent={renderFooter}
        showsVerticalScrollIndicator={false}
      />

      {/* 底部操作栏 */}
      {!isSelectionMode && items.length > 0 && (
        <View style={styles.bottomBar}>
          <TouchableOpacity
            style={styles.bottomButton}
            onPress={toggleSelectionMode}
          >
            <Ionicons name="checkmark-circle-outline" size={20} color="#007AFF" />
            <Text style={styles.bottomButtonText}>选择</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.bottomButton}
            onPress={handleClearAll}
          >
            <Ionicons name="trash-outline" size={20} color="#ff4757" />
            <Text style={[styles.bottomButtonText, { color: '#ff4757' }]}>清空</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 筛选模态框 */}
      <Modal
        visible={showFilterModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowFilterModal(false)}
        >
          <View style={styles.filterModal}>
            <Text style={styles.filterTitle}>筛选类型</Text>
            {FILTER_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.filterOption,
                  currentFilter === option.key && styles.selectedFilterOption,
                ]}
                onPress={() => handleFilterSelect(option.key)}
              >
                <Ionicons
                  name={option.icon as any}
                  size={20}
                  color={currentFilter === option.key ? '#007AFF' : '#666'}
                />
                <Text
                  style={[
                    styles.filterOptionText,
                    currentFilter === option.key && styles.selectedFilterOptionText,
                  ]}
                >
                  {option.label}
                </Text>
                {currentFilter === option.key && (
                  <Ionicons name="checkmark" size={20} color="#007AFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* 加载状态 */}
      {loading && items.length === 0 && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  statsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  statsText: {
    fontSize: 14,
    color: '#666',
  },
  selectionToolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#f0f8ff',
    borderBottomWidth: 1,
    borderBottomColor: '#007AFF',
  },
  selectionInfo: {
    flex: 1,
  },
  selectionText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  selectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toolbarButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 8,
    borderRadius: 6,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  toolbarButtonText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: '#ff4757',
    borderColor: '#ff4757',
  },
  deleteButtonText: {
    color: '#fff',
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  errorSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(248, 249, 250, 0.8)',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  footerContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#666',
  },
  bottomBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingVertical: 16,
    paddingHorizontal: 32,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  bottomButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  bottomButtonText: {
    fontSize: 16,
    color: '#007AFF',
    marginLeft: 8,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterModal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    margin: 20,
    minWidth: 200,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 4,
  },
  selectedFilterOption: {
    backgroundColor: '#f0f8ff',
  },
  filterOptionText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
    flex: 1,
  },
  selectedFilterOptionText: {
    color: '#007AFF',
    fontWeight: '500',
  },
});

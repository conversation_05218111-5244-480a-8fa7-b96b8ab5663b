
export type ProductSearchResponse = {
    products: SearchProduct[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
}

export type SearchProduct = {
    id: string;
    _id?: string; // 添加_id字段以兼容后端数据
    name: string;
    brand: string;
    image_url: string;
    product_type: string;
    category?: string; // 添加category字段，通常等于product_type
    rating: number;
    review_count: number;
}


export type BrandSearchResponse ={
    brands:SearchBrand[],
    totalCount: number;
    totalPages: number;
    currentPage: number;
}

export type SearchBrand = {
    id:string,
    name:string,
    image_url:string,
    products:number,
    rating: number,
    review_count: number
}

export type ThreadSearchResponse = {
    threads:SearchThread[],
    totalCount: number;
    totalPages: number;
    currentPage: number;
}

export type SearchThread = {
    thread_id:string,
    title:string,
    content:string,
    user_id:string,
    like_count:number,
    reply_count:number,
    created_at:string,
    user: {
        id:string,
        username:string,
        avatar:string
    }
}
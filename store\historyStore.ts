import { create } from 'zustand';
import historyService, { HistoryItem, HistoryResponse, CreateHistoryRequest, HistoryStats } from '@/api/services/historyService';

interface HistoryState {
  items: HistoryItem[];
  stats: HistoryStats | null;
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
  totalCount: number;
  refreshing: boolean;
  selectedItems: Set<string>;
  isSelectionMode: boolean;
  currentFilter: string;
}

interface HistoryActions {
  fetchHistory: (userId: string, page?: number, itemType?: string) => Promise<void>;
  refreshHistory: (userId: string, itemType?: string) => Promise<void>;
  loadMoreHistory: (userId: string, itemType?: string) => Promise<void>;
  addHistoryItem: (userId: string, data: CreateHistoryRequest) => Promise<void>;
  deleteHistoryItem: (userId: string, historyId: string) => Promise<void>;
  clearAllHistory: (userId: string) => Promise<void>;
  fetchHistoryStats: (userId: string) => Promise<void>;
  deleteMultipleItems: (userId: string, historyIds: string[]) => Promise<void>;
  toggleSelection: (historyId: string) => void;
  selectAll: () => void;
  clearSelection: () => void;
  toggleSelectionMode: () => void;
  setFilter: (userId: string, filter: string) => void;
  reset: () => void;
}

type HistoryStore = HistoryState & HistoryActions;

const initialState: HistoryState = {
  items: [],
  stats: null,
  loading: false,
  error: null,
  hasMore: true,
  currentPage: 1,
  totalPages: 1,
  totalCount: 0,
  refreshing: false,
  selectedItems: new Set(),
  isSelectionMode: false,
  currentFilter: 'all',
};

export const useHistoryStore = create<HistoryStore>((set, get) => ({
  ...initialState,

  fetchHistory: async (userId: string, page = 1, itemType = 'all') => {
    const { items } = get();

    try {
      set({
        loading: page === 1,
        error: null,
        refreshing: page === 1 && items.length > 0
      });

      const response: HistoryResponse = await historyService.getUserHistory(userId, page, 20, itemType);
      
      const newItems = response.items || [];
      
      set({
        items: page === 1 ? newItems : [...items, ...newItems],
        loading: false,
        refreshing: false,
        currentPage: response.currentPage || page,
        totalPages: response.totalPages || 1,
        totalCount: response.totalCount || 0,
        hasMore: (response.currentPage || page) < (response.totalPages || 1),
        error: null,
        currentFilter: itemType,
      });
    } catch (error: any) {
      console.error('Fetch history error:', error);
      set({
        loading: false,
        refreshing: false,
        error: error.message || '获取浏览历史失败',
      });
    }
  },

  refreshHistory: async (userId: string, itemType = 'all') => {
    await get().fetchHistory(userId, 1, itemType);
  },

  loadMoreHistory: async (userId: string, itemType = 'all') => {
    const { hasMore, loading, currentPage } = get();

    if (!hasMore || loading) return;

    await get().fetchHistory(userId, currentPage + 1, itemType);
  },

  addHistoryItem: async (userId: string, data: CreateHistoryRequest) => {
    try {
      const newItem = await historyService.addHistoryItem(userId, data);
      const { items } = get();
      
      // 检查是否已存在相同的记录，如果存在则更新，否则添加到顶部
      const existingIndex = items.findIndex(item => 
        item.item_type === data.item_type && item.item_id === data.item_id
      );
      
      if (existingIndex !== -1) {
        // 更新现有记录并移到顶部
        const updatedItems = [...items];
        updatedItems.splice(existingIndex, 1);
        updatedItems.unshift(newItem);
        set({ items: updatedItems });
      } else {
        // 添加新记录到顶部
        set({ items: [newItem, ...items] });
      }
    } catch (error: any) {
      console.error('Add history item error:', error);
      set({ error: error.message || '添加浏览记录失败' });
    }
  },

  deleteHistoryItem: async (userId: string, historyId: string) => {
    try {
      await historyService.deleteHistoryItem(userId, historyId);
      const { items } = get();
      set({ 
        items: items.filter(item => item.id !== historyId),
        selectedItems: new Set([...get().selectedItems].filter(id => id !== historyId))
      });
    } catch (error: any) {
      console.error('Delete history item error:', error);
      set({ error: error.message || '删除记录失败' });
    }
  },

  clearAllHistory: async (userId: string) => {
    try {
      await historyService.clearAllHistory(userId);
      set({ 
        items: [],
        selectedItems: new Set(),
        isSelectionMode: false,
        totalCount: 0,
      });
    } catch (error: any) {
      console.error('Clear all history error:', error);
      set({ error: error.message || '清空历史记录失败' });
    }
  },

  fetchHistoryStats: async (userId: string) => {
    try {
      const stats = await historyService.getHistoryStats(userId);
      set({ stats });
    } catch (error: any) {
      console.error('Fetch history stats error:', error);
      set({ error: error.message || '获取统计信息失败' });
    }
  },

  deleteMultipleItems: async (userId: string, historyIds: string[]) => {
    try {
      await historyService.deleteMultipleHistoryItems(userId, historyIds);
      const { items } = get();
      set({ 
        items: items.filter(item => !historyIds.includes(item.id)),
        selectedItems: new Set(),
        isSelectionMode: false,
      });
    } catch (error: any) {
      console.error('Delete multiple items error:', error);
      set({ error: error.message || '批量删除失败' });
    }
  },

  toggleSelection: (historyId: string) => {
    const { selectedItems } = get();
    const newSelection = new Set(selectedItems);
    
    if (newSelection.has(historyId)) {
      newSelection.delete(historyId);
    } else {
      newSelection.add(historyId);
    }
    
    set({ selectedItems: newSelection });
  },

  selectAll: () => {
    const { items } = get();
    set({ selectedItems: new Set(items.map(item => item.id)) });
  },

  clearSelection: () => {
    set({ selectedItems: new Set(), isSelectionMode: false });
  },

  toggleSelectionMode: () => {
    const { isSelectionMode } = get();
    set({ 
      isSelectionMode: !isSelectionMode,
      selectedItems: new Set(),
    });
  },

  setFilter: (userId: string, filter: string) => {
    set({ currentFilter: filter });
    get().refreshHistory(userId, filter);
  },

  reset: () => {
    set(initialState);
  },
}));

// Selector hooks for better performance
export const useHistoryItems = () => useHistoryStore(state => state.items);
export const useHistoryLoading = () => useHistoryStore(state => state.loading);
export const useHistoryError = () => useHistoryStore(state => state.error);
export const useHistoryStats = () => useHistoryStore(state => state.stats);
export const useHistorySelection = () => useHistoryStore(state => ({
  selectedItems: state.selectedItems,
  isSelectionMode: state.isSelectionMode,
}));

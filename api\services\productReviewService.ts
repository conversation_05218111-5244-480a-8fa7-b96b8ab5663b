import apiClient from "../apiClient";

export interface ProductReviewSummary {
	_id: string;
	product_id: string;
	average_rating: number;
	total_rating: number;
	review_count: number;
	short_review_count: number;
	long_review_count: number;
	created_at: string;
	updated_at: string;
	product_name: string;
	brand: string;
}

export interface Review {
	_id: string;
	review_id: string;
	product_id: string;
	user_id: string;
	content: string;
	rating: number;
	review_time: string;
	liked_by: string[];
	disliked_by: string[];
	parent_review_id: string | null;
	is_recommended: boolean;
	review_status: boolean;
	report_count: number;
	reply_count: number;
	images: string[];
	is_long_review: boolean;
	title?: string;
	likes: number;
	dislikes: number;
	replies?: Review[];
}

export interface PopularProduct {
	product_id: string;
	name: string;
	brand: string;
	image_url?: string;
	product_type?: string; // 确保包含此字段
	est_calories?: string; // 确保包含此字段
	average_rating: number | string;
	review_count: number;
}

export type ProductReviewSummaryResponse = {
	products: ProductReviewSummary[];
	totalCount: number;
	totalPages: number;
	currentPage: number;
	sortBy: string;
	sortOrder: string;
};

export type ProductReviewsResponse = {
	reviews: Review[];
	totalCount: number;
	totalPages: number;
	currentPage: number;
};

// Updated response type to match the actual API response
export interface PopularProductsResponse {
	success: boolean;
	status: number;
	data: PopularProduct[];
	message: string;
	totalCount: number;
	totalPages: number;
	currentPage: number;
}

export enum ProductReviewApi {
	ProductReviews = "reviews",
}

export enum ReviewApi {
	Reviews = "reviews",
}

// Get product review summaries
const getProductReviewSummaries = async (page = 1, limit = 10, sortBy = "review_count", sortOrder = "desc") => {
	try {
		const response = await apiClient.get<ProductReviewSummaryResponse>({
			url: `${ProductReviewApi.ProductReviews}/products-with-reviews`,
			params: { page, limit, sortBy, sortOrder },
		});
		return response;
	} catch (error) {
		console.error("Product Review Summaries API error:", error);
		throw error;
	}
};

// Get reviews for specific product
const getReviewsByProductId = async (productId: string, page = 1, limit = 10, type?: string, sortBy?: string) => {
	try {
		const response = await apiClient.get<ProductReviewsResponse>({
			url: `${ProductReviewApi.ProductReviews}/product/${productId}`,
			params: { 
				page, 
				limit, 
				...(type && { type }), // Add type parameter if provided
				...(sortBy && { sortBy }), // Add sortBy parameter if provided
				includeReplies: true, 
				maxReplyDepth: 2,
				includeUserInfo: true
			},
		});
		return response;
	} catch (error) {
		console.error(`Product Reviews API error for product ${productId}:`, error);
		throw error;
	}
};

// Get specific review by ID
const getReviewById = async (reviewId: string) => {
	try {
		const response = await apiClient.get<Review>({
			url: `${ReviewApi.Reviews}/${reviewId}`,
		});
		return response;
	} catch (error) {
		console.error(`Review API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Update review status (approve/reject)
const updateReviewStatus = async (reviewId: string, status: boolean) => {
	try {
		// Use put instead of patch if patch is not available on the APIClient
		const response = await apiClient.put<Review>({
			url: `${ReviewApi.Reviews}/${reviewId}/status`,
			data: { review_status: status },
		});
		return response;
	} catch (error) {
		console.error(`Review status update API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Update review content
const updateReviewContent = async (reviewId: string, data: Partial<Review>) => {
	try {
		const response = await apiClient.put<Review>({
			url: `${ReviewApi.Reviews}/${reviewId}`,
			data,
		});
		return response;
	} catch (error) {
		console.error(`Review update API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Delete review
const deleteReview = async (reviewId: string) => {
	try {
		const response = await apiClient.delete<Review>({
			url: `${ReviewApi.Reviews}/${reviewId}`,
		});
		return response;
	} catch (error) {
		console.error(`Review delete API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Create new review
const createReview = async (reviewData: Partial<Review>) => {
	try {
		const response = await apiClient.post<Review>({
			url: `${ReviewApi.Reviews}`,
			data: reviewData,
		});
		return response;
	} catch (error) {
		console.error("Review creation API error:", error);
		throw error;
	}
};

// Get popular products
const getPopularProducts = async (limit = 10) => {
	try {
		const response = await apiClient.get<PopularProductsResponse>({
			url: `${ReviewApi.Reviews}/popular-products`,
			params: { limit },
		});
		
		// Return the full response so we can handle it properly in the store
		return response;
	} catch (error) {
		// console.error("Popular Products API error:", error);
		throw error;
	}
};

// Get daily random products
const getDailyRandomProducts = async (limit = 9) => {
	try {
		const response = await apiClient.get<PopularProduct[]>({
			url: `${ProductReviewApi.ProductReviews}/daily-random-products`,
			params: { limit },
		});
		
		return response;
	} catch (error) {
		console.error("Daily Random Products API error:", error);
		throw error;
	}
};

// Get replies for a specific review
const getReviewReplies = async (reviewId: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<ProductReviewsResponse>({
			url: `${ReviewApi.Reviews}/${reviewId}/replies`,
			params: { page, limit, maxReplyDepth: 3 },
		});
		return response;
	} catch (error) {
		console.error(`Review replies API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Get long reviews for a specific product
const getLongReviews = async (
	productId: string,
	page = 1,
	limit = 10,
	includeReplies = true,
	maxReplyDepth = 3,
	sortBy = 'latest'
) => {
	try {
		const response = await apiClient.get<ProductReviewsResponse>({
			url: `${ProductReviewApi.ProductReviews}/product/${productId}`,
			params: { page, limit, type: "long", includeReplies, maxReplyDepth, sortBy },
		});
		return response;
	} catch (error) {
		console.error(`Long Product Reviews API error for product ${productId}:`, error);
		throw error;
	}
};

// Get short reviews for a specific product
const getShortReviews = async (
	productId: string,
	page = 1,
	limit = 10,
	includeReplies = true,
	maxReplyDepth = 3,
	sortBy = 'latest'
) => {
	try {
		const response = await apiClient.get<ProductReviewsResponse>({
			url: `${ProductReviewApi.ProductReviews}/product/${productId}`,
			params: { page, limit, type: "short", includeReplies, maxReplyDepth, sortBy },
		});
		return response;
	} catch (error) {
		console.error(`Short Product Reviews API error for product ${productId}:`, error);
		throw error;
	}
};

// Get product review summary
const getProductReviewSummary = async (productId: string) => {
	try {
		const response = await apiClient.get<ProductReviewSummary>({
			url: `${ProductReviewApi.ProductReviews}/summary/${productId}`,
		});
		return response;
	} catch (error) {
		console.error(`Product Review Summary API error for product ${productId}:`, error);
		throw error;
	}
};

// Check if user has already reviewed a product
const hasUserReviewedProduct = async (productId: string, userId: string) => {
	try {
		const response = await apiClient.get<{ hasReviewed: boolean }>({
			url: `${ProductReviewApi.ProductReviews}/product/${productId}/user/${userId}/has-reviewed`,
		});
		return response;
	} catch (error) {
		console.error(`Has user reviewed product API error for product ${productId}:`, error);
		throw error;
	}
};

// Get user's review for a specific product
const getUserReviewForProduct = async (productId: string, userId: string) => {
	try {
		const response = await apiClient.get<Review | null>({
			url: `${ProductReviewApi.ProductReviews}/product/${productId}/user/${userId}/review`,
		});
		return response;
	} catch (error) {
		console.error(`Get user review for product API error for product ${productId}:`, error);
		throw error;
	}
};

// Get rating distribution for a product
const getRatingDistribution = async (productId: string) => {
	try {
		const response = await apiClient.get<{
			distribution: { [key: string]: number },
			percentages: number[]
		}>({
			url: `${ProductReviewApi.ProductReviews}/product/${productId}/rating-distribution`,
		});
		return response;
	} catch (error) {
		console.error(`Get rating distribution API error for product ${productId}:`, error);
		throw error;
	}
};

// Like a review
const likeReview = async (reviewId: string) => {
	try {
		const response = await apiClient.post<{
			review_id: string;
			likes: number;
			dislikes: number;
			liked_by: string[];
			disliked_by: string[];
		}>({
			url: `${ReviewApi.Reviews}/${reviewId}/like`,
			data: {}, // user_id will be extracted from Authorization header by backend
		});
		return response;
	} catch (error) {
		console.error(`Review like API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Unlike a review
const unlikeReview = async (reviewId: string) => {
	try {
		const response = await apiClient.post<{
			review_id: string;
			likes: number;
			dislikes: number;
			liked_by: string[];
			disliked_by: string[];
		}>({
			url: `${ReviewApi.Reviews}/${reviewId}/unlike`,
			data: {}, // user_id will be extracted from Authorization header by backend
		});
		return response;
	} catch (error) {
		console.error(`Review unlike API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Dislike a review
const dislikeReview = async (reviewId: string) => {
	try {
		const response = await apiClient.post<{
			review_id: string;
			likes: number;
			dislikes: number;
			liked_by: string[];
			disliked_by: string[];
		}>({
			url: `${ReviewApi.Reviews}/${reviewId}/dislike`,
			data: {}, // user_id will be extracted from Authorization header by backend
		});
		return response;
	} catch (error) {
		console.error(`Review dislike API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Undislike a review
const undislikeReview = async (reviewId: string) => {
	try {
		const response = await apiClient.post<{
			review_id: string;
			likes: number;
			dislikes: number;
			liked_by: string[];
			disliked_by: string[];
		}>({
			url: `${ReviewApi.Reviews}/${reviewId}/undislike`,
			data: {}, // user_id will be extracted from Authorization header by backend
		});
		return response;
	} catch (error) {
		console.error(`Review undislike API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Get user reaction status for a review
const getUserReactionStatus = async (reviewId: string) => {
	try {
		const response = await apiClient.get<{
			liked: boolean;
			disliked: boolean;
		}>({
			url: `${ReviewApi.Reviews}/${reviewId}/reaction-status`, // Remove userId from URL, use auth header
		});
		return response;
	} catch (error) {
		console.error(`Get user reaction status API error for review ${reviewId}:`, error);
		throw error;
	}
};

export default {
	getProductReviewSummaries,
	getReviewsByProductId,
	getReviewById,
	updateReviewStatus,
	updateReviewContent,
	deleteReview,
	createReview,
	getPopularProducts,
	getDailyRandomProducts,
	getLongReviews,
	getShortReviews,
	getProductReviewSummary,
	getReviewReplies,
	hasUserReviewedProduct,
	getUserReviewForProduct,
	getRatingDistribution,
	likeReview,
	unlikeReview,
	dislikeReview,
	undislikeReview,
	getUserReactionStatus,
};

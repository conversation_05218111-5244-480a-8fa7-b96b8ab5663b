import { create } from 'zustand';
import userReviewService, { UserReview, CombinedUserReviewsResponse } from '@/api/services/userReviewService';

interface UserReviewState {
  // 数据状态
  productReviews: UserReview[];
  brandReviews: UserReview[];
  allReviews: UserReview[];
  
  // 分页信息
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasMore: boolean;
  
  // 加载状态
  loading: boolean;
  refreshing: boolean;
  error: string | null;
  
  // 当前筛选类型
  currentFilter: 'all' | 'product' | 'brand';
}

interface UserReviewActions {
  // 获取用户点评
  fetchUserReviews: (userId: string, page?: number, reviewType?: 'all' | 'product' | 'brand') => Promise<void>;
  refreshUserReviews: (userId: string, reviewType?: 'all' | 'product' | 'brand') => Promise<void>;
  loadMoreUserReviews: (userId: string, reviewType?: 'all' | 'product' | 'brand') => Promise<void>;
  
  // 筛选操作
  setFilter: (userId: string, filter: 'all' | 'product' | 'brand') => void;
  
  // 状态重置
  reset: () => void;
  clearError: () => void;
}

type UserReviewStore = UserReviewState & UserReviewActions;

const initialState: UserReviewState = {
  productReviews: [],
  brandReviews: [],
  allReviews: [],
  totalCount: 0,
  totalPages: 0,
  currentPage: 0,
  hasMore: false,
  loading: false,
  refreshing: false,
  error: null,
  currentFilter: 'all',
};

export const useUserReviewStore = create<UserReviewStore>((set, get) => ({
  ...initialState,

  fetchUserReviews: async (userId: string, page = 1, reviewType = 'all') => {
    const { allReviews } = get();
    
    try {
      set({ 
        loading: page === 1, 
        error: null,
        refreshing: page === 1 && allReviews.length > 0
      });

      let response: CombinedUserReviewsResponse;

      switch (reviewType) {
        case 'product':
          const productResponse = await userReviewService.getUserProductReviews(userId, page, 20);
          response = {
            productReviews: productResponse.reviews,
            brandReviews: [],
            allReviews: productResponse.reviews,
            totalCount: productResponse.totalCount,
            totalPages: productResponse.totalPages,
            currentPage: productResponse.currentPage
          };
          break;
        case 'brand':
          const brandResponse = await userReviewService.getUserBrandReviews(userId, page, 20);
          response = {
            productReviews: [],
            brandReviews: brandResponse.reviews,
            allReviews: brandResponse.reviews,
            totalCount: brandResponse.totalCount,
            totalPages: brandResponse.totalPages,
            currentPage: brandResponse.currentPage
          };
          break;
        default:
          response = await userReviewService.getUserAllReviews(userId, page, 20);
          break;
      }

      const newReviews = response.allReviews;
      const existingReviews = page === 1 ? [] : get().allReviews;
      const updatedReviews = page === 1 ? newReviews : [...existingReviews, ...newReviews];

      set({
        productReviews: response.productReviews,
        brandReviews: response.brandReviews,
        allReviews: updatedReviews,
        totalCount: response.totalCount,
        totalPages: response.totalPages,
        currentPage: response.currentPage,
        hasMore: response.currentPage < response.totalPages,
        loading: false,
        refreshing: false,
        currentFilter: reviewType,
      });
    } catch (error: any) {
      console.error('Fetch user reviews error:', error);
      set({
        loading: false,
        refreshing: false,
        error: error.message || '获取用户点评失败',
      });
    }
  },

  refreshUserReviews: async (userId: string, reviewType = 'all') => {
    await get().fetchUserReviews(userId, 1, reviewType);
  },

  loadMoreUserReviews: async (userId: string, reviewType = 'all') => {
    const { hasMore, loading, currentPage } = get();
    
    if (!hasMore || loading) return;
    
    await get().fetchUserReviews(userId, currentPage + 1, reviewType);
  },

  setFilter: (userId: string, filter: 'all' | 'product' | 'brand') => {
    set({ currentFilter: filter });
    get().refreshUserReviews(userId, filter);
  },

  reset: () => {
    set(initialState);
  },

  clearError: () => {
    set({ error: null });
  },
}));

// 导出选择器函数
export const useUserReviews = () => {
  const store = useUserReviewStore();
  return {
    allReviews: store.allReviews,
    productReviews: store.productReviews,
    brandReviews: store.brandReviews,
    totalCount: store.totalCount,
    hasMore: store.hasMore,
    loading: store.loading,
    refreshing: store.refreshing,
    error: store.error,
    currentFilter: store.currentFilter,
  };
};

export const useUserReviewActions = () => {
  const store = useUserReviewStore();
  return {
    fetchUserReviews: store.fetchUserReviews,
    refreshUserReviews: store.refreshUserReviews,
    loadMoreUserReviews: store.loadMoreUserReviews,
    setFilter: store.setFilter,
    reset: store.reset,
    clearError: store.clearError,
  };
};

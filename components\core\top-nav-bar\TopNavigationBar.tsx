
import React from 'react';
import { StyleSheet } from 'react-native';
import ExtendSideBarButton from './ExtendSideBarButton';
import PrivateMessageButton from './PrivateMessageButton';
import SearchButton from './SearchButton';
import { SafeAreaView } from 'react-native-safe-area-context';


export default function TopNavigationBar() {
  return (
    <SafeAreaView style={styles.container}>
      <ExtendSideBarButton />
      <SearchButton />
      <PrivateMessageButton />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical:10,
    zIndex: 100,
  },
});

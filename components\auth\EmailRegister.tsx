import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { ActivityIndicator, Alert, Dimensions, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { useSignUp } from '@/store/userStore';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface EmailRegisterProps {
  onBackToLogin: () => void;
}

export default function EmailRegister({ onBackToLogin }: EmailRegisterProps) {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [username, setUsername] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const signUpMutation = useSignUp();

  // 验证邮箱格式
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 验证密码强度
  const validatePassword = (password: string) => {
    return password.length >= 6;
  };

  const handleRegister = async () => {
    // 表单验证
    if (!username.trim()) {
      Alert.alert('错误', '请输入用户名');
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert('错误', '请输入正确的邮箱地址');
      return;
    }

    if (!validatePassword(password)) {
      Alert.alert('错误', '密码至少需要6位字符');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('错误', '两次输入的密码不一致');
      return;
    }

    try {
      await signUpMutation.mutateAsync({
        username: username.trim(),
        email: email.trim().toLowerCase(),
        password
      });

      // 注册成功，跳转到验证页面
      Alert.alert(
        '注册成功',
        '验证码已发送到您的邮箱，请查收并验证',
        [
          {
            text: '确定',
            onPress: () => router.push(`/verify-code?email=${encodeURIComponent(email)}&type=email`)
          }
        ]
      );
    } catch (error) {
      // 错误处理已在 mutation 中完成
      console.error('Registration error:', error);
    }
  };

  const isFormValid = username.trim() && 
                     validateEmail(email) && 
                     validatePassword(password) && 
                     password === confirmPassword;

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={onBackToLogin}
        >
          <Ionicons name="chevron-back" size={36} color="#333" />
        </TouchableOpacity>
        <View style={styles.header}>
          <Text style={styles.title}>邮箱注册</Text>
          <Text style={styles.subtitle}>创建您的新账户</Text>
        </View>
      </View>

      {/* 用户名输入 */}
      <View style={styles.inputContainer}>
        <Icon name="account-outline" size={20} color="#666" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="请输入用户名"
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
          autoComplete="username"
        />
      </View>

      {/* 邮箱输入 */}
      <View style={styles.inputContainer}>
        <Icon name="email-outline" size={20} color="#666" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="请输入邮箱"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          autoComplete="email"
        />
      </View>

      {/* 密码输入 */}
      <View style={styles.inputContainer}>
        <Icon name="lock-outline" size={20} color="#666" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="请输入密码（至少6位）"
          value={password}
          onChangeText={setPassword}
          secureTextEntry={!showPassword}
          autoComplete="new-password"
        />
        <TouchableOpacity style={styles.eyeIcon} onPress={() => setShowPassword(!showPassword)}>
          <Icon
            name={showPassword ? 'eye-outline' : 'eye-off-outline'}
            size={20}
            color="#666"
          />
        </TouchableOpacity>
      </View>

      {/* 确认密码输入 */}
      <View style={styles.inputContainer}>
        <Icon name="lock-check-outline" size={20} color="#666" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="请再次输入密码"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry={!showConfirmPassword}
          autoComplete="new-password"
        />
        <TouchableOpacity style={styles.eyeIcon} onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
          <Icon
            name={showConfirmPassword ? 'eye-outline' : 'eye-off-outline'}
            size={20}
            color="#666"
          />
        </TouchableOpacity>
      </View>

      {/* 注册按钮 */}
      <TouchableOpacity
        style={[styles.primaryButton, (!isFormValid || signUpMutation.isPending) && styles.primaryButtonDisabled]}
        onPress={handleRegister}
        disabled={!isFormValid || signUpMutation.isPending}
      >
        {signUpMutation.isPending ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.primaryButtonText}>注册</Text>
        )}
      </TouchableOpacity>

      {/* 返回登录链接 */}
      <TouchableOpacity
        style={styles.loginLinkContainer}
        onPress={onBackToLogin}
      >
        <Text style={styles.loginLinkText}>已有账户？返回登录</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    flex: 1,
    paddingHorizontal: Math.max(20, screenWidth * 0.05),
    paddingTop: Math.max(20, screenHeight * 0.02),
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Math.max(30, screenHeight * 0.04),
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  header: {
    flex: 1,
  },
  title: {
    fontSize: Math.max(22, screenWidth * 0.06),
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: Math.max(13, screenWidth * 0.035),
    color: '#999',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: Math.max(16, screenWidth * 0.04),
    paddingVertical: Math.max(14, screenHeight * 0.018),
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
    minHeight: Math.max(50, screenHeight * 0.06),
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: Math.max(15, screenWidth * 0.04),
    color: '#333',
    padding: 0,
  },
  eyeIcon: {
    padding: 4,
  },
  primaryButton: {
    backgroundColor: '#3498db',
    paddingVertical: Math.max(16, screenHeight * 0.02),
    borderRadius: 12,
    alignItems: 'center',
    marginTop: Math.max(24, screenHeight * 0.03),
    minHeight: Math.max(50, screenHeight * 0.06),
    justifyContent: 'center',
    shadowColor: '#3498db',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  primaryButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: Math.max(16, screenWidth * 0.045),
    fontWeight: '600',
  },
  loginLinkContainer: {
    alignItems: 'center',
    marginTop: 24,
    paddingVertical: 16,
  },
  loginLinkText: {
    color: '#3498db',
    fontSize: 14,
    fontWeight: '500',
  },
});

import StackHeader from '@/components/ui/StackHeader';
import { useCurrentUserData, useUserActions, useUserInfo } from '@/store/userStore';
import { router } from 'expo-router';
import React, { useEffect } from 'react';
import {
  Alert,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

export default function SettingsScreen() {
  // 用户相关的hooks
  const userInfo = useUserInfo();
  const { clearUserInfoAndToken } = useUserActions();
  const { data: currentUser, isLoading: userLoading } = useCurrentUserData();
  
  // 使用最新的用户数据，如果有的话
  const displayUser = currentUser || userInfo;

  useEffect(() => {
    loadCacheStats();
  }, []);

  const loadCacheStats = async () => {
    // expo-image 自动管理缓存，无需手动统计
  };


  const handleLogout = () => {
    Alert.alert(
      "确认退出",
      "确定要退出登录吗？",
      [
        { text: "取消", style: "cancel" },
        {
          text: "退出",
          style: "destructive",
          onPress: async () => {
            try {
              // 清除用户信息和token
              clearUserInfoAndToken();

              // expo-image 会自动管理缓存，无需手动清理

              // AuthGuard 会自动检测到用户状态变化并重定向到登录页
              // 不需要手动导航

              Alert.alert("提示", "已退出登录");
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert("错误", "退出登录时发生错误");
            }
          }
        }
      ]
    );
  };

  const handleSwitchAccount = () => {
    Alert.alert(
      "切换账户",
      "确定要切换到其他账户吗？当前会话将被清除。",
      [
        { text: "取消", style: "cancel" },
        {
          text: "切换",
          onPress: async () => {
            try {
              // 清除当前用户信息
              clearUserInfoAndToken();

              // AuthGuard 会自动检测到用户状态变化并重定向到登录页
              // 不需要手动导航

              Alert.alert("提示", "请登录其他账户");
            } catch (_error) {
              console.error('Switch account error:', _error);
              Alert.alert("错误", "切换账户时发生错误");
            }
          }
        }
      ]
    );
  };

  const handleEditProfile = () => {
    if (displayUser?.id) {
      router.push({
        pathname: '/(stack)/me',
        params: { userId: displayUser.id }
      });
    }
  };

  const renderSettingItem = (
    icon: string, 
    title: string, 
    subtitle?: string, 
    onPress?: () => void,
    showArrow: boolean = true,
    rightElement?: React.ReactNode
  ) => (
    <TouchableOpacity 
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingLeft}>
        <Icon name={icon} size={24} color="#666" />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightElement || (showArrow && <Icon name="chevron-right" size={20} color="#ccc" />)}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <StackHeader title='设置' />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 用户信息部分 */}
        <View style={styles.userSection}>
          <TouchableOpacity style={styles.userInfo} onPress={handleEditProfile}>
            <View style={styles.avatar}>
              {displayUser?.avatar ? (
                <Image source={{ uri: displayUser.avatar }} style={styles.avatarImage} />
              ) : (
                <Icon name="account" size={40} color="#666" />
              )}
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>
                {displayUser?.username || '用户'}
              </Text>
              <Text style={styles.userEmail}>
                {displayUser?.email || '<EMAIL>'}
              </Text>
              {userLoading && <Text style={styles.loadingUser}>更新中...</Text>}
            </View>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
        </View>


        {/* 通用设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>通用设置</Text>
          {renderSettingItem(
            'help-circle-outline',
            '帮助与支持',
            '常见问题和联系我们'
          )}
        </View>

        {/* 账户管理 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>账户管理</Text>
        
          {renderSettingItem(
            'account-switch',
            '切换账户',
            '登录其他账户',
            handleSwitchAccount
          )}

          {renderSettingItem(
            'logout',
            '退出登录',
            '退出当前账户',
            handleLogout,
            false,
            <Icon name="chevron-right" size={20} color="#ff6b6b" />
          )}
        </View>

        {/* 关于 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>关于</Text>
          
          {renderSettingItem(
            'information-outline',
            '应用版本',
            '1.0.0 Dev',
            undefined,
            false
          )}

          {renderSettingItem(
            'file-document-outline',
            '用户协议',
            '服务条款和隐私政策'
          )}
        </View>
      </ScrollView>
    </SafeAreaView >
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingTop: 20,
  },
  userSection: {
    backgroundColor: 'white',
    paddingVertical: 20,
    marginBottom: 20,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  avatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  loadingUser: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  section: {
    backgroundColor: 'white',
    marginBottom: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 12,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  clearButton: {
    backgroundColor: '#ff6b6b',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  disabledText: {
    color: '#ccc',
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

import productReviewService from '@/api/services/productReviewService';
import { useQuery } from '@tanstack/react-query';

// Hook to get product rating distribution
export const useProductRatingDistribution = (productId: string) => {
  return useQuery({
    queryKey: ['productRatingDistribution', productId],
    queryFn: async () => {
      if (!productId) return { distribution: {}, percentages: [0, 0, 0, 0, 0] };
      const response = await productReviewService.getRatingDistribution(productId);
      return response;
    },
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to get product review summary
export const useProductReviewSummary = (productId: string) => {
  return useQuery({
    queryKey: ['productReviewSummary', productId],
    queryFn: async () => {
      if (!productId) return null;
      const response = await productReviewService.getProductReviewSummary(productId);
      return response;
    },
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

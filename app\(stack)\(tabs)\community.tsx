import type { ThreadSortBy } from '@/api/services/threadsService';
import CreateThreadModal from '@/components/threads/CreateThreadModal';
import ThreadCard from '@/components/threads/ThreadCard';
import { globalEvents } from '@/components/ui/BottomTabBar';
import { useThreads } from '@/store/threadsStore';
import { useUserInfo } from '@/store/userStore';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  RefreshControl,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function CommunityScreen() {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const router = useRouter();
  const userInfo = useUserInfo();

  // 监听全局创建帖子事件
  useEffect(() => {
    const handleGlobalCreatePost = () => {
      setCreateModalVisible(true);
    };

    globalEvents.on('createPost', handleGlobalCreatePost);

    return () => {
      globalEvents.off('createPost', handleGlobalCreatePost);
    };
  }, []);

  const {
    threads,
    isLoading,
    error,
    hasMore,
    loadMoreThreads,
    isLoadingMore,
    createThread,
    toggleLike,
    toggleBookmark,
    refreshThreads,
    isCreating,
    isLiking,
    isBookmarking,
    sortBy,
    setSortBy
  } = useThreads();

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await refreshThreads();
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshThreads]);

  // 加载更多
  const handleLoadMore = useCallback(() => {
    if (hasMore && !isLoadingMore) {
      loadMoreThreads();
    }
  }, [hasMore, isLoadingMore, loadMoreThreads]);

  // 点赞处理
  const handleLike = useCallback(async (threadId: string) => {
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录');
      return;
    }
    try {
      toggleLike({ threadId, userId: userInfo.id });
    } catch (error: any) {
      Alert.alert('错误', error.message || '点赞失败');
    }
  }, [toggleLike, userInfo.id]);

  // 收藏处理
  const handleBookmark = useCallback(async (threadId: string) => {
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录');
      return;
    }
    try {
      toggleBookmark({ threadId, userId: userInfo.id });
    } catch (error: any) {
      Alert.alert('错误', error.message || '收藏失败');
    }
  }, [toggleBookmark, userInfo.id]);

  // 发帖处理
  const handleCreateThread = useCallback(async (data: any) => {
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录');
      return;
    }
    try {
      createThread({ data, userId: userInfo.id });
    } catch (error: any) {
      Alert.alert('错误', error.message || '发帖失败');
    }
  }, [createThread, userInfo.id]);

  // 评论处理
  const handleComment = useCallback((thread: any) => {
    router.push(`/(stack)/thread-detail?threadId=${thread.thread_id}`);
  }, [router]);

  // 帖子详情处理
  const handleThreadPress = useCallback((thread: any) => {
    router.push(`/(stack)/thread-detail?threadId=${thread.thread_id}`);
  }, [router]);

  // 排序方式处理
  const handleSortChange = useCallback((newSortBy: ThreadSortBy) => {
    // 如果选择关注但用户未登录，显示提示
    if (newSortBy === 'following' && !userInfo.id) {
      Alert.alert('提示', '请先登录后查看关注动态');
      return;
    }
    setSortBy(newSortBy);
  }, [setSortBy, userInfo.id]);

  const handleUserPress = useCallback((user: any) => {
    router.push(`/(stack)/user-profile?userId=${user.id}`);
  }, [router]);

  // 筛选头组件
  const renderFilterHeader = () => {
    const sortOptions: { key: ThreadSortBy; label: string }[] = [
      { key: 'latest', label: '最新' },
      { key: 'updated', label: '推荐' },
      { key: 'hot', label: '热门' },
      { key: 'following', label: '关注' },
    ];

    return (
      <View style={styles.filterHeader}>
        {sortOptions.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.sortButton,
              sortBy === option.key && styles.sortButtonActive
            ]}
            onPress={() => handleSortChange(option.key)}
          >
            <Text style={[
              styles.sortButtonText,
              sortBy === option.key && styles.sortButtonTextActive
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderThread = useCallback(({ item }: { item: any }) => (
    <ThreadCard
      thread={item}
      onPress={() => handleThreadPress(item)}
      onUserPress={() => handleUserPress(item.user)}
      onLike={() => handleLike(item.thread_id)}
      onBookmark={() => handleBookmark(item.thread_id)}
      onComment={() => handleComment(item)}
      isLiking={isLiking}
      isBookmarking={isBookmarking}
    />
  ), [handleThreadPress, handleUserPress, handleLike, handleBookmark, handleComment, isLiking, isBookmarking]);

  const renderFooter = () => {
    if (!isLoadingMore) return null;
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color="#007AFF" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  };

  const renderEmpty = () => {
    if (sortBy === 'following') {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="people-outline" size={64} color="#CCC" />
          <Text style={styles.emptyText}>暂无关注动态</Text>
          <Text style={styles.emptySubtext}>关注一些用户来查看他们的最新动态吧！</Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="chatbubbles-outline" size={64} color="#CCC" />
        <Text style={styles.emptyText}>还没有帖子</Text>
        <Text style={styles.emptySubtext}>成为第一个发帖的人吧！</Text>
      </View>
    );
  };

  if (isLoading && threads.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error && threads.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="warning-outline" size={64} color="#FF6B6B" />
          <Text style={styles.errorText}>加载失败</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryText}>重试</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderFilterHeader()}
      <FlatList
        data={threads}
        renderItem={renderThread}
        keyExtractor={(item) => item.thread_id}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
            tintColor="#007AFF"
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={threads.length === 0 ? styles.emptyContentContainer : undefined}
      />

      <CreateThreadModal
        visible={createModalVisible}
        onClose={() => setCreateModalVisible(false)}
        onSubmit={handleCreateThread}
        isLoading={isCreating}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    backgroundColor: 'white',
  },
  sortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  sortButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 0,
  },
  sortButtonActive: {
    borderBottomWidth: 2,
    borderBottomColor: '#006400',
  },
  sortButtonText: {
    fontSize: 15,
    color: '#006400',
    fontWeight: '500',
  },
  sortButtonTextActive: {
    fontWeight: 'bold',
    color: '#006400',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: '#6C757D',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#495057',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '500',
    color: '#495057',
    textAlign: 'center',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#6C757D',
    textAlign: 'center',
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
});


import BrandCard from "@/components/core/explore/show/BrandCard";
import { useDailyRandomBrands } from "@/store/brandReviewStore";
import React from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  View
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

export default function RandomBrandContent() {
  const { data: brands, isLoading, error } = useDailyRandomBrands(9);

  if (isLoading) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (error || !brands || brands.length === 0) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <Icon
          name="alert-circle-outline"
          size={60}
          color="#e74c3c"
          style={styles.errorIcon}
        />
        <Text style={styles.errorText}>无法加载品牌数据</Text>
        <Text style={styles.errorSubtext}>请检查网络连接或稍后再试</Text>
      </View>
    );
  }

  return (
    <View style={styles.contentContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>每日推荐</Text>
      </View>

      <FlatList
        data={brands}
        renderItem={({ item }) => (
          <BrandCard brand={item} />
        )}
        keyExtractor={(item) => item.brand_id}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暂无品牌数据</Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: "bold",
  },
  viewAllLink: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    color: "#666",
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#3498db",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
  },
});

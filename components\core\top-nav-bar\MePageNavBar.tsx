import { DrawerActions, useNavigation } from '@react-navigation/native';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface MePageNavBarProps {
  onDrawerPress?: () => void;
  onQRScanPress?: () => void;
  onSharePress?: () => void;
}

export default function MePageNavBar({ 
  onDrawerPress, 
  onQRScanPress, 
  onSharePress 
}: MePageNavBarProps) {
  const navigation = useNavigation();

  const handleDrawerPress = () => {
    try {
      navigation.dispatch(DrawerActions.openDrawer());
      onDrawerPress?.();
    } catch (error) {
      console.log('Error opening drawer:', error);
    }
  };


  return (
    <View style={styles.container}>
      {/* Left - Drawer */}
      <TouchableOpacity style={styles.iconButton} onPress={handleDrawerPress}>
        <Icon name="menu" size={24} color="#fff" />
      </TouchableOpacity>

    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 15,
    width: '100%',
    height: 120,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
  },
  iconButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  rightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});

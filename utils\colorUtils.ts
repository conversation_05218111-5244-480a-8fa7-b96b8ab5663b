import { Image } from 'react-native';

// Extract dominant color from image using canvas-like approach
export const extractDominantColor = async (imageUri: string): Promise<string> => {
  try {
    if (!imageUri) {
      return '#f8f9fa';
    }

    // For React Native, we'll simulate color extraction
    // In a real implementation, you might use react-native-image-colors
    return new Promise((resolve) => {
      Image.getSize(
        imageUri,
        (width, height) => {
          // Simulate color extraction by analyzing the image URL and creating similar colors
          const colorFromUrl = generateColorFromImageUrl(imageUri);
          resolve(colorFromUrl);
        },
        (error) => {
          console.log('Image size fetch failed:', error);
          resolve(generateColorFromText(imageUri));
        }
      );
    });
  } catch (error) {
    console.log('Color extraction failed, using fallback');
    return generateColorFromText(imageUri);
  }
};

// Generate color based on image URL patterns and brand characteristics
export const generateColorFromImageUrl = (imageUrl: string): string => {
  // Extract brand/product info from URL to determine color family
  const url = imageUrl.toLowerCase();
  
  // Brand-specific color mappings with classical vibrant colors
  if (url.includes('hills') || url.includes('hill')) {
    return '#8B0000'; // Dark red
  }
  if (url.includes('royal') || url.includes('canin')) {
    return '#4B0082'; // Indigo
  }
  if (url.includes('purina') || url.includes('pro-plan')) {
    return '#DC143C'; // Crimson
  }
  if (url.includes('whiskas')) {
    return '#800080'; // Purple
  }
  if (url.includes('iams')) {
    return '#B8860B'; // Dark goldenrod
  }
  if (url.includes('wellness') || url.includes('core')) {
    return '#006400'; // Dark green
  }
  if (url.includes('blue') || url.includes('buffalo')) {
    return '#191970'; // Midnight blue
  }
  if (url.includes('orijen') || url.includes('acana')) {
    return '#228B22'; // Forest green
  }
  if (url.includes('taste') || url.includes('wild')) {
    return '#B8860B'; // Dark goldenrod
  }
  if (url.includes('merrick')) {
    return '#8B008B'; // Dark magenta
  }
  
  // Fallback to hash-based classical color for unknown brands
  return generateColorFromText(imageUrl);
};

export const generateColorFromText = (text: string): string => {
  // Generate a consistent color based on text hash
  let hash = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  // Convert to positive number
  const positiveHash = Math.abs(hash);
  
  // Classical vibrant color palette with deep, rich colors
  const vibrantColorPalette = [
    '#8B0000', // Dark red
    '#006400', // Dark green
    '#191970', // Midnight blue
    '#800080', // Purple
    '#B8860B', // Dark goldenrod
    '#8B008B', // Dark magenta
    '#DC143C', // Crimson
    '#4B0082', // Indigo
    '#2F4F4F', // Dark slate gray
    '#8B4513', // Saddle brown
    '#483D8B', // Dark slate blue
    '#2E8B57', // Sea green
    '#A0522D', // Sienna
    '#556B2F', // Dark olive green
    '#8B1538', // Deep pink (dark)
    '#36454F', // Charcoal
    '#722F37', // Wine red
    '#4A5D23', // Dark moss green
    '#1F4E79', // Steel blue
    '#5D2E5D', // Dark orchid
  ];
  
  // Select color based on hash
  const colorIndex = positiveHash % vibrantColorPalette.length;
  return vibrantColorPalette[colorIndex];
};

export const hslToHex = (h: number, s: number, l: number): string => {
  l /= 100;
  const a = s * Math.min(l, 1 - l) / 100;
  const f = (n: number) => {
    const k = (n + h / 30) % 12;
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
    return Math.round(255 * color).toString(16).padStart(2, '0');
  };
  return `#${f(0)}${f(8)}${f(4)}`;
};

export const getContrastColor = (backgroundColor: string): string => {
  // Simple contrast calculation - return dark or light text color
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate relative luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // For vibrant backgrounds, use darker text for better readability
  return luminance > 0.6 ? '#2C3E50' : '#34495E';
};

export const lightenColor = (color: string, amount: number): string => {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = Math.min(255, (num >> 16) + amount);
  const g = Math.min(255, (num >> 8 & 0x00FF) + amount);
  const b = Math.min(255, (num & 0x0000FF) + amount);
  return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
};

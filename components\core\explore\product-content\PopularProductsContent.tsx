import { PopularProduct } from "@/api/services/productReviewService";
import ProductCard from "@/components/core/explore/show/ProductCard";
import { usePopularProducts } from "@/store/productStore";
import { router } from "expo-router";
import React, { useMemo } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

export default function PopularProductsContent() {
  const { data: popularProducts, isLoading, error } = usePopularProducts();

  const products = useMemo(() => {
    if (!Array.isArray(popularProducts) || popularProducts.length === 0) {
      return [];
    }

    return popularProducts.map((product: PopularProduct) => {
      return {
        _id: product.product_id,
        brand: product.brand || "Unknown Brand",
        image_url: product.image_url || "",
        name: product.name || "Unknown Product",
        product_type: product.product_type || "猫粮",
        est_calories: product.est_calories || undefined,
        average_rating:
          typeof product.average_rating === "string" && product.average_rating === "暂无评分"
            ? 0
            : typeof product.average_rating === "number"
            ? product.average_rating
            : 0,
        review_count: product.review_count || 0,
      };
    });
  }, [popularProducts]);

  const displayedProducts = useMemo(() => {
    return products.slice(0, 6);
  }, [products]);

  const handleViewAll = () => {
    router.push("/all-popular-products");
  };

  if (isLoading) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (error || products.length === 0) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <Icon
          name="alert-circle-outline"
          size={60}
          color="#e74c3c"
          style={styles.errorIcon}
        />
        <Text style={styles.errorText}>无法加载产品数据 {error?.message}</Text>
        <Text style={styles.errorSubtext}>请检查网络连接或稍后再试</Text>
      </View>
    );
  }

  return (
    <View style={styles.contentContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>热门产品</Text>
        <TouchableOpacity style={styles.viewAllLink} onPress={handleViewAll}>
          <Text style={styles.viewAllText}>查看全部</Text>
          <Icon name="chevron-right" size={14} color="#666" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={displayedProducts}
        renderItem={({ item, index }) => (
          <ProductCard
            product={item}
            onViewAll={index === 0 ? handleViewAll : undefined}
            totalCount={products.length}
          />
        )}
        keyExtractor={(item) => item._id}
        numColumns={3}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暂无产品数据</Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: "bold",
  },
  viewAllLink: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    color: "#666",
  },
  row: {
    justifyContent: "space-between",
    marginHorizontal: 2,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#3498db",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
  },
});

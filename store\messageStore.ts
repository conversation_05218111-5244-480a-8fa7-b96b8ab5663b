import { ConversationData, MessageData } from '@/api/services/messageService';
import { create } from 'zustand';

type MessageStore = {
  conversations: ConversationData[];
  messages: { [conversationId: string]: MessageData[] };
  unreadCount: number;
  currentConversationId: string | null;
  actions: {
    setConversations: (conversations: ConversationData[]) => void;
    addConversation: (conversation: ConversationData) => void;
    updateConversation: (conversationId: string, updates: Partial<ConversationData>) => void;
    setMessages: (conversationId: string, messages: MessageData[]) => void;
    addMessage: (conversationId: string, message: MessageData) => void;
    addMessages: (conversationId: string, messages: MessageData[]) => void;
    updateMessage: (conversationId: string, messageId: string, updates: Partial<MessageData>) => void;
    removeMessage: (conversationId: string, messageId: string) => void;
    setUnreadCount: (count: number) => void;
    setCurrentConversationId: (conversationId: string | null) => void;
    markConversationAsRead: (conversationId: string) => void;
    clearConversations: () => void;
    clearMessages: (conversationId?: string) => void;
    clearAll: () => void;
  };
};

const useMessageStore = create<MessageStore>()((set, get) => ({
  conversations: [],
  messages: {},
  unreadCount: 0,
  currentConversationId: null,
  actions: {
    setConversations: (conversations) => {
      set({ conversations });
    },
    
    addConversation: (conversation) => {
      set((state) => ({
        conversations: [conversation, ...state.conversations.filter(c => c.conversation_id !== conversation.conversation_id)]
      }));
    },
    
    updateConversation: (conversationId, updates) => {
      set((state) => ({
        conversations: state.conversations.map(conv => 
          conv.conversation_id === conversationId 
            ? { ...conv, ...updates }
            : conv
        )
      }));
    },
    
    setMessages: (conversationId, messages) => {
      set((state) => ({
        messages: {
          ...state.messages,
          [conversationId]: messages
        }
      }));
    },
    
    addMessage: (conversationId, message) => {
      set((state) => {
        const existingMessages = state.messages[conversationId] || [];
        const messageExists = existingMessages.some(m => m.message_id === message.message_id);
        
        if (messageExists) {
          return state;
        }
        
        return {
          messages: {
            ...state.messages,
            [conversationId]: [...existingMessages, message]
          }
        };
      });
    },
    
    addMessages: (conversationId, messages) => {
      set((state) => {
        const existingMessages = state.messages[conversationId] || [];
        const existingIds = new Set(existingMessages.map(m => m.message_id));
        const newMessages = messages.filter(m => !existingIds.has(m.message_id));
        
        return {
          messages: {
            ...state.messages,
            [conversationId]: [...existingMessages, ...newMessages]
          }
        };
      });
    },
    
    updateMessage: (conversationId, messageId, updates) => {
      set((state) => ({
        messages: {
          ...state.messages,
          [conversationId]: (state.messages[conversationId] || []).map(msg =>
            msg.message_id === messageId ? { ...msg, ...updates } : msg
          )
        }
      }));
    },
    
    removeMessage: (conversationId, messageId) => {
      set((state) => ({
        messages: {
          ...state.messages,
          [conversationId]: (state.messages[conversationId] || []).filter(msg => msg.message_id !== messageId)
        }
      }));
    },
    
    setUnreadCount: (count) => {
      set({ unreadCount: count });
    },
    
    setCurrentConversationId: (conversationId) => {
      set({ currentConversationId: conversationId });
    },
    
    markConversationAsRead: (conversationId) => {
      set((state) => ({
        conversations: state.conversations.map(conv => 
          conv.conversation_id === conversationId 
            ? { ...conv, unread_count: 0 }
            : conv
        )
      }));
    },
    
    clearConversations: () => {
      set({ conversations: [] });
    },
    
    clearMessages: (conversationId) => {
      if (conversationId) {
        set((state) => {
          const newMessages = { ...state.messages };
          delete newMessages[conversationId];
          return { messages: newMessages };
        });
      } else {
        set({ messages: {} });
      }
    },
    
    clearAll: () => {
      set({
        conversations: [],
        messages: {},
        unreadCount: 0,
        currentConversationId: null
      });
    },
  },
}));

export default useMessageStore;

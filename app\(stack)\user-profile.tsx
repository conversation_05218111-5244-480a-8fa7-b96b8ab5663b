import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import UserSocialCard from '@/components/core/user/UserSocialCard';
import { useCreateConversation } from '@/store/useMessages';
import { useUserDetail, useUserInfo } from '@/store/userStore';
import { useFollowStatus, useToggleFollow } from '@/store/userStatsStore';

export default function UserProfileScreen() {
    const router = useRouter();
    const { userId } = useLocalSearchParams<{ userId: string }>();
    const userInfo = useUserInfo();
    const createConversationMutation = useCreateConversation();

    // 如果没有userId，返回错误页面
    // 所有 hooks 必须在条件判断之前调用
    const { user: targetUser } = useUserDetail(userId || '');
    const { data: followStatus } = useFollowStatus(userId || '', userInfo?.id || '');
    const toggleFollowMutation = useToggleFollow(userInfo?.id || '');

    if (!userId) {
        return (
            <View style={styles.container}>
                <Text>用户ID不存在</Text>
            </View>
        );
    }

    const handleEditProfile = () => {
        router.push('/(stack)/edit-profile');
    };

    const handleFollow = async () => {
        if (!userInfo.id) {
            Alert.alert('提示', '请先登录');
            return;
        }

        // 直接调用mutation，不需要额外处理
        // UserProfileHeader内部会处理所有的状态更新
        try {
            await toggleFollowMutation.mutateAsync(userId || '');
        } catch (error) {
            console.error('Follow error in UserProfile:', error);
            // 错误会在组件内部处理，这里不需要显示Alert
        }
    };

    const handleMessage = async () => {
        if (!userId || !userInfo?.id) {
            Alert.alert('错误', '用户信息不完整');
            return;
        }

        if (userId === userInfo.id) {
            Alert.alert('提示', '不能给自己发私信');
            return;
        }

        try {
            const result = await createConversationMutation.mutateAsync({
                userId1: userInfo.id,
                userId2: userId
            });
            // APIClient返回的是data部分，直接使用
            if (result && result.conversation_id) {
                // 导航到聊天页面
                router.push({
                    pathname: '/(stack)/chat',
                    params: {
                        conversationId: result.conversation_id,
                        otherUserId: userId,
                        otherUserName: targetUser?.username || 'User',
                        otherUserAvatar: targetUser?.avatar || ''
                    }
                });
            } else {
                console.error('Invalid response structure:', result);
                Alert.alert('错误', '创建会话失败，请重试');
            }
        } catch (error) {
            console.error('Create conversation error:', error);
            
            // 提供更具体的错误信息
            if (error instanceof Error) {
                if (error.message.includes('请求超时')) {
                    Alert.alert('超时错误', '请求超时，请检查网络连接');
                } else if (error.message.includes('网络连接失败')) {
                    Alert.alert('网络错误', '网络连接失败，请检查网络设置');
                } else {
                    Alert.alert('错误', `创建会话失败: ${error.message}`);
                }
            } else {
                Alert.alert('错误', '创建会话失败，请重试');
            }
        }
    };

    const handleBack = () => {
        router.back();
    };

    return (
        <View style={styles.container}>
            <View style={styles.headerOverlay}>
                <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                    <Icon name="arrow-left" size={24} color="#fff" />
                </TouchableOpacity>
            </View>

            <UserSocialCard
                userId={userId!}
                onEditProfile={handleEditProfile}
                onFollow={handleFollow}
                onMessage={handleMessage}
            />


        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    profileContainer: {
        position: 'relative',
    },
    headerOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        paddingTop: 50,
        zIndex: 10,
        backgroundColor: 'rgba(0, 0, 0, 0)',
    },
    backButton: {
        padding: 8,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#fff',
    },
    headerTitleDark: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    placeholder: {
        width: 40,
    },
    loadingContainer: {
        flex: 1,
        backgroundColor: '#fff',
    },
    loadingContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 100,
    },
    loadingText: {
        marginTop: 12,
        fontSize: 14,
        color: '#6C757D',
    },
    errorContainer: {
        flex: 1,
        backgroundColor: '#fff',
    },
    errorContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
        paddingTop: 100,
    },
    errorText: {
        marginTop: 16,
        fontSize: 16,
        color: '#495057',
        textAlign: 'center',
    },
    retryButton: {
        marginTop: 16,
        paddingHorizontal: 24,
        paddingVertical: 12,
        backgroundColor: '#007AFF',
        borderRadius: 8,
    },
    retryText: {
        color: '#FFFFFF',
        fontSize: 16,
        fontWeight: '500',
    },
    bottomSpacing: {
        height: 100,
    },
});


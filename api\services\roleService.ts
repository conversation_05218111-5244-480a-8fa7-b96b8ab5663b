
import type { Role } from "@/types/entity";
import apiClient from "../apiClient";

export enum RoleApi {
	Role = "roles",
}

const getRoles = async () => {
	try {
		const response = await apiClient.get<Role[]>({
			url: `${RoleApi.Role}/`,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

const getRolesWithPermissions = async () => {
	try {
		const response = await apiClient.get<Role[]>({
			url: `${RoleApi.Role}/with-permissions`,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

const getRoleById = async (id: string) => {
	try {
		const response = await apiClient.get<Role>({
			url: `${RoleApi.Role}/${id}`,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

const getRoleWithPermissions = async (id: string) => {
	try {
		const response = await apiClient.get<Role>({
			url: `${RoleApi.Role}/${id}/with-permissions`,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

const createRole = async (role: Role) => {
	try {
		const response = await apiClient.post<Role>({
			url: `${RoleApi.Role}/`,
			data: role,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

const updateRole = async (id: string, role: Role) => {
	try {
		const response = await apiClient.put<Role>({
			url: `${RoleApi.Role}/${id}`,
			data: role,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

const deleteRole = async (id: string) => {
	try {
		const response = await apiClient.delete<Role>({
			url: `${RoleApi.Role}/${id}`,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

const initializeRoles = async () => {
	try {
		const response = await apiClient.get<Role[]>({
			url: `${RoleApi.Role}/initialize`,
		});
		return response;
	} catch (e) {
		console.log(e);
		throw e;
	}
};

export {
	createRole, deleteRole, getRoleById, getRoles,
	getRolesWithPermissions, getRoleWithPermissions, initializeRoles, updateRole
};


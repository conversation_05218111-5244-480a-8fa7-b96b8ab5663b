import { ImageType } from '@/constants/profile';
import { getAvatarUrl, getBackgroundImageUrl } from '@/utils/userUtils';
import React from 'react';
import { ActivityIndicator, Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface ProfileImagePickerProps {
  type: ImageType;
  imageUri?: string;
  isUploading?: boolean;
  onPress: () => void;
  style?: any;
}

export const ProfileImagePicker: React.FC<ProfileImagePickerProps> = ({
  type,
  imageUri,
  isUploading = false,
  onPress,
  style,
}) => {
  const isAvatar = type === 'avatar';

  // 使用工具函数构建正确的图像URL
  const getImageUrl = (uri?: string) => {
    if (!uri) return '';
    return isAvatar ? getAvatarUrl(uri) : getBackgroundImageUrl(uri);
  };

  return (
    <TouchableOpacity
      style={[
        isAvatar ? styles.avatarContainer : styles.backgroundContainer,
        style,
      ]}
      onPress={onPress}
      disabled={isUploading}
    >
      {imageUri ? (
        <Image
          source={{ uri: getImageUrl(imageUri) }}
          style={isAvatar ? styles.avatarImage : styles.backgroundImage}
          resizeMode="cover"
        />
      ) : (
        <View style={isAvatar ? styles.avatarPlaceholder : styles.backgroundPlaceholder}>
          <Icon
            name={isAvatar ? 'account-circle' : 'image'}
            size={isAvatar ? 60 : 40}
            color="#ccc"
          />
        </View>
      )}
      
      {isUploading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#007AFF" />
        </View>
      )}
      
      <View style={isAvatar ? styles.avatarEditIcon : styles.backgroundEditIcon}>
        <Icon name="camera" size={20} color="#fff" />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignSelf: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  backgroundContainer: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 20,
    position: 'relative',
  },
  avatarImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  backgroundImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  backgroundPlaceholder: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 60, // 对头像使用圆形
  },
  avatarEditIcon: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: '#007AFF',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  backgroundEditIcon: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: '#007AFF',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
});

export interface HistoryItem {
  id: string;
  user_id: string;
  item_type: 'thread' | 'product' | 'brand';
  item_id: string;
  item_title: string;
  item_description?: string;
  item_image?: string;
  visited_at: string;
  created_at: string;
  updated_at: string;
}

export interface HistoryResponse {
  items: HistoryItem[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export interface CreateHistoryRequest {
  item_type: 'thread' | 'product' | 'brand';
  item_id: string;
  item_title: string;
  item_description?: string;
  item_image?: string;
}

export interface HistoryStats {
  totalItems: number;
  threadsCount: number;
  productsCount: number;
  brandsCount: number;
  todayCount: number;
  weekCount: number;
}

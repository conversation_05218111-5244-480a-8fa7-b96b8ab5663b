import React from 'react';
import { Dimensions, StyleSheet, Text, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface AgreementCheckboxProps {
  checked: boolean;
  onToggle: () => void;
}

export default function AgreementCheckbox({ checked, onToggle }: AgreementCheckboxProps) {
  return (
    <TouchableOpacity style={styles.container} onPress={onToggle}>
      <Icon 
        name={checked ? 'checkbox-marked' : 'checkbox-blank-outline'} 
        size={20} 
        color={checked ? '#3498db' : '#999'} 
      />
      <Text style={styles.text}>
        我已阅读并同意
        <Text style={styles.link}>《用户协议》</Text>
        和
        <Text style={styles.link}>《隐私政策》</Text>
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: Math.max(20, screenHeight * 0.025),
    paddingHorizontal: Math.max(4, screenWidth * 0.01),
    paddingVertical: 8,
  },
  text: {
    flex: 1,
    fontSize: Math.max(12, screenWidth * 0.032),
    color: '#666',
    marginLeft: 8,
    lineHeight: Math.max(18, screenWidth * 0.048),
  },
  link: {
    color: '#3498db',
    textDecorationLine: 'underline',
  },
});

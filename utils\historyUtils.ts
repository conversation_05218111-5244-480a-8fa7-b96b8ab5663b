import historyService, { CreateHistoryRequest } from '@/api/services/historyService';

/**
 * 自动记录浏览历史
 * @param userId 用户ID
 * @param data 历史记录数据
 */
export const recordHistory = async (userId: string, data: CreateHistoryRequest) => {
  try {
    await historyService.addHistoryItem(userId, data);
  } catch (error) {
    console.error('Record history error:', error);
    // 静默失败，不影响用户体验
  }
};

/**
 * 记录帖子浏览历史
 * @param userId 用户ID
 * @param threadId 帖子ID
 * @param title 帖子标题
 * @param description 帖子描述
 * @param image 帖子图片
 */
export const recordThreadHistory = async (
  userId: string,
  threadId: string,
  title: string,
  description?: string,
  image?: string
) => {
  await recordHistory(userId, {
    item_type: 'thread',
    item_id: threadId,
    item_title: title,
    item_description: description,
    item_image: image,
  });
};

/**
 * 记录产品浏览历史
 * @param userId 用户ID
 * @param productId 产品ID
 * @param title 产品名称
 * @param description 产品描述
 * @param image 产品图片
 */
export const recordProductHistory = async (
  userId: string,
  productId: string,
  title: string,
  description?: string,
  image?: string
) => {
  await recordHistory(userId, {
    item_type: 'product',
    item_id: productId,
    item_title: title,
    item_description: description,
    item_image: image,
  });
};

/**
 * 记录品牌浏览历史
 * @param userId 用户ID
 * @param brandId 品牌ID
 * @param title 品牌名称
 * @param description 品牌描述
 * @param image 品牌图片
 */
export const recordBrandHistory = async (
  userId: string,
  brandId: string,
  title: string,
  description?: string,
  image?: string
) => {
  await recordHistory(userId, {
    item_type: 'brand',
    item_id: brandId,
    item_title: title,
    item_description: description,
    item_image: image,
  });
};

/**
 * 防抖记录历史，避免短时间内重复记录
 */
const historyDebounceMap = new Map<string, number>();
const DEBOUNCE_TIME = 5000; // 5秒内不重复记录

export const recordHistoryDebounced = async (userId: string, data: CreateHistoryRequest) => {
  const key = `${data.item_type}-${data.item_id}`;
  const now = Date.now();
  const lastRecorded = historyDebounceMap.get(key);

  if (lastRecorded && now - lastRecorded < DEBOUNCE_TIME) {
    return; // 跳过重复记录
  }

  historyDebounceMap.set(key, now);
  await recordHistory(userId, data);
};

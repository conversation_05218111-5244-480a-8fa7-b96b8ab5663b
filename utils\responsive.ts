import { Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 设备类型判断
export const DeviceType = {
  isSmallPhone: screenWidth < 350,
  isPhone: screenWidth < 768,
  isTablet: screenWidth >= 768 && screenWidth < 1024,
  isDesktop: screenWidth >= 1024,
};

// 响应式字体大小计算
export const getResponsiveFontSize = (baseSize: number, minScale = 0.7, maxScale = 1.3) => {
  const scale = screenWidth / 375; // 以iPhone X为基准
  const newSize = baseSize * scale;
  // 限制最小和最大字体大小
  return Math.max(Math.min(newSize, baseSize * maxScale), baseSize * minScale);
};

// 响应式间距计算
export const getResponsiveSpacing = (baseSpacing: number) => {
  return Math.max(baseSpacing * 0.8, Math.min(baseSpacing * 1.2, screenWidth * (baseSpacing / 375)));
};

// 响应式宽度百分比
export const getResponsiveWidth = (percentage: number) => {
  return screenWidth * (percentage / 100);
};

// 响应式高度百分比
export const getResponsiveHeight = (percentage: number) => {
  return screenHeight * (percentage / 100);
};

// 安全的最小触摸目标大小
export const getMinTouchTarget = () => {
  return Math.max(44, screenHeight * 0.055); // iOS HIG 推荐最小44pt
};

// 响应式内边距
export const getResponsivePadding = (basePadding: number) => {
  if (DeviceType.isSmallPhone) {
    return basePadding * 0.8;
  } else if (DeviceType.isTablet) {
    return basePadding * 1.2;
  }
  return basePadding;
};

// 屏幕尺寸常量
export const ScreenDimensions = {
  width: screenWidth,
  height: screenHeight,
  isLandscape: screenWidth > screenHeight,
  isPortrait: screenHeight > screenWidth,
};

// 响应式样式工具
export const ResponsiveStyles = {
  // 容器样式
  container: {
    paddingHorizontal: getResponsivePadding(24),
    paddingVertical: getResponsivePadding(20),
  },
  
  // 按钮样式
  button: {
    paddingVertical: getResponsiveSpacing(16),
    paddingHorizontal: getResponsiveSpacing(24),
    borderRadius: 12,
    minHeight: getMinTouchTarget(),
  },
  
  // 输入框样式
  input: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(14),
    borderRadius: 12,
    minHeight: getMinTouchTarget(),
  },
  
  // 文本样式
  text: {
    title: {
      fontSize: getResponsiveFontSize(24),
      lineHeight: getResponsiveFontSize(24) * 1.2,
    },
    subtitle: {
      fontSize: getResponsiveFontSize(16),
      lineHeight: getResponsiveFontSize(16) * 1.4,
    },
    body: {
      fontSize: getResponsiveFontSize(14),
      lineHeight: getResponsiveFontSize(14) * 1.5,
    },
    caption: {
      fontSize: getResponsiveFontSize(12),
      lineHeight: getResponsiveFontSize(12) * 1.4,
    },
  },
};

import { PopularProduct } from "@/api/services/productReviewService";
import { useRatingRanking } from "@/store/productStore";
import { getProductImageUrlSync } from "@/utils/imageUtils";
import { Image } from 'expo-image';
import { router } from "expo-router";
import React, { useMemo } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

interface RankingItemProps {
  product: PopularProduct;
  rank: number;
  onPress: () => void;
}

function RankingItem({ product, rank, onPress }: RankingItemProps) {

  // 获取正确的图片URL
  const getImageUrl = () => {

    // 如果有product_id，使用getProductImageUrlSync
    if (product.product_id) {
      return getProductImageUrlSync(product.product_id);
    }

    // 默认占位符
    return `https://via.placeholder.com/60x60/f0f0f0/666?text=${encodeURIComponent(product.name.substring(0, 2))}`;
  };

  const getRankIcon = () => {
    switch (rank) {
      case 1:
        return "trophy";
      case 2:
        return "medal";
      case 3:
        return "medal-outline";
      default:
        return "numeric-" + rank + "-circle";
    }
  };

  const getRankColor = () => {
    switch (rank) {
      case 1:
        return "#FFD700"; // 金色
      case 2:
        return "#C0C0C0"; // 银色
      case 3:
        return "#CD7F32"; // 铜色
      default:
        return "#666";
    }
  };

  const getQualityScore = () => {
    // 使用实际的用户评分数据
    if (typeof product.average_rating === 'number' && product.average_rating > 0) {
      return product.average_rating.toFixed(1);
    } else if (product.average_rating === "暂无评分" || product.average_rating === null || product.average_rating === undefined) {
      return "暂无";
    } else if (typeof product.average_rating === 'string') {
      // 尝试解析字符串形式的数字
      const parsed = parseFloat(product.average_rating);
      if (!isNaN(parsed) && parsed > 0) {
        return parsed.toFixed(1);
      }
      return "暂无";
    }
    return "0.0";
  };

  return (
    <TouchableOpacity style={styles.rankingItem} onPress={onPress}>
      <View style={styles.rankContainer}>
        <Icon
          name={getRankIcon()}
          size={24}
          color={getRankColor()}
          style={styles.rankIcon}
        />
        <Text style={[styles.rankNumber, { color: getRankColor() }]}>
          {rank}
        </Text>
      </View>

      <View style={styles.productImageContainer}>
        <Image
          source={{ uri: getImageUrl() }}
          style={styles.productImage}
          contentFit="contain"
          transition={200}
        />
      </View>

      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={2}>
          {product.name}
        </Text>
        <Text style={styles.brandName} numberOfLines={1}>
          {product.brand}
        </Text>
        <View style={styles.scoreContainer}>
          <Icon name="star" size={14} color="#FFD700" />
          <Text style={styles.scoreText}>
            {getQualityScore()}{getQualityScore() !== '暂无' ? '分' : ''}
          </Text>
          <Text style={styles.reviewCount}>
            ({product.review_count || 0}条评价)
          </Text>
        </View>
      </View>

      <View style={styles.arrowContainer}>
        <Icon name="chevron-right" size={20} color="#ccc" />
      </View>
    </TouchableOpacity>
  );
}

export default function RatingRankingContent() {
  const { data: rankingProducts, isLoading, error } = useRatingRanking();

  const displayedProducts = useMemo(() => {
    if (!Array.isArray(rankingProducts)) {
      return [];
    }
    return rankingProducts.slice(0, 10);
  }, [rankingProducts]);

  const handleProductPress = (product: PopularProduct) => {
    router.push(`/(stack)/product-detail?id=${product.product_id}`);
  };

  if (isLoading) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (error || !Array.isArray(rankingProducts) || rankingProducts.length === 0) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <Icon
          name="alert-circle-outline"
          size={60}
          color="#e74c3c"
          style={styles.errorIcon}
        />
        <Text style={styles.errorText}>暂无排行榜数据</Text>
        <Text style={styles.errorSubtext}>正在努力收集产品评分数据中...</Text>
      </View>
    );
  }

  return (
    <View style={styles.contentContainer}>
      <View style={styles.sectionHeader}>
        <View style={styles.titleContainer}>
          <Icon name="trophy-outline" size={20} color="#FF6B35" />
          <Text style={styles.sectionTitle}>好评榜</Text>
        </View>
      </View>

      <View style={styles.rankingList}>
        <FlatList
          data={displayedProducts}
          renderItem={({ item, index }) => (
            <RankingItem
              product={item}
              rank={index + 1}
              onPress={() => handleProductPress(item)}
            />
          )}
          keyExtractor={(item) => item.product_id}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>暂无排行榜数据</Text>
            </View>
          }
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    backgroundColor: "#fff",
    padding: 16,

  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
    minHeight: 200,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 8,
  },
  viewAllLink: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    marginRight: 4,
  },
  rankingList: {
    padding: 0,
  },
  rankingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical:10,
  },
  rankContainer: {
    width: 25,
    alignItems: "center",
    marginRight: 12,
  },
  rankIcon: {
    marginBottom: 2,
  },
  rankNumber: {
    fontSize: 12,
    fontWeight: "600",
  },
  productImageContainer: {
    marginRight: 12,
  },
  productImage: {
    width: 80,
    height: 80,
  },
  productInfo: {
    flex: 1,
    marginRight: 8,
  },
  productName: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
    lineHeight: 18,
    marginBottom: 2,
  },
  brandName: {
    fontSize: 12,
    color: "#666",
    marginBottom: 4,
  },
  scoreContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  scoreText: {
    fontSize: 12,
    color: "#FF6B35",
    fontWeight: "600",
    marginLeft: 4,
  },
  reviewCount: {
    fontSize: 10,
    color: "#999",
    marginLeft: 4,
  },
  arrowContainer: {
    justifyContent: "center",
  },
  separator: {
    height: 0.5,
    backgroundColor: "#e0e0e0",
    marginVertical: 2,
    marginHorizontal: 8,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
  },
  errorIcon: {
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: "#e74c3c",
    fontWeight: "500",
    marginBottom: 4,
  },
  errorSubtext: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
  },
  emptyContainer: {
    alignItems: "center",
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 14,
    color: "#999",
  },
});

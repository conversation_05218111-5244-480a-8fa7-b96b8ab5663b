import React, { useCallback, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import CommentItem from '@/components/comments/CommentItem';
import UserReviewItem from '@/components/user-reviews/UserReviewItem';
import { useUserCommentsStore } from '@/store/userCommentsStore';
import { useUserReviews, useUserReviewActions } from '@/store/userReviewStore';
import { useUserInfo } from '@/store/userStore';
import { Thread } from '@/api/services/threadsService';

type TabType = 'comments' | 'reviews';

export default function MyCommentsScreen() {
  const router = useRouter();
  const userInfo = useUserInfo();
  const [activeTab, setActiveTab] = useState<TabType>('comments');

  // 评论相关状态
  const {
    comments,
    loading: commentsLoading,
    error: commentsError,
    hasMore: commentsHasMore,
    refreshing: commentsRefreshing,
    totalCount: commentsTotalCount,
    fetchUserComments,
    refreshUserComments,
    loadMoreUserComments,
    reset: resetComments,
  } = useUserCommentsStore();

  // 点评相关状态
  const {
    allReviews,
    loading: reviewsLoading,
    error: reviewsError,
    hasMore: reviewsHasMore,
    refreshing: reviewsRefreshing,
    totalCount: reviewsTotalCount,
    currentFilter,
  } = useUserReviews();

  const {
    fetchUserReviews,
    refreshUserReviews,
    loadMoreUserReviews,
    reset: resetReviews,
  } = useUserReviewActions();

  useEffect(() => {
    if (userInfo?.id) {
      fetchUserComments(userInfo.id);
      fetchUserReviews(userInfo.id);
    }

    return () => {
      resetComments();
      resetReviews();
    };
  }, [userInfo?.id, fetchUserComments, fetchUserReviews, resetComments, resetReviews]);

  // 标签页切换
  const handleTabChange = useCallback((tab: TabType) => {
    setActiveTab(tab);
  }, []);

  // 刷新处理
  const handleRefresh = useCallback(() => {
    if (!userInfo?.id) return;

    if (activeTab === 'comments') {
      refreshUserComments(userInfo.id);
    } else {
      refreshUserReviews(userInfo.id, currentFilter);
    }
  }, [userInfo?.id, activeTab, currentFilter, refreshUserComments, refreshUserReviews]);

  // 加载更多处理
  const handleLoadMore = useCallback(() => {
    if (!userInfo?.id) return;

    if (activeTab === 'comments') {
      if (commentsHasMore && !commentsLoading) {
        loadMoreUserComments(userInfo.id);
      }
    } else {
      if (reviewsHasMore && !reviewsLoading) {
        loadMoreUserReviews(userInfo.id, currentFilter);
      }
    }
  }, [userInfo?.id, activeTab, commentsHasMore, commentsLoading, reviewsHasMore, reviewsLoading, currentFilter, loadMoreUserComments, loadMoreUserReviews]);

  // 评论点击处理
  const handleCommentPress = useCallback((comment: Thread) => {
    if (comment.parent_thread_id) {
      router.push(`/(stack)/thread-detail?threadId=${comment.parent_thread_id}&commentId=${comment.thread_id}`);
    } else {
      router.push(`/(stack)/thread-detail?threadId=${comment.thread_id}`);
    }
  }, [router]);

  // 用户点击处理
  const handleUserPress = useCallback((comment: Thread) => {
    router.push(`/(stack)/user-profile?userId=${comment.user.id}`);
  }, [router]);

  // 渲染评论项
  const renderCommentItem = useCallback(({ item }: { item: Thread }) => (
    <CommentItem
      comment={item}
      onPress={() => handleCommentPress(item)}
      onUserPress={() => handleUserPress(item)}
    />
  ), [handleCommentPress, handleUserPress]);

  // 渲染点评项
  const renderReviewItem = useCallback(({ item }: { item: any }) => (
    <UserReviewItem review={item} />
  ), []);

  // 渲染空状态
  const renderEmptyComponent = () => {
    const isComments = activeTab === 'comments';
    return (
      <View style={styles.emptyContainer}>
        <Ionicons
          name={isComments ? "chatbubbles-outline" : "star-outline"}
          size={64}
          color="#ccc"
        />
        <Text style={styles.emptyTitle}>
          {isComments ? '暂无评论' : '暂无点评'}
        </Text>
        <Text style={styles.emptySubtitle}>
          {isComments ? '您还没有发表过任何评论' : '您还没有发表过任何点评'}
        </Text>
      </View>
    );
  };

  // 渲染底部加载状态
  const renderFooter = () => {
    const hasMore = activeTab === 'comments' ? commentsHasMore : reviewsHasMore;
    const loading = activeTab === 'comments' ? commentsLoading : reviewsLoading;
    const items = activeTab === 'comments' ? comments : allReviews;

    if (!hasMore) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            {activeTab === 'comments' ? '没有更多评论了' : '没有更多点评了'}
          </Text>
        </View>
      );
    }

    if (loading && items.length > 0) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
        </View>
      );
    }

    return null;
  };

  // 获取当前标签页的状态
  const currentError = activeTab === 'comments' ? commentsError : reviewsError;
  const currentLoading = activeTab === 'comments' ? commentsLoading : reviewsLoading;
  const currentRefreshing = activeTab === 'comments' ? commentsRefreshing : reviewsRefreshing;
  const currentTotalCount = activeTab === 'comments' ? commentsTotalCount : reviewsTotalCount;
  const currentData = activeTab === 'comments' ? comments : allReviews;

  if (currentError) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>我的评论</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#ff4757" />
          <Text style={styles.errorTitle}>加载失败</Text>
          <Text style={styles.errorSubtitle}>{currentError}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryButtonText}>重试</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>我的评论</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* 标签页 */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'comments' && styles.activeTab]}
          onPress={() => handleTabChange('comments')}
        >
          <Text style={[styles.tabText, activeTab === 'comments' && styles.activeTabText]}>
            回复
          </Text>
          {commentsTotalCount > 0 && (
            <Text style={[styles.tabCount, activeTab === 'comments' && styles.activeTabCount]}>
              {commentsTotalCount}
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'reviews' && styles.activeTab]}
          onPress={() => handleTabChange('reviews')}
        >
          <Text style={[styles.tabText, activeTab === 'reviews' && styles.activeTabText]}>
            点评
          </Text>
          {reviewsTotalCount > 0 && (
            <Text style={[styles.tabCount, activeTab === 'reviews' && styles.activeTabCount]}>
              {reviewsTotalCount}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* 统计信息 */}
      {currentTotalCount > 0 && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            共 {currentTotalCount} 条{activeTab === 'comments' ? '评论' : '点评'}
          </Text>
        </View>
      )}

      {/* 列表 */}
      <FlatList
        data={currentData as any}
        renderItem={activeTab === 'comments' ? renderCommentItem : renderReviewItem}
        keyExtractor={(item: any) => activeTab === 'comments' ? item.thread_id : item.review_id}
        contentContainerStyle={[
          styles.listContainer,
          currentData.length === 0 && styles.emptyListContainer,
        ]}
        refreshControl={
          <RefreshControl
            refreshing={currentRefreshing}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
            tintColor="#007AFF"
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        ListEmptyComponent={!currentLoading ? renderEmptyComponent : null}
        ListFooterComponent={renderFooter}
        showsVerticalScrollIndicator={false}
      />

      {/* 加载状态 */}
      {currentLoading && currentData.length === 0 && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginRight: 6,
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  tabCount: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f1f3f4',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    textAlign: 'center',
  },
  activeTabCount: {
    color: '#007AFF',
    backgroundColor: '#e3f2fd',
  },
  statsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  statsText: {
    fontSize: 14,
    color: '#666',
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  errorSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(248, 249, 250, 0.8)',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  footerContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#666',
  },
});

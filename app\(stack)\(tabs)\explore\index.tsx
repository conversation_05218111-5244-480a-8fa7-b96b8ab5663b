import ExploreTopNav from '@/components/core/explore/ExploreTopNav';
import BrandExploreContent from '@/components/core/explore/brand-content/BrandExploreContent';
import ProductsContent from '@/components/core/explore/product-content/ProductContent';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';



export default function ExploreTab() {
  const [activeTab, setActiveTab] = useState('products');

  const renderContent = () => {
    switch (activeTab) {
      case 'brands':
        return <BrandExploreContent />;
      case 'products':
        return (
          <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
            <ProductsContent />
          </ScrollView>
        );
      default:
        return <BrandExploreContent />;
    }
  };

  return (
    <View style={styles.container}>
      <ExploreTopNav activeTab={activeTab} setActiveTab={setActiveTab} />
      {renderContent()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white'
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    display: 'flex',
    flex: 1,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: 'bold',
  },
  viewAllLink: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 14,
    color: '#666',
  },
  row: {
    justifyContent: 'space-between',
    marginHorizontal: 2
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#3498db',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  }
});

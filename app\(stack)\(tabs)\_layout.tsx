import { Tabs, useSegments } from 'expo-router';
import React from 'react';

import MePageNavBar from '@/components/core/top-nav-bar/MePageNavBar';
import TopNavigationBar from '@/components/core/top-nav-bar/TopNavigationBar';
import BottomTabBar from '@/components/ui/BottomTabBar';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { StatusBar } from 'expo-status-bar';
export default function TabsLayout() {
  const colorScheme = useColorScheme();
  const segments = useSegments();
  const currentTab = segments[segments.length - 1];

  const handleDrawerPress = () => {
    console.log('Open drawer');
  };

  const handleQRScanPress = () => {
    console.log('Open QR scanner');
  };

  const handleSharePress = () => {
    console.log('Share profile');
  };

  return (
    <>
      <StatusBar style="dark" />
      {currentTab === 'me' ? (
        <MePageNavBar
          onDrawerPress={handleDrawerPress}
          onQRScanPress={handleQRScanPress}
          onSharePress={handleSharePress}
        />
      ) : (
        <TopNavigationBar />
      )}
      <Tabs
        tabBar={(props) => <BottomTabBar {...props} />}
        screenOptions={{
          tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
          headerShown: false,
        }}>
        <Tabs.Screen
          name="index"
          options={{
            title: '首页',
            tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
          }}
        />
        <Tabs.Screen
          name="explore/index"
          options={{
            title: '宠粮',
            tabBarIcon: ({ color }) => <IconSymbol size={28} name="pawprint.fill" color={color} />,
          }}
        />
        <Tabs.Screen
          name="community"
          options={{
            title: '社区',
            tabBarIcon: ({ color }) => <IconSymbol size={28} name="bubble.left.and.bubble.right.fill" color={color} />,
          }}
        />
        <Tabs.Screen
          name="me"
          options={{
            title: '我',
            tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.fill" color={color} />,
          }}
        />
      </Tabs>
    </>
  );
}

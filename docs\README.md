# 文档文件夹概述

每个文件都在项目的不同方面发挥特定作用，从高级愿景到详细的实施指南。

## 文件及其用途

### [project-overview.md](Documentation/project-overview.md)
包含核心愿景陈述、主要目标以及关于项目旨在解决或实现什么的高级解释。该文档作为指导所有其他决策的"北极星"。

### [tech-stack.md](Documentation/tech-stack.md)
解释技术选择，说明为什么为项目的不同部分选择特定工具、框架或语言，以及它们如何协同工作。

### [feautures.md](Documentation/feautures.md)
深入探讨各个功能，描述每个功能应如何工作，包括边缘情况和任何特定的业务规则或验证要求。

### [user-flow.md](Documentation/user-flow.md)
映射用户和数据通过系统的完整旅程 - 显示应用程序中每个步骤和互动的详细路线图。


### [price-quote.md](Documentation/price-quote.md)
开发成本，服务器成本报价单
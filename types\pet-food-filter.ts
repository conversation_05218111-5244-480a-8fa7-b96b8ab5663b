export interface NutritionRange {
  min: number;
  max: number;
}

export interface PetFoodFilters {
  // 基础筛选
  searchQuery: string;
  brands: string[];
  productTypes: string[];
  
  // 营养成分筛选
  protein?: NutritionRange;
  fat?: NutritionRange;
  carbs?: NutritionRange;
  calories?: NutritionRange;
  
  // 成分质量筛选
  minQualityIngredients?: number;
  maxQuestionableIngredients?: number;
  excludeAllergens: string[];
  
  // 评价筛选
  minRating?: number;
  minReviewCount?: number;
}

export interface SortOption {
  key: string;
  label: string;
  field: string;
  order: 'asc' | 'desc';
}

export interface FilterState {
  filters: PetFoodFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface FilterGroup {
  key: string;
  label: string;
  type: 'checkbox' | 'range' | 'slider' | 'select';
  options?: FilterOption[];
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
}

// 预定义的排序选项
export const SORT_OPTIONS: SortOption[] = [
  { key: 'quality', label: '综合质量', field: 'qualityScore', order: 'desc' },
  { key: 'protein', label: '蛋白质含量', field: 'protein', order: 'desc' },
  { key: 'fat', label: '脂肪含量', field: 'fat', order: 'desc' },
  { key: 'rating', label: '评分', field: 'rating', order: 'desc' },
  { key: 'reviews', label: '评论数', field: 'review_count', order: 'desc' },
  { key: 'calories', label: '卡路里', field: 'calories', order: 'asc' },
];

// 产品类型选项
export const PRODUCT_TYPE_OPTIONS: FilterOption[] = [
  { value: '干粮', label: '干粮' },
  { value: '湿粮', label: '湿粮' },
  { value: '冻干', label: '冻干' },
  { value: '零食', label: '零食' },
  { value: '其他', label: '其他' },
];

// 常见过敏原
export const COMMON_ALLERGENS: FilterOption[] = [
  { value: 'chicken', label: '鸡肉' },
  { value: 'beef', label: '牛肉' },
  { value: 'fish', label: '鱼类' },
  { value: 'dairy', label: '乳制品' },
  { value: 'grain', label: '谷物' },
  { value: 'soy', label: '大豆' },
  { value: 'egg', label: '鸡蛋' },
];

// 营养成分范围配置
export const NUTRITION_RANGES = {
  protein: { min: 0, max: 60, step: 1, unit: '%' },
  fat: { min: 0, max: 30, step: 0.5, unit: '%' },
  carbs: { min: 0, max: 70, step: 1, unit: '%' },
  calories: { min: 200, max: 600, step: 10, unit: 'kcal/cup' },
};

import brandReviewService from '@/api/services/brandReviewService';
import productReviewService from '@/api/services/productReviewService';
import { useUserInfo } from '@/store/userStore';
import { useRouter } from 'expo-router';
import React from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface SubmitReviewBottomBarProps {
  entityType: 'product' | 'brand';
  entityId: string;
  isFavorited?: boolean;
  onFavoritePress?: () => void;
}

export default function SubmitReviewBottomBar({
  entityType,
  entityId,
  isFavorited = false,
  onFavoritePress
}: SubmitReviewBottomBarProps) {
  const router = useRouter();
  const userInfo = useUserInfo();

  const handleWriteReview = async () => {
    // Check if user is logged in
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录后再发表评论', [
        { text: '取消', style: 'cancel' },
        { text: '去登录', onPress: () => router.push('/login') }
      ]);
      return;
    }

    try {
      // Check if user has already reviewed this entity
      let hasReviewed = false;
      if (entityType === 'brand') {
        const response = await brandReviewService.hasUserReviewedBrand(entityId, userInfo.id);
        hasReviewed = response.hasReviewed;
      } else if (entityType === 'product') {
        const response = await productReviewService.hasUserReviewedProduct(entityId, userInfo.id);
        hasReviewed = response.hasReviewed;
      }

      if (hasReviewed) {
        Alert.alert(
          '提示',
          `您已经对该${entityType === 'brand' ? '品牌' : '产品'}发表过评论，每个${entityType === 'brand' ? '品牌' : '产品'}只能评论一次`,
          [
            { text: '确定', style: 'default' }
          ]
        );
        return;
      }

      // If user hasn't reviewed, proceed to edit review page
      router.push({
        pathname: '/(stack)/edit-review',
        params: {
          entityType,
          entityId
        }
      });
    } catch (error: any) {
      Alert.alert('错误', error.message || '检查评论状态失败，请重试');
    }
  };

  const handleFavorite = () => {
    if (onFavoritePress) {
      onFavoritePress();
    } else {
      // 默认行为 - 显示提示
      Alert.alert(
        isFavorited ? '取消收藏' : '收藏',
        isFavorited ? '确定要取消收藏吗？' : '确定要收藏吗？',
        [
          { text: '取消', style: 'cancel' },
          { text: '确定', onPress: () => console.log('收藏操作') }
        ]
      );
    }
  };

  const handleShare = () => {
    Alert.alert(
      '分享',
      '选择分享方式',
      [
        { text: '取消', style: 'cancel' },
        { text: '复制链接', onPress: () => console.log('复制链接') },
        { text: '分享到微信', onPress: () => console.log('分享到微信') }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.button} onPress={handleFavorite}>
        <Icon 
          name={isFavorited ? 'heart' : 'heart-outline'} 
          size={24} 
          color={isFavorited ? '#e74c3c' : '#666'} 
        />
        <Text style={[styles.buttonText, isFavorited && styles.favoriteText]}>
          {isFavorited ? '已收藏' : '收藏'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.primaryButton]} onPress={handleWriteReview}>
        <Icon name="pencil" size={24} color="#fff" />
        <Text style={styles.primaryButtonText}>写评论</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={handleShare}>
        <Icon name="share-variant" size={24} color="#666" />
        <Text style={styles.buttonText}>分享</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 7,
    paddingHorizontal: 1,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  primaryButton: {
    backgroundColor: '#3498db',
    marginHorizontal: 8,
  },
  buttonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  primaryButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  favoriteText: {
    color: '#e74c3c',
  },
});
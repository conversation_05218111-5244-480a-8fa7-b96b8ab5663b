import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// 创建一个简单的事件发射器
class EventEmitter {
  private listeners: { [key: string]: Function[] } = {};

  on(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event: string, callback: Function) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
  }

  emit(event: string, ...args: any[]) {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(callback => callback(...args));
  }
}

export const globalEvents = new EventEmitter();

export default function BottomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const colorScheme = useColorScheme();
  const activeColor = Colors[colorScheme ?? 'light'].tint;
  const inactiveColor = '#8E8E93';

  const handleCreatePost = () => {
    // 发射创建帖子事件
    globalEvents.emit('createPost');
  };

  return (
    <View style={styles.tabBar}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label = typeof options.tabBarLabel === 'string'
          ? options.tabBarLabel
          : typeof options.title === 'string'
            ? options.title
            : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        // 获取图标名称
        const getIconName = () => {
          switch (route.name) {
            case 'index':
              return 'home';
            case 'explore':
              return 'paw';
            case 'community':
              return 'chatbubbles';
            case 'me':
              return 'person';
            default:
              return 'ellipse';
          }
        };

        // 如果是第三个tab（community），在它前面插入发帖按钮
        if (index === 2) {
          return (
            <React.Fragment key={route.key}>
              {/* 发帖按钮 */}
              <TouchableOpacity
                style={styles.createButton}
                onPress={handleCreatePost}
                activeOpacity={0.7}
              >
                <View style={styles.createButtonBackground}>
                  <Ionicons name="add" size={28} color="#FFFFFF" style={styles.submitButtonIcon} />
                </View>
              </TouchableOpacity>

              {/* 原来的tab */}
              <TouchableOpacity
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                onPress={onPress}
                onLongPress={onLongPress}
                style={styles.tabItem}
              >
                <Ionicons 
                  size={28}
                  name={getIconName()}
                  color={isFocused ? activeColor : inactiveColor}
                  />
                <Text style={[
                  styles.tabLabel,
                  { color: isFocused ? activeColor : inactiveColor }
                ]}>
                  {label}
                </Text>
              </TouchableOpacity>
            </React.Fragment>
          );
        }

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            style={styles.tabItem}
          >
            <Ionicons
              size={28}
              name={getIconName()}
              color={isFocused ? activeColor : inactiveColor}
            />
            <Text style={[
              styles.tabLabel,
              { color: isFocused ? activeColor : inactiveColor }
            ]}>
              {label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderTopWidth: 0.5,
    borderTopColor: '#E5E5EA',
    paddingBottom: Platform.select({
      ios: 20,
      android: 10,
      default: 10,
    }),
    height: Platform.select({
      ios: 65,
      android: 60,
      default: 70,
    }),
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 10,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 8,
  },
  tabLabel: {
    fontSize: 10,
    marginTop: 4,
    textAlign: 'center',
  },
  submitButtonIcon: {
    color: '#FFFFFF',
  },
  createButton: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -10, // 让按钮稍微突出一点
  },
  createButtonBackground: {
    width: 50,
    height: 40,
    borderRadius: 15,
    backgroundColor: '#006400', // 使用绿色主题
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.25,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
});

import apiClient from "../apiClient";

export interface I18nEntry {
	id: string;
	locale: string;
	namespace: string;
	key: string;
	value: string;
	parent: string | null;
	createdAt?: string;
	updatedAt?: string;
}
export type I18nItemsResponse = {
	items: I18nEntry[];
	totalCount: number;
	totalPages: number;
	currentPage: number;
};

export type I18TreeResponse = Record<string, Record<string, any>>;

export enum I18nApi {
	I18n = "i18n",
}

// 获取i18n树结构 - 所有翻译
const getI18nTree = async () => {
	try {
		const response = await apiClient.get<I18TreeResponse>({
			url: `${I18nApi.I18n}/tree`,
		});
		return response;
	} catch (error) {
		console.error("I18n Tree API error:", error);
		throw error;
	}
};

// 获取i18n条目
const getI18nEntries = async (value?: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<I18nItemsResponse>({
			url: `${I18nApi.I18n}`,
			params: { value, page, limit },
		});
		return response;
	} catch (error) {
		console.error("I18n API error:", error);
		throw error;
	}
};

// 获取特定语言的i18n条目
const getI18nByLocale = async (locale: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<I18nItemsResponse>({
			url: `${I18nApi.I18n}/locale/${locale}`,
			params: { page, limit },
		});
		return response;
	} catch (error) {
		console.error(`I18n API error for locale ${locale}:`, error);
		throw error;
	}
};

// 获取特定命名空间的i18n条目
const getI18nByNamespace = async (namespace: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<I18nItemsResponse>({
			url: `${I18nApi.I18n}/namespace/${namespace}`,
			params: { page, limit },
		});
		return response;
	} catch (error) {
		console.error(`I18n API error for namespace ${namespace}:`, error);
		throw error;
	}
};

// 获取特定语言和命名空间的i18n条目
const getI18nByLocaleAndNamespace = async (locale: string, namespace: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<I18nItemsResponse>({
			url: `${I18nApi.I18n}/locale/${locale}/namespace/${namespace}`,
			params: { page, limit },
		});
		return response;
	} catch (error) {
		console.error(`I18n API error for locale ${locale} and namespace ${namespace}:`, error);
		throw error;
	}
};

// 获取特定ID的i18n条目
const getI18nById = async (id: string) => {
	try {
		const response = await apiClient.get<I18nEntry>({
			url: `${I18nApi.I18n}/${id}`,
		});
		return response;
	} catch (error) {
		console.error(`I18n API error for id ${id}:`, error);
		throw error;
	}
};

// 创建i18n条目
const createI18nEntry = async (data: Partial<I18nEntry>) => {
	try {
		const response = await apiClient.post<I18nEntry>({
			url: `${I18nApi.I18n}`,
			data,
		});
		return response;
	} catch (error) {
		console.error("I18n create API error:", error);
		throw error;
	}
};

// 更新i18n条目
const updateI18nEntry = async (id: string, data: Partial<I18nEntry>) => {
	try {
		const response = await apiClient.put<I18nEntry>({
			url: `${I18nApi.I18n}/${id}`,
			data,
		});
		return response;
	} catch (error) {
		console.error(`I18n update API error for id ${id}:`, error);
		throw error;
	}
};

// 删除i18n条目
const deleteI18nEntry = async (id: string) => {
	try {
		const response = await apiClient.delete<I18nEntry>({
			url: `${I18nApi.I18n}/${id}`,
		});
		return response;
	} catch (error) {
		console.error(`I18n delete API error for id ${id}:`, error);
		throw error;
	}
};

export default {
	getI18nTree,
	getI18nEntries,
	getI18nByLocale,
	getI18nByNamespace,
	getI18nByLocaleAndNamespace,
	getI18nById,
	createI18nEntry,
	updateI18nEntry,
	deleteI18nEntry,
};

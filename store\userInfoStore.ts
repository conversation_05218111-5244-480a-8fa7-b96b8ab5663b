import { useQuery } from '@tanstack/react-query';
import userService, { UserBasicInfo } from '@/api/services/userService';

// 使用React Query缓存用户基本信息
export const useUserBasicInfo = (userId: string | null) => {
  return useQuery({
    queryKey: ['user-basic-info', userId],
    queryFn: () => userService.getUserBasicInfo(userId!),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    gcTime: 10 * 60 * 1000, // 10分钟垃圾回收
    retry: false, // 不重试，因为我们有fallback
  });
};

// 批量获取用户信息的hook
export const useUsersBasicInfo = (userIds: string[]) => {
  return useQuery({
    queryKey: ['users-basic-info', userIds.sort().join(',')],
    queryFn: () => userService.getUsersBasicInfo(userIds),
    enabled: userIds.length > 0,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

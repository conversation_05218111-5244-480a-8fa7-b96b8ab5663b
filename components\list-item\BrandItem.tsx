import StarRating from '@/components/ui/StartRating';
import { SearchBrand } from '@/types/search-type';
import { getRatingScore } from '@/utils/commonUtils';
import { getBrandLogoUrlSync } from '@/utils/imageUtils';
import { Image } from 'expo-image';
import React, { useState } from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Icon from 'react-native-vector-icons/EvilIcons';


interface BrandItemProps {
    item: SearchBrand;
    onBrandPress: (brandId: string) => void;
}

const BrandItem: React.FC<BrandItemProps> = ({
    item,
    onBrandPress
}) => {
    const [imageLoadError, setImageLoadError] = useState(false);
    const brandId = item.id || '';
    const imageUrl = brandId && !imageLoadError ? getBrandLogoUrlSync(brandId) : '';

    return (
        <TouchableOpacity style={styles.brandItem} onPress={() => onBrandPress(brandId)}>
            <View style={styles.brandImageContainer}>
                {imageUrl ? (
                    <Image
                        source={{ uri: imageUrl }}
                        style={styles.brandImage}
                        contentFit="contain"
                        transition={200}
                        onError={() => {
                            console.log('图片加载失败:', imageUrl);
                            setImageLoadError(true);
                        }}
                        onLoad={() => console.log('图片加载成功:', imageUrl)}
                    />
                ) : (
                    <View style={styles.brandImagePlaceholder}>
                        <Icon name="image" size={24} color="#ccc" />
                    </View>
                )}
            </View>
            <View style={styles.brandInfo}>
                <Text style={styles.brandName} numberOfLines={2}>{item.name}</Text>
                <Text style={styles.brandBrand}>{item.products} 款产品</Text>
                <View style={styles.brandRatingContainer}>
                    <StarRating
                        score={getRatingScore(item.rating)}
                    />
                    <Text style={styles.brandReviewCount}>
                        {item.review_count ? `${item.review_count}条评论` : '暂无评论'}
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    );
};


const styles = StyleSheet.create({
    listContainer: {
        paddingHorizontal: 0,
    },
    brandItem: {
        flexDirection: 'row',
        justifyContent: 'center',
        padding: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    brandImageContainer: {
        width: 80,
        height: 80,
        borderRadius: 8,
        overflow: 'hidden',
    },
    brandImage: {
        width: '100%',
        height: '100%',
    },
    brandImagePlaceholder: {
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9f9f9',
    },
    brandInfo: {
        flex: 1,
        marginLeft: 12,
        justifyContent: 'space-between',
    },
    brandName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    brandBrand: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
    },
    brandRatingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        flexWrap: 'wrap',
    },
    brandRating: {
        fontSize: 14,
        color: '#666',
        marginLeft: 4,
        marginRight: 12,
    },
    brandReviewCount: {
        fontSize: 14,
        color: '#999',
        marginLeft: 8,
    },
    brandCategoryContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    brandCategory: {
        fontSize: 12,
        color: '#666',
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
    },
    loadingFooter: {
        paddingVertical: 20,
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: '#666',
    },
    endOfListContainer: {
        paddingVertical: 20,
        alignItems: 'center',
    },
    endOfListText: {
        fontSize: 14,
        color: '#999',
    },
});


export default BrandItem;
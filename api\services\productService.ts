import type { Product } from "@/types/entity";

import apiClient from "../apiClient";

export interface ProductRes {
	success: boolean;
	status?: number;
	message?: string;
	totalCount?: number;
	totalPages?: number;
	currentPage?: number;
	products?: Product[];
	product?: Product;
}

export interface ImageResult {
	productId: string;
	imagePath?: string;
	exists: boolean;
	brand?: string;
}

export interface ProductImagesRes {
	success: boolean;
	status?: number;
	message?: string;
	totalCount?: number;
	totalPages?: number;
	currentPage?: number;
	images?: ImageResult[];
}

export enum ProductApi {
	Product = "products",
}

const getProducts = async (page = 1, name?: string) => {
	try {
		const params: Record<string, any> = { page };
		if (name) params.name = name;

		const response = await apiClient.get<ProductRes>({
			url: `${ProductApi.Product}`,
			params,
		});
		return response;
	} catch (error) {
		console.error("Product API error:", error);
		throw error;
	}
};

const getProductById = async (id: string) => {
	try {
		const response = await apiClient.get<Product>({
			url: `${ProductApi.Product}/${id}`,
		});
		return response;
	} catch (error) {
		console.error("Product API error:", error);
		throw error;
	}
};

const getProductRanking = async (
	page = 1,
	sortBy:
		| "quality"
		| "protein"
		| "fat"
		| "carbs"
		| "calories"
		| "quality_ingredients"
		| "questionable_ingredients" = "quality",
	sortOrder: "asc" | "desc" = "desc",
	limit = 10,
	name?: string,
) => {
	try {
		const params: Record<string, any> = {
			page,
			sortBy,
			sortOrder,
			limit,
		};

		if (name) params.name = name;

		const response = await apiClient.get<ProductRes>({
			url: `${ProductApi.Product}/ranking`,
			params,
		});
		return response;
	} catch (error) {
		console.error("Product Ranking API error:", error);
		throw error;
	}
};

const createProduct = async (productData: Partial<Product>) => {
	try {
		const response = await apiClient.post<ProductRes>({
			url: `${ProductApi.Product}`,
			data: productData,
		});
		return response;
	} catch (error) {
		console.error("Product Create API error:", error);
		throw error;
	}
};

const getProductImages = async (productIds: string[], page = 1, limit = 10) => {
	try {
		const response = await apiClient.post<ProductImagesRes>({
			url: `${ProductApi.Product}/images`,
			data: {
				productIds,
				page,
				limit
			}
		});
		return response;
	} catch (error) {
		console.error("Product Images API error:", error);
		throw error;
	}
};

const getBrandImages = async (brandName: string) => {
	try {
		const response = await apiClient.get<{
			brand: string;
			images: string[];
			count: number;
		}>({
			url: `${ProductApi.Product}/images/brand/${encodeURIComponent(brandName)}`,
		});
		return response;
	} catch (error) {
		console.error("Brand Images API error:", error);
		throw error;
	}
};

const getAllImages = async () => {
	try {
		const response = await apiClient.get<{
			images: string[];
			count: number;
		}>({
			url: `${ProductApi.Product}/images/all`,
		});
		return response;
	} catch (error) {
		console.error("All Images API error:", error);
		throw error;
	}
};

export default {
	getProducts,
	getProductById,
	getProductRanking,
	createProduct,
	getProductImages,
	getBrandImages,
	getAllImages,
};

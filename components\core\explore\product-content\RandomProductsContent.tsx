import reviewService, { PopularProduct } from "@/api/services/productReviewService";
import ProductCard from "@/components/core/explore/show/ProductCard";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import { usePetFoodFilter } from '@/hooks/usePetFoodFilter';
import { useFilteredProducts } from '@/hooks/useFilteredProducts';
import FilterModal from '@/components/core/pet-food-finder/FilterModal';
import FilterChips from '@/components/core/pet-food-finder/FilterChips';

export default function RandomProductsContent() {
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSmartFilter, setShowSmartFilter] = useState(false);

  // 智能筛选状态管理
  const {
    filterState,
    hasActiveFilters,
    activeFilterCount,
    updateSearchQuery,
    updateNutritionRange,
    updateQualityFilters,
    updateAllergens,
    updateRatingFilters,
    resetFilters,
    resetFilter,
  } = usePetFoodFilter();

  // 获取筛选后的产品数据
  const {
    products: filteredProducts,
    totalCount: filteredTotalCount,
    isLoading: isFilteredLoading,
    fetchNextPage,
    hasNextPage,
  } = useFilteredProducts(filterState);

  const { data: randomProducts, isLoading, error } = useQuery<PopularProduct[]>({
    queryKey: ["daily-random-products"],
    queryFn: async () => {
      const products = await reviewService.getDailyRandomProducts();
      return products;
    },
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
  });

  const products = useMemo(() => {
    if (!Array.isArray(randomProducts) || randomProducts.length === 0) {
      return [];
    }

    return randomProducts.map((product: PopularProduct) => {
      return {
        _id: product.product_id,
        brand: product.brand || "Unknown Brand",
        image_url: product.image_url || "",
        name: product.name || "Unknown Product",
        // Preserve "暂无评分" as a string instead of converting to number
        average_rating: typeof product.average_rating === 'string' && product.average_rating === "暂无评分" 
          ? "暂无评分" 
          : typeof product.average_rating === 'number' 
            ? product.average_rating 
            : typeof product.average_rating === 'string' && !isNaN(parseFloat(product.average_rating))
              ? parseFloat(product.average_rating)
              : "暂无评分",
        review_count: product.review_count || 0,
      };
    });
  }, [randomProducts]);

  // 决定显示哪些产品：如果有筛选条件，显示筛选结果；否则显示随机产品
  const displayedProducts = useMemo(() => {
    if (showSmartFilter && hasActiveFilters) {
      // 转换筛选结果为ProductCard格式
      return filteredProducts.slice(0, 9).map(product => ({
        _id: product.id || product._id || '',
        brand: product.brand || "Unknown Brand",
        image_url: product.image_url || "",
        name: product.name || "Unknown Product",
        average_rating: product.rating || 0,
        review_count: product.review_count || 0,
        product_type: product.product_type || product.category,
      }));
    } else {
      return products.slice(0, 9);
    }
  }, [products, filteredProducts, showSmartFilter, hasActiveFilters]);

  const handleSmartFilterToggle = () => {
    setShowSmartFilter(!showSmartFilter);
    if (!showSmartFilter) {
      resetFilters(); // 切换到智能筛选时重置筛选条件
    }
  };

  const handleRemoveFilter = (filterKey: keyof typeof filterState.filters, value?: string) => {
    if (value && filterKey === 'excludeAllergens') {
      const currentValues = filterState.filters.excludeAllergens;
      const newValues = currentValues.filter(v => v !== value);
      updateAllergens(newValues);
    } else {
      resetFilter(filterKey);
    }
  };

  if (isLoading && !showSmartFilter) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <ActivityIndicator size="small" color="#3498db" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (error || (products.length === 0 && !showSmartFilter)) {
    return (
      <View style={[styles.contentContainer, styles.centered]}>
        <Icon
          name="alert-circle-outline"
          size={40}
          color="#e74c3c"
          style={styles.errorIcon}
        />
        <Text style={styles.errorText}>无法加载随机产品数据</Text>
      </View>
    );
  }

  return (
    <View style={styles.contentContainer}>
      {/* 智能找宠粮标题和切换按钮 */}
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>
          {showSmartFilter ? '智能找宠粮' : '找宠粮'}
        </Text>
        <TouchableOpacity
          style={[styles.smartFilterButton, showSmartFilter && styles.smartFilterButtonActive]}
          onPress={handleSmartFilterToggle}
        >
          <Icon
            name={showSmartFilter ? "tune" : "tune-variant"}
            size={16}
            color={showSmartFilter ? "#fff" : "#4d920f"}
          />
          <Text style={[styles.smartFilterText, showSmartFilter && styles.smartFilterTextActive]}>
            {showSmartFilter ? '智能筛选' : '智能筛选'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* 智能筛选控制区域 */}
      {showSmartFilter && (
        <View style={styles.filterControlsContainer}>
          <TouchableOpacity
            style={[styles.filterButton, hasActiveFilters && styles.filterButtonActive]}
            onPress={() => setShowFilterModal(true)}
          >
            <Icon name="filter-variant" size={16} color={hasActiveFilters ? "#fff" : "#4d920f"} />
            <Text style={[styles.filterButtonText, hasActiveFilters && styles.filterButtonTextActive]}>
              筛选
            </Text>
          </TouchableOpacity>

          {hasActiveFilters && (
            <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
              <Text style={styles.resetButtonText}>重置</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* 已选筛选条件 */}
      {showSmartFilter && hasActiveFilters && (
        <FilterChips
          filters={filterState.filters}
          onRemoveFilter={handleRemoveFilter}
          onClearAll={resetFilters}
        />
      )}

      {/* 产品列表 */}
      <FlatList
        data={displayedProducts}
        renderItem={({ item }) => (
          <ProductCard
            product={item}
            totalCount={showSmartFilter ? filteredTotalCount : products.length}
          />
        )}
        keyExtractor={(item) => item._id}
        numColumns={3}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {showSmartFilter && hasActiveFilters ? '没有找到符合条件的产品' : '暂无产品数据'}
            </Text>
            {showSmartFilter && hasActiveFilters && (
              <Text style={styles.emptySubtext}>试试调整筛选条件</Text>
            )}
          </View>
        }
      />

      {/* 筛选弹窗 */}
      <FilterModal
        visible={showFilterModal}
        filters={filterState.filters}
        onClose={() => setShowFilterModal(false)}
        onFiltersChange={{
          updateSearchQuery,
          updateBrands: () => {}, // 删除品牌筛选
          updateProductTypes: () => {}, // 删除产品类型筛选
          updateNutritionRange,
          updateQualityFilters,
          updateAllergens,
          updateRatingFilters,
        }}
        onResetFilters={resetFilters}
        availableBrands={[]} // 不提供品牌选项
        activeFilterCount={activeFilterCount}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    padding: 16,
    marginTop: 8,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: "bold",
  },
  viewAllLink: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    color: "#666",
  },
  row: {
    justifyContent: "space-between",
    marginHorizontal: 2,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorIcon: {
    marginBottom: 12,
  },
  errorText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: "#3498db",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
  },
  emptySubtext: {
    fontSize: 14,
    color: "#999",
    marginTop: 8,
  },
  smartFilterButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#4d920f",
    backgroundColor: "#fff",
  },
  smartFilterButtonActive: {
    backgroundColor: "#4d920f",
    borderColor: "#4d920f",
  },
  smartFilterText: {
    fontSize: 12,
    color: "#4d920f",
    fontWeight: "500",
  },
  smartFilterTextActive: {
    color: "#fff",
  },
  filterControlsContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#4d920f",
    backgroundColor: "#fff",
  },
  filterButtonActive: {
    backgroundColor: "#4d920f",
  },
  filterButtonText: {
    fontSize: 12,
    color: "#4d920f",
    fontWeight: "500",
  },
  filterButtonTextActive: {
    color: "#fff",
  },
  resetButton: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
  },
  resetButtonText: {
    fontSize: 12,
    color: "#666",
    fontWeight: "500",
  },
});

import apiClient from '../apiClient';

export interface UserReview {
  review_id: string;
  user_id: string;
  rating: number;
  review_text: string;
  review_time: string;
  likes: number;
  dislikes: number;
  is_long_review: boolean;
  review_status: boolean;
  type: 'product' | 'brand';
  // Product-specific fields
  product_id?: string;
  product?: {
    id: string;
    name: string;
    brand: string;
    image_url: string;
  };
  // Brand-specific fields
  brand_id?: string;
  brand?: {
    id: string;
    name: string;
    logo_url: string;
    desc: string;
  };
}

export interface UserReviewsResponse {
  reviews: UserReview[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export interface CombinedUserReviewsResponse {
  productReviews: UserReview[];
  brandReviews: UserReview[];
  allReviews: UserReview[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export enum UserReviewApi {
  ProductReviews = 'reviews/user',
  BrandReviews = 'brand-reviews/user',
}

// 获取用户的产品点评
const getUserProductReviews = async (userId: string, page = 1, limit = 10) => {
  try {
    const response = await apiClient.get<UserReviewsResponse>({
      url: `${UserReviewApi.ProductReviews}/${userId}/reviews`,
      params: { page, limit },
    });
    
    // 为每个点评添加类型标识
    const reviewsWithType = response.reviews.map(review => ({
      ...review,
      type: 'product' as const
    }));
    
    return {
      ...response,
      reviews: reviewsWithType
    };
  } catch (error) {
    console.error('Get user product reviews API error:', error);
    throw error;
  }
};

// 获取用户的品牌点评
const getUserBrandReviews = async (userId: string, page = 1, limit = 10) => {
  try {
    const response = await apiClient.get<UserReviewsResponse>({
      url: `${UserReviewApi.BrandReviews}/${userId}/reviews`,
      params: { page, limit },
    });
    
    // 为每个点评添加类型标识
    const reviewsWithType = response.reviews.map(review => ({
      ...review,
      type: 'brand' as const
    }));
    
    return {
      ...response,
      reviews: reviewsWithType
    };
  } catch (error) {
    console.error('Get user brand reviews API error:', error);
    throw error;
  }
};

// 获取用户的所有点评（产品+品牌）
const getUserAllReviews = async (userId: string, page = 1, limit = 10): Promise<CombinedUserReviewsResponse> => {
  try {
    // 并行获取产品和品牌点评
    const [productResponse, brandResponse] = await Promise.all([
      getUserProductReviews(userId, page, Math.ceil(limit / 2)),
      getUserBrandReviews(userId, page, Math.ceil(limit / 2))
    ]);

    // 合并所有点评并按时间排序
    const allReviews = [
      ...productResponse.reviews,
      ...brandResponse.reviews
    ].sort((a, b) => new Date(b.review_time).getTime() - new Date(a.review_time).getTime());

    // 分页处理合并后的结果
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedReviews = allReviews.slice(startIndex, endIndex);

    const totalCount = productResponse.totalCount + brandResponse.totalCount;
    const totalPages = Math.ceil(totalCount / limit);

    return {
      productReviews: productResponse.reviews,
      brandReviews: brandResponse.reviews,
      allReviews: paginatedReviews,
      totalCount,
      totalPages,
      currentPage: page
    };
  } catch (error) {
    console.error('Get user all reviews API error:', error);
    throw error;
  }
};

const userReviewService = {
  getUserProductReviews,
  getUserBrandReviews,
  getUserAllReviews,
};

export default userReviewService;

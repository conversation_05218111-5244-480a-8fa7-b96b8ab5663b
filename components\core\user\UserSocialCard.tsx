import type { Thread } from '@/api/services/threadsService';
import ThreadCard from '@/components/threads/ThreadCard';
import { useThreads } from '@/store/threadsStore';
import { useUserDetail, useUserInfo } from '@/store/userStore';
import { useRouter } from 'expo-router';
import React, { useCallback, useMemo, useState } from 'react';
import {
    ActivityIndicator,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface UserSocialCardProps {
    userId: string;
    onTabChange?: (tab: string) => void;
    onEditProfile?: () => void;
    onLogin?: () => void;
    onFollow?: () => void;
    onMessage?: () => void;
    isFollowing?: boolean;
}

type TabType = 'posts' | 'likes' | 'bookmarks';


export default function UserSocialCard({
    userId,
    onTabChange,
    onEditProfile,
    onLogin,
    onFollow,
    onMessage,
    isFollowing = false
}: UserSocialCardProps) {
    const router = useRouter();
    const userInfo = useUserInfo();
    const [activeTab, setActiveTab] = useState<TabType>('posts');
    const [isManualRefreshing, setIsManualRefreshing] = useState(false);
    const { useUserThreads, toggleLike, toggleBookmark } = useThreads();

    // 根据 userId 和 activeTab 获取用户的帖子数据
    const {
        data,
        isLoading,
        isFetching,
        isFetchingNextPage,
        hasNextPage,
        fetchNextPage,
        refetch,
        error,
    } = useUserThreads(userId, activeTab);

    const { user } = useUserDetail(userId);

    // 判断是否为当前用户
    const isCurrentUser = useMemo(() => {
        return userInfo.id === userId;
    }, [userInfo.id, userId]);



    // 扁平化所有页面的帖子数据
    const threads = useMemo(() => {
        if (!data?.pages) return [];
        return data.pages.flatMap(page => page.threads);
    }, [data]);

    // 判断是否为初始加载（没有任何数据且正在加载）
    const isInitialLoading = isLoading && threads.length === 0;

    // 切换标签
    const handleTabPress = useCallback((tab: TabType) => {
        if (tab === activeTab) return;

        setActiveTab(tab);
        onTabChange?.(tab);
    }, [activeTab, onTabChange]);

    // 下拉刷新
    const handleRefresh = useCallback(async () => {
        setIsManualRefreshing(true);
        try {
            await refetch();
        } finally {
            setIsManualRefreshing(false);
        }
    }, [refetch]);

    // 加载更多
    const handleLoadMore = useCallback(() => {
        if (hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
        }
    }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

    // 帖子交互处理
    const handleThreadPress = useCallback((thread: Thread) => {
        router.push(`/(stack)/thread-detail?threadId=${thread.thread_id}`);
    }, [router]);

    const handleUserPress = useCallback((user: any) => {
        if (user.id !== userId) {
            router.push(`/(stack)/user-profile?userId=${user.id}`);
        }
    }, [router, userId]);

    const handleLike = useCallback(async (threadId: string) => {
        if (!userInfo.id) return;
        try {
            toggleLike({ threadId, userId: userInfo.id });
        } catch (error) {
            console.error('Toggle like failed:', error);
        }
    }, [userInfo.id, toggleLike]);

    const handleBookmark = useCallback(async (threadId: string) => {
        if (!userInfo.id) return;
        try {
            toggleBookmark({ threadId, userId: userInfo.id });
        } catch (error) {
            console.error('Toggle bookmark failed:', error);
        }
    }, [userInfo.id, toggleBookmark]);

    const handleComment = useCallback((thread: Thread) => {
        router.push(`/(stack)/thread-detail?threadId=${thread.thread_id}`);
    }, [router]);

    // 渲染帖子项
    const renderThread = useCallback(({ item }: { item: Thread }) => (
        <ThreadCard
            thread={item}
            onPress={() => handleThreadPress(item)}
            onUserPress={() => handleUserPress(item.user)}
            onLike={() => handleLike(item.thread_id)}
            onBookmark={() => handleBookmark(item.thread_id)}
            onComment={() => handleComment(item)}
            isLiking={false}
            isBookmarking={false}
            showFullActions={true}
        />
    ), [handleThreadPress, handleUserPress, handleLike, handleBookmark, handleComment]);

    // 渲染加载更多组件
    const renderFooter = useCallback(() => {
        if (!isFetchingNextPage) return null;

        return (
            <View style={styles.loadingFooter}>
                <ActivityIndicator size="small" color="#007AFF" />
                <Text style={styles.loadingText}>加载中...</Text>
            </View>
        );
    }, [isFetchingNextPage]);

    // 渲染空状态
    const renderEmpty = useCallback(() => {
        if (isInitialLoading) return null;

        const emptyMessages = {
            posts: '还没有发布任何帖子',
            likes: '还没有点赞任何帖子',
            bookmarks: '还没有收藏任何帖子'
        };

        return (
            <View style={styles.emptyContainer}>
                <Icon
                    name={activeTab === 'posts' ? 'post-outline' : activeTab === 'likes' ? 'heart-outline' : 'bookmark-outline'}
                    size={64}
                    color="#CCC"
                />
                <Text style={styles.emptyText}>{emptyMessages[activeTab]}</Text>
            </View>
        );
    }, [isInitialLoading, activeTab]);


    // 缓存 Tab Header 组件，避免重新渲染
    const TabHeader = useMemo(() => (
        <View style={styles.tabHeader}>
            <TouchableOpacity
                style={[styles.tab, activeTab === 'posts' && styles.activeTab]}
                onPress={() => handleTabPress('posts')}
            >
                <Text style={[styles.tabText, activeTab === 'posts' && styles.activeTabText]}>
                    发文
                </Text>
            </TouchableOpacity>

            <TouchableOpacity
                style={[styles.tab, activeTab === 'likes' && styles.activeTab]}
                onPress={() => handleTabPress('likes')}
            >
                <Text style={[styles.tabText, activeTab === 'likes' && styles.activeTabText]}>
                    赞过
                </Text>
            </TouchableOpacity>

            <TouchableOpacity
                style={[styles.tab, activeTab === 'bookmarks' && styles.activeTab]}
                onPress={() => handleTabPress('bookmarks')}
            >
                <Text style={[styles.tabText, activeTab === 'bookmarks' && styles.activeTabText]}>
                    收藏
                </Text>
            </TouchableOpacity>
        </View>
    ), [activeTab, handleTabPress]);

    // 缓存整个 ListHeader，只有 ListHeaderComponent 变化时才重新渲染
    const MemoizedListHeader = useMemo(() => {
        const components = [];

        // 如果有用户信息，显示用户信息简介
        if (user) {
            const UserProfileHeader = require('./UserProfileHeader').default;
            components.push(
                <UserProfileHeader
                    key="user-header"
                    user={user}
                    isCurrentUser={isCurrentUser}
                    onEditProfile={onEditProfile}
                    onLogin={onLogin}
                    onFollow={onFollow}
                    onMessage={onMessage}
                    isFollowing={isFollowing}
                />
            );
        }

        // 添加标签页头部
        components.push(<View key="tab-header">{TabHeader}</View>);

        return components.length > 1 ? (
            <View>{components}</View>
        ) : components[0] || TabHeader;
    }, [user, isCurrentUser, TabHeader, onEditProfile, onLogin, onFollow, onMessage, isFollowing]);

    return (
        <View style={styles.container}>
            <FlatList
                data={threads}
                renderItem={renderThread}
                keyExtractor={(item) => item.thread_id}
                ListHeaderComponent={MemoizedListHeader}
                refreshControl={
                    <RefreshControl
                        refreshing={isManualRefreshing}
                        onRefresh={handleRefresh}
                        colors={['#3498db']}
                        tintColor="#3498db"
                    />
                }
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={renderFooter}
                ListEmptyComponent={renderEmpty}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={threads.length === 0 ? styles.emptyContentContainer : styles.contentContainer}
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                windowSize={10}
                removeClippedSubviews={true}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        minHeight: 300,
        flex: 1,
    },
    tabHeader: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    tab: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        gap: 6,
    },
    activeTab: {
        borderBottomWidth: 2,
        borderBottomColor: '#3498db',
    },
    tabText: {
        fontSize: 14,
        color: '#666',
        fontWeight: '500',
    },
    activeTabText: {
        color: '#3498db',
        fontWeight: '600',
    },
    contentContainer: {
        paddingBottom: 20,
    },
    emptyContentContainer: {
        flexGrow: 1,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    emptyText: {
        marginTop: 16,
        fontSize: 16,
        color: '#999',
        textAlign: 'center',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    errorText: {
        marginTop: 16,
        fontSize: 16,
        color: '#FF6B6B',
        textAlign: 'center',
        marginBottom: 20,
    },
    retryButton: {
        paddingHorizontal: 20,
        paddingVertical: 10,
        backgroundColor: '#3498db',
        borderRadius: 8,
    },
    retryText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '600',
    },
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingFooter: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 16,
        gap: 8,
    },
    loadingText: {
        fontSize: 14,
        color: '#666',
    },
});

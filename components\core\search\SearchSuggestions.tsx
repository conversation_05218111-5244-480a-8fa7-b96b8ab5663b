import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface SearchSuggestionsProps {
  searchHistory: string[];
  hotSearches: string[];
  onHistoryItemPress: (item: string) => void;
  onHotSearchPress: (item: string) => void;
  onClearHistory: () => void;
}

const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  searchHistory,
  hotSearches,
  onHistoryItemPress,
  onHotSearchPress,
  onClearHistory,
}) => {
  const renderHotSearchItem = (item: string, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.hotSearchItem}
      onPress={() => onHotSearchPress(item)}
    >
      <Text style={styles.hotSearchText} numberOfLines={1}>
        {item}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.searchContent}>
      {/* 搜索历史 */}
      {searchHistory.length > 0 && (
        <View style={styles.historySection}>
          <View style={styles.historySectionHeader}>
            <Text style={styles.sectionTitle}>搜索历史</Text>
            <TouchableOpacity onPress={onClearHistory}>
              <Ionicons name="trash-outline" size={18} color="#999" />
            </TouchableOpacity>
          </View>
          <FlatList
            data={searchHistory}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.historyItem}
                onPress={() => onHistoryItemPress(item)}
              >
                <Ionicons name="time-outline" size={16} color="#999" style={styles.historyIcon} />
                <Text style={styles.historyText}>{item}</Text>
              </TouchableOpacity>
            )}
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}

      {/* 热门搜索 */}
      <View style={styles.hotSearchSection}>
        <Text style={styles.sectionTitle}>热门搜索</Text>
        <View style={styles.hotSearchContainer}>
          {hotSearches.map((item, index) => renderHotSearchItem(item, index))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  searchContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  historySection: {
    marginBottom: 32,
  },
  historySectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  historyIcon: {
    marginRight: 12,
  },
  historyText: {
    fontSize: 16,
    color: '#333',
  },
  hotSearchSection: {
    flex: 1,
  },
  hotSearchContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 16,
  },
  hotSearchItem: {
    backgroundColor: '#f8f8f8',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    marginBottom: 12,
    maxWidth: '45%',
  },
  hotSearchText: {
    fontSize: 14,
    color: '#666',
  },
});

export default SearchSuggestions;

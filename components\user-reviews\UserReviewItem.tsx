import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { UserReview } from '@/api/services/userReviewService';
import { router } from 'expo-router';
import { buildImageUrl, getProductImageUrlSync, getBrandLogoUrlSync } from '@/utils/imageUtils';

interface UserReviewItemProps {
  review: UserReview;
  onPress?: () => void;
}

const UserReviewItem: React.FC<UserReviewItemProps> = ({ review, onPress }) => {

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderStars = (rating: number) => {
    const stars = [];
    // rating 是10分制，转换为5星制
    const starRating = rating / 2; // 10分制转5分制
    const fullStars = Math.floor(starRating);
    const hasHalfStar = (starRating - fullStars) >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Ionicons key={i} name="star" size={14} color="#FFD700" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <Ionicons key={i} name="star-half" size={14} color="#FFD700" />
        );
      } else {
        stars.push(
          <Ionicons key={i} name="star-outline" size={14} color="#FFD700" />
        );
      }
    }

    return stars;
  };

  const handleItemPress = () => {
    if (onPress) {
      onPress();
    } else {
      // 导航到对应的详情页面
      if (review.type === 'product' && review.product_id) {
        router.push(`/(stack)/product-detail?id=${review.product_id}`);
      } else if (review.type === 'brand' && review.brand_id) {
        router.push(`/(stack)/brand-detail?id=${review.brand_id}`);
      }
    }
  };

  const getItemInfo = () => {
    if (review.type === 'product' && review.product) {
      // 使用产品ID构建图片URL
      const imageUrl = review.product.image_url
        ? buildImageUrl(review.product.image_url)
        : getProductImageUrlSync(review.product.id);

      return {
        name: review.product.name,
        subtitle: review.product.brand,
        image: imageUrl,
        icon: 'cube-outline' as const,
      };
    } else if (review.type === 'brand' && review.brand) {
      // 使用品牌ID构建logo URL
      const logoUrl = review.brand.logo_url
        ? buildImageUrl(review.brand.logo_url)
        : getBrandLogoUrlSync(review.brand.id);

      return {
        name: review.brand.name,
        subtitle: review.brand.desc || '品牌',
        image: logoUrl,
        icon: 'business-outline' as const,
      };
    }
    return {
      name: '未知项目',
      subtitle: '',
      image: null,
      icon: 'help-outline' as const,
    };
  };

  const itemInfo = getItemInfo();

  return (
    <TouchableOpacity
      onPress={handleItemPress}
      style={styles.container}
      activeOpacity={0.7}
    >
      {/* 点评内容 - 主要内容 */}
      <View style={styles.reviewContent}>
        <Text style={styles.reviewText} numberOfLines={6}>
          {review.review_text || '暂无评论内容'}
        </Text>
      </View>

      {/* 评分 */}
      <View style={styles.ratingContainer}>
        <View style={styles.starsContainer}>
          {renderStars(review.rating)}
        </View>
        <Text style={styles.ratingText}>
          {(review.rating / 2).toFixed(1)} 分
        </Text>
        {review.is_long_review && (
          <View style={styles.longReviewTag}>
            <Text style={styles.longReviewText}>长评</Text>
          </View>
        )}
      </View>

      {/* 关联的产品/品牌信息 */}
      <View style={styles.itemSection}>
        <View style={styles.itemHeader}>
          {/* 项目图片/图标 */}
          <View style={styles.imageContainer}>
            {itemInfo.image ? (
              <Image
                source={itemInfo.image}
                style={styles.image}
                contentFit="cover"
                placeholder={require('@/assets/images/placeholder.png')}
              />
            ) : (
              <Ionicons name={itemInfo.icon} size={20} color="#9CA3AF" />
            )}
          </View>

          {/* 项目信息 */}
          <View style={styles.itemInfo}>
            <Text style={styles.itemName} numberOfLines={1}>
              {itemInfo.name}
            </Text>
            {itemInfo.subtitle && (
              <Text style={styles.itemSubtitle} numberOfLines={1}>
                {itemInfo.subtitle}
              </Text>
            )}
          </View>

          {/* 类型标识 */}
          <View style={[
            styles.typeTag,
            review.type === 'product' ? styles.productTag : styles.brandTag
          ]}>
            <Text style={[
              styles.typeTagText,
              review.type === 'product' ? styles.productTagText : styles.brandTagText
            ]}>
              {review.type === 'product' ? '产品' : '品牌'}
            </Text>
          </View>
        </View>
      </View>

      {/* 底部信息 */}
      <View style={styles.footer}>
        <Text style={styles.dateText}>
          {formatDate(review.review_time)}
        </Text>

        <View style={styles.footerRight}>
          {/* 点赞数 */}
          <View style={styles.likeContainer}>
            <Ionicons name="heart-outline" size={14} color="#9CA3AF" />
            <Text style={styles.likeText}>{review.likes}</Text>
          </View>

          {/* 状态指示器 */}
          <View style={[
            styles.statusIndicator,
            review.review_status ? styles.approvedStatus : styles.pendingStatus
          ]} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#f1f3f4',
  },
  reviewContent: {
    marginBottom: 12,
  },
  reviewText: {
    fontSize: 16,
    color: '#1f2937',
    lineHeight: 24,
    fontWeight: '400',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  ratingText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  longReviewTag: {
    marginLeft: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    backgroundColor: '#fed7aa',
    borderRadius: 8,
  },
  longReviewText: {
    fontSize: 12,
    color: '#ea580c',
    fontWeight: '600',
  },
  itemSection: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageContainer: {
    width: 36,
    height: 36,
    borderRadius: 6,
    backgroundColor: '#fff',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 12,
    color: '#9ca3af',
  },
  typeTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  productTag: {
    backgroundColor: '#dbeafe',
  },
  brandTag: {
    backgroundColor: '#dcfce7',
  },
  typeTagText: {
    fontSize: 10,
    fontWeight: '600',
  },
  productTagText: {
    color: '#2563eb',
  },
  brandTagText: {
    color: '#16a34a',
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateText: {
    fontSize: 12,
    color: '#9ca3af',
  },
  footerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  likeText: {
    fontSize: 12,
    color: '#9ca3af',
    marginLeft: 4,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  approvedStatus: {
    backgroundColor: '#10b981',
  },
  pendingStatus: {
    backgroundColor: '#f59e0b',
  },
});

export default UserReviewItem;

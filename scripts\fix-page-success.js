#!/usr/bin/env node

/**
 * 批量修复 ResponseData.pageSuccess 调用的脚本
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/routes/brand-review-routes.ts',
  'src/routes/brand-routes.ts',
  'src/routes/i18n-routes.ts',
  'src/routes/product-review-routes.ts',
  'src/routes/product-routes.ts'
];

function fixPageSuccessCall(content) {
  // 匹配 ResponseData.pageSuccess 调用模式
  const pattern = /ResponseData\.pageSuccess\(\s*\{([^}]+)\},\s*([^,)]+)(?:,\s*(\d+))?\s*\)/g;
  
  return content.replace(pattern, (match, dataContent, message, status) => {
    // 提取 totalCount, totalPages, currentPage 从 dataContent
    const totalCountMatch = dataContent.match(/totalCount:\s*([^,\n]+)/);
    const totalPagesMatch = dataContent.match(/totalPages:\s*([^,\n]+)/);
    const currentPageMatch = dataContent.match(/currentPage:\s*([^,\n]+)/);
    
    if (totalCountMatch && totalPagesMatch && currentPageMatch) {
      // 移除这些字段从 dataContent
      let cleanedDataContent = dataContent
        .replace(/,?\s*totalCount:\s*[^,\n]+/g, '')
        .replace(/,?\s*totalPages:\s*[^,\n]+/g, '')
        .replace(/,?\s*currentPage:\s*[^,\n]+/g, '')
        .replace(/,\s*$/, '') // 移除末尾的逗号
        .trim();
      
      const totalCount = totalCountMatch[1].trim();
      const totalPages = totalPagesMatch[1].trim();
      const currentPage = currentPageMatch[1].trim();
      const statusCode = status || '200';
      
      return `ResponseData.pageSuccess({${cleanedDataContent}}, ${totalCount}, ${totalPages}, ${currentPage}, ${message}, ${statusCode})`;
    }
    
    return match; // 如果无法解析，保持原样
  });
}

function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixPageSuccessCall(content);
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ 修复了 ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  ${filePath} 无需修复`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复 ${filePath} 失败:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 开始批量修复 ResponseData.pageSuccess 调用...\n');
  
  let fixedCount = 0;
  
  for (const file of filesToFix) {
    if (fs.existsSync(file)) {
      if (fixFile(file)) {
        fixedCount++;
      }
    } else {
      console.log(`⚠️  文件不存在: ${file}`);
    }
  }
  
  console.log(`\n🎉 修复完成! 共修复了 ${fixedCount} 个文件`);
}

main();

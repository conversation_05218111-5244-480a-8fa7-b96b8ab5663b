import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { PetFoodFilters } from '@/types/pet-food-filter';

interface FilterChipsProps {
  filters: PetFoodFilters;
  onRemoveFilter: (filterKey: keyof PetFoodFilters, value?: string) => void;
  onClearAll: () => void;
}

interface FilterChip {
  key: keyof PetFoodFilters;
  label: string;
  value?: string;
}

const FilterChips: React.FC<FilterChipsProps> = ({
  filters,
  onRemoveFilter,
  onClearAll,
}) => {
  const chips: FilterChip[] = [];

  console.log('FilterChips - filters:', filters); // 调试日志

  // 搜索关键词
  if (filters.searchQuery && filters.searchQuery.trim() !== '') {
    chips.push({
      key: 'searchQuery',
      label: `搜索: ${filters.searchQuery}`,
    });
  }

  // 营养成分筛选
  if (filters.protein && (filters.protein.min > 0 || filters.protein.max < 100)) {
    chips.push({
      key: 'protein',
      label: `蛋白质: ${filters.protein.min}%-${filters.protein.max}%`,
    });
  }

  if (filters.fat && (filters.fat.min > 0 || filters.fat.max < 100)) {
    chips.push({
      key: 'fat',
      label: `脂肪: ${filters.fat.min}%-${filters.fat.max}%`,
    });
  }

  if (filters.carbs && (filters.carbs.min > 0 || filters.carbs.max < 100)) {
    chips.push({
      key: 'carbs',
      label: `碳水: ${filters.carbs.min}%-${filters.carbs.max}%`,
    });
  }

  if (filters.calories && (filters.calories.min > 0 || filters.calories.max < 1000)) {
    chips.push({
      key: 'calories',
      label: `卡路里: ${filters.calories.min}-${filters.calories.max}`,
    });
  }

  // 成分质量筛选
  if (filters.minQualityIngredients !== undefined && filters.minQualityIngredients > 0) {
    chips.push({
      key: 'minQualityIngredients',
      label: `优质成分 ≥${filters.minQualityIngredients}`,
    });
  }

  if (filters.maxQuestionableIngredients !== undefined && filters.maxQuestionableIngredients < 10) {
    chips.push({
      key: 'maxQuestionableIngredients',
      label: `可疑成分 ≤${filters.maxQuestionableIngredients}`,
    });
  }

  // 过敏原筛选
  if (Array.isArray(filters.excludeAllergens) && filters.excludeAllergens.length > 0) {
    filters.excludeAllergens.forEach(allergen => {
      if (allergen && allergen.trim() !== '') {
        chips.push({
          key: 'excludeAllergens',
          label: `排除: ${allergen}`,
          value: allergen,
        });
      }
    });
  }

  // 评价筛选
  if (filters.minRating !== undefined && filters.minRating > 0) {
    chips.push({
      key: 'minRating',
      label: `评分 ≥${filters.minRating}星`,
    });
  }

  if (filters.minReviewCount !== undefined && filters.minReviewCount > 0) {
    chips.push({
      key: 'minReviewCount',
      label: `评论 ≥${filters.minReviewCount}条`,
    });
  }

  console.log('FilterChips - generated chips:', chips); // 调试日志

  if (chips.length === 0) {
    return null;
  }

  const handleRemoveChip = (chip: FilterChip) => {
    if (chip.value) {
      // 对于数组类型的筛选，需要传递具体的值
      onRemoveFilter(chip.key, chip.value);
    } else {
      // 对于单值类型的筛选，直接重置
      onRemoveFilter(chip.key);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>筛选条件</Text>
        <TouchableOpacity onPress={onClearAll} style={styles.clearButton}>
          <Text style={styles.clearText}>清除全部</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.chipsContainer}
      >
        {chips.map((chip, index) => {
          console.log(`Rendering chip ${index}:`, chip.label); // 调试渲染过程
          return (
            <TouchableOpacity
              key={`${chip.key}-${chip.value || 'single'}-${index}`}
              style={styles.chip}
              onPress={() => handleRemoveChip(chip)}
            >
              <View style={styles.chipContent}>
                <Text style={styles.chipText}>
                  {chip.label}
                </Text>
                <Ionicons name="close" size={16} color="#666" style={styles.closeIcon} />
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  clearButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  clearText: {
    fontSize: 12,
    color: '#4d920f',
    fontWeight: '500',
  },
  chipsContainer: {
    flexDirection: 'row',
    paddingRight: 16,
  },
  chip: {
    marginRight: 8,
    backgroundColor: '#fff',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  chipContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    minHeight: 32,
  },
  chipText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
    marginRight: 6,
    textAlign: 'left',
  },
  closeIcon: {
    marginLeft: 2,
  },
});

export default FilterChips;

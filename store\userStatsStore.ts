import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import userStatsService, { UserStats, FollowResult, FollowStatus, UserListResponse } from '@/api/services/userStatsService';

// 获取用户统计信息
export const useUserStats = (userId: string) => {
  return useQuery({
    queryKey: ['user-stats', userId],
    queryFn: () => userStatsService.getUserStats(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    gcTime: 10 * 60 * 1000, // 10分钟垃圾回收
  });
};

// 获取关注状态
export const useFollowStatus = (userId: string, currentUserId?: string) => {
  return useQuery({
    queryKey: ['follow-status', currentUserId, userId],
    queryFn: () => userStatsService.getFollowStatus(userId, currentUserId!),
    enabled: !!userId && !!currentUserId && userId !== currentUserId,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });
};

// 切换关注状态
export const useToggleFollow = (currentUserId?: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => userStatsService.toggleFollow(userId, currentUserId!),

    // 乐观更新 - 在API调用前立即更新UI
    onMutate: async (userId: string) => {
      // 取消相关的查询以避免冲突
      await queryClient.cancelQueries({ queryKey: ['follow-status', currentUserId, userId] });

      // 获取当前的关注状态
      const previousFollowStatus = queryClient.getQueryData(['follow-status', currentUserId, userId]);
      const currentIsFollowing = (previousFollowStatus as any)?.isFollowing ?? false;

      // 乐观更新关注状态
      queryClient.setQueryData(['follow-status', currentUserId, userId], {
        isFollowing: !currentIsFollowing
      });

      // 返回之前的状态，以便在错误时回滚
      return { previousFollowStatus, currentIsFollowing };
    },

    onSuccess: (data: FollowResult, userId: string) => {
      console.log('Follow API success, server response:', data);

      // 确保状态与服务器响应一致
      queryClient.setQueryData(['follow-status', currentUserId, userId], {
        isFollowing: data.isFollowing
      });

      // 更新被关注用户的统计缓存
      queryClient.setQueryData(['user-stats', userId], (oldData: UserStats | undefined) => {
        if (oldData) {
          return {
            ...oldData,
            followersCount: data.followersCount,
          };
        }
        return oldData;
      });

      // 更新当前用户的关注数统计缓存
      if (currentUserId) {
        queryClient.setQueryData(['user-stats', currentUserId], (oldData: UserStats | undefined) => {
          if (oldData) {
            return {
              ...oldData,
              followingCount: data.isFollowing
                ? oldData.followingCount + 1
                : Math.max(0, oldData.followingCount - 1),
            };
          }
          return oldData;
        });
      }

      // 使相关查询失效（但不立即重新获取，避免闪烁）
      queryClient.invalidateQueries({
        queryKey: ['user-stats', userId],
        refetchType: 'none' // 不立即重新获取
      });
      queryClient.invalidateQueries({
        queryKey: ['user-stats', currentUserId],
        refetchType: 'none'
      });
    },

    onError: (error: any, userId: string, context: any) => {
      console.error('Toggle follow error:', error);

      // 回滚乐观更新
      if (context?.previousFollowStatus) {
        queryClient.setQueryData(['follow-status', currentUserId, userId], context.previousFollowStatus);
      }
    },
  });
};

// 获取粉丝列表
export const useFollowers = (userId: string, page: number = 1, limit: number = 10) => {
  return useQuery<UserListResponse>({
    queryKey: ['followers', userId, page, limit],
    queryFn: () => userStatsService.getFollowers(userId, page, limit),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
};

// 获取关注列表
export const useFollowing = (userId: string, page: number = 1, limit: number = 10) => {
  return useQuery<UserListResponse>({
    queryKey: ['following', userId, page, limit],
    queryFn: () => userStatsService.getFollowing(userId, page, limit),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
};

// 用户统计相关的hooks集合
export const useUserStatsStore = () => {
  return {
    useUserStats,
    useFollowStatus,
    useToggleFollow,
    useFollowers,
    useFollowing,
  };
};

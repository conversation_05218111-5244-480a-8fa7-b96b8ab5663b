import { BrandCardType } from '@/types/brand-types';
import { getBrandLogoUrlSync } from '@/utils/imageUtils';
import { useRouter } from 'expo-router';
import React, { useMemo, useState } from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
interface BrandCardProps {
  brand: BrandCardType;
}

const BrandCard: React.FC<BrandCardProps> = ({ brand }) => {
  const router = useRouter();
  const [imageError, setImageError] = useState(false);

  const imageUrl = useMemo(() => {
    if (!imageError && brand.brand_id) {
      return getBrandLogoUrlSync(brand.brand_id);
    }
    return '';
  }, [brand.brand_id, imageError]);

  const ratingDisplay = useMemo(() => {
    if (brand.average_rating === "暂无评分") {
      return "暂无评分";
    } else if (typeof brand.average_rating === 'number') {
      return brand.average_rating.toFixed(1);
    } else {
      return "暂无评分";
    }
  }, [brand.average_rating]);


  const handlePress = () => {
    router.push(`/brand-detail?id=${brand.brand_id}` as any);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Pressable
      style={({ pressed }) => [
        styles.card,
        pressed ? styles.cardPressed : {}
      ]}
      onPress={handlePress}
    >
      <View style={styles.contentContainer}>
        <View style={styles.topSection}>
          <View style={styles.logoContainer}>
            {imageUrl && !imageError ? (
              <Image
                source={{ uri: imageUrl }}
                style={styles.logo}
                resizeMode="contain"
                onError={handleImageError}
              />
            ) : (
              <View style={styles.placeholderLogo}>
                <Icon name="store" size={24} color="#ccc" />
              </View>
            )}
          </View>

          <View style={[
            styles.descContainer,
          ]}>
            <Text
              style={[
                styles.desc,
                { color: 'white' }
              ]}
              numberOfLines={4}
            >
              {brand.desc}
            </Text>
            <Text style={styles.brandLabel}>- 品牌简介 -</Text>
          </View>
        </View>

        <View style={styles.bottomSection}>
          <Text style={styles.name} numberOfLines={1}>
            {brand.name}
          </Text>

          <View style={styles.ratingContainer}>
            <View style={styles.ratingRow}>
              <Text style={styles.rating}>
                {ratingDisplay}
              </Text>
              {typeof brand.average_rating === 'number' &&
                <Icon name="star" size={12} color="#ff9900" />
              }
            </View>
            <Text style={styles.reviewCount}>
              {brand.review_count > 0 ? `${brand.review_count}条评价` : '暂无评价'}
            </Text>
          </View>
        </View>
      </View>

    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    overflow: 'hidden',
    position: 'relative',
    borderRadius: 12,
  },
  cardPressed: {
    opacity: 0.9,
  },
  contentContainer: {
    padding: 12,
  },
  topSection: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  logoContainer: {
    width: 100,
    height: 120,
    borderRadius: 8,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  logo: {
    width: 100,
    height: 120,
  },
  placeholderLogo: {
    width: 100,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
  },
  descContainer: {
    flex: 1,
    justifyContent: 'center',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 12,
    height: 120,
    backgroundColor: 'black'

  },
  desc: {
    fontSize: 12,
    lineHeight: 16,
  },
  brandLabel: {
    fontSize: 10,
    color: '#888',
    textAlign: 'center',
    marginTop: 8,
  },
  bottomSection: {
    gap: 2,
  },
  name: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#ff9900',
    marginRight: 4,
  },
  reviewCount: {
    fontSize: 12,
    color: '#888',
  }, 
});

export default BrandCard;

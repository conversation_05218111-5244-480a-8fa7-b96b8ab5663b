import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { PetFoodFilters, NutritionRange } from '@/types/pet-food-filter';
import FilterPanel from './FilterPanel';

interface FilterModalProps {
  visible: boolean;
  filters: PetFoodFilters;
  onClose: () => void;
  onFiltersChange: {
    updateSearchQuery: (query: string) => void;
    updateBrands: (brands: string[]) => void;
    updateProductTypes: (types: string[]) => void;
    updateNutritionRange: (
      nutrient: keyof Pick<PetFoodFilters, 'protein' | 'fat' | 'carbs' | 'calories'>,
      range: NutritionRange | undefined
    ) => void;
    updateQualityFilters: (updates: {
      minQualityIngredients?: number;
      maxQuestionableIngredients?: number;
    }) => void;
    updateAllergens: (allergens: string[]) => void;
    updateRatingFilters: (updates: {
      minRating?: number;
      minReviewCount?: number;
    }) => void;
  };
  onResetFilters: () => void;
  availableBrands?: string[];
  activeFilterCount: number;
}

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  filters,
  onClose,
  onFiltersChange,
  onResetFilters,
  availableBrands,
  activeFilterCount,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          
          <View style={styles.titleContainer}>
            <Text style={styles.title}>筛选条件</Text>
            {activeFilterCount > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{activeFilterCount}</Text>
              </View>
            )}
          </View>
          
          <TouchableOpacity onPress={onResetFilters} style={styles.resetButton}>
            <Text style={styles.resetText}>重置</Text>
          </TouchableOpacity>
        </View>

        {/* 筛选面板 */}
        <FilterPanel
          filters={filters}
          onFiltersChange={onFiltersChange}
          availableBrands={availableBrands}
        />

        {/* 底部按钮 */}
        <View style={styles.footer}>
          <TouchableOpacity style={styles.applyButton} onPress={onClose}>
            <Text style={styles.applyButtonText}>
              应用筛选 {activeFilterCount > 0 && `(${activeFilterCount})`}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  closeButton: {
    padding: 2,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  filterBadge: {
    backgroundColor: '#4d920f',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  resetButton: {
    padding: 4,
  },
  resetText: {
    fontSize: 16,
    color: '#4d920f',
    fontWeight: '500',
  },
  footer: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  applyButton: {
    backgroundColor: '#4d920f',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
});

export default FilterModal;

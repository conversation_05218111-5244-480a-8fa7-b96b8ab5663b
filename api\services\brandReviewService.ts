import apiClient from "../apiClient";

export interface BrandReviewSummary {
	_id: string;
	brand_id: string;
	average_rating: number;
	total_rating: number;
	review_count: number;
	short_review_count: number;
	long_review_count: number;
	created_at: string;
	updated_at: string;
	brand_name: string;
}

export interface BrandReview {
	_id: string;
	review_id: string;
	brand_id: string;
	user_id: string;
	user?: {
		id: string;
		username: string;
		avatar?: string;
	};
	content: string;
	rating: number;
	review_time: string;
	liked_by: string[];
	disliked_by: string[];
	parent_review_id: string | null;
	is_recommended: boolean;
	review_status: boolean;
	report_count: number;
	reply_count: number;
	images: string[];
	is_long_review: boolean;
	title?: string;
	replies?: BrandReview[];
}

export interface DailyRandomBrand {
	brand_id: string;
	name: string;
	logo: string;
	desc: string;
	average_rating: number | "暂无评分";
	review_count: number;
}

export type BrandReviewSummaryResponse = {
	brands: BrandReviewSummary[];
	totalCount: number;
	totalPages: number;
	currentPage: number;
	sortBy: string;
	sortOrder: string;
};

export type BrandReviewsResponse = {
	reviews: BrandReview[];
	totalCount: number;
	totalPages: number;
	currentPage: number;
};

export enum BrandReviewApi {
	BrandReviews = "brand-reviews",
}

// Get brand review summaries
const getBrandReviewSummaries = async (page = 1, limit = 10, sortBy = "review_count", sortOrder = "desc") => {
	try {
		const response = await apiClient.get<BrandReviewSummaryResponse>({
			url: `${BrandReviewApi.BrandReviews}/brands-with-reviews`,
			params: { page, limit, sortBy, sortOrder },
		});
		return response;
	} catch (error) {
		console.error("Brand Review Summaries API error:", error);
		throw error;
	}
};

// Get reviews for specific brand
const getReviewsByBrandId = async (brandId: string, page = 1, limit = 10, sortBy = 'latest') => {
	try {
		const response = await apiClient.get<BrandReviewsResponse>({
			url: `${BrandReviewApi.BrandReviews}/brand/${brandId}`,
			params: { 
				page, 
				limit,
				sortBy,
				includeReplies: true, 
				maxReplyDepth: 2,
				includeUserInfo: true
			},
		});
		return response;
	} catch (error) {
		console.error(`Brand Reviews API error for brand ${brandId}:`, error);
		throw error;
	}
};

// Get specific review by ID
const getReviewById = async (reviewId: string) => {
	try {
		// Log the reviewId we're trying to fetch
		console.log(`Fetching brand review with ID: ${reviewId}`);
		
		const response = await apiClient.get<BrandReview>({
			url: `${BrandReviewApi.BrandReviews}/${reviewId}`,
			params: {
				includeUserInfo: true
			}
		});
		return response;
	} catch (error) {
		console.error(`Brand Review API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Update review status (approve/reject)
const updateReviewStatus = async (reviewId: string, status: boolean) => {
	try {
		const response = await apiClient.patch<BrandReview>({
			url: `${BrandReviewApi.BrandReviews}/${reviewId}/status`,
			data: { review_status: status },
		});
		return response;
	} catch (error) {
		console.error(`Brand Review status update API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Update review content
const updateReviewContent = async (reviewId: string, data: Partial<BrandReview>) => {
	try {
		const response = await apiClient.put<BrandReview>({
			url: `${BrandReviewApi.BrandReviews}/${reviewId}`,
			data,
		});
		return response;
	} catch (error) {
		console.error(`Brand Review update API error for ID ${reviewId}:`, error);
		throw error;
	}
};

// Delete review
const deleteReview = async (reviewId: string) => {
	try {
		const response = await apiClient.delete<BrandReview>({
			url: `${BrandReviewApi.BrandReviews}/${reviewId}`,
		});
		return response;
	} catch (error) {
		console.error(`Brand Review delete API error for ID ${reviewId}:`, error);
		throw error;
	}
};

const createReview = async (reviewData: Partial<BrandReview>) => {
	try {
		const response = await apiClient.post<BrandReview>({
			url: `${BrandReviewApi.BrandReviews}`,
			data: reviewData,
		});
		return response;
	} catch (error) {
		console.error("Brand Review creation API error:", error);
		throw error;
	}
};


const getDailyRandomBrands = async (count = 9) => {
	try {
		const response = await apiClient.get<DailyRandomBrand[]>({
			url: `${BrandReviewApi.BrandReviews}/daily-random-brands`,
			params: { count },
		});
		return response;
	} catch (error) {
		console.error("Daily Random Brands API error:", error);
		throw error;
	}
};

// Get long reviews for a specific brand
const getLongReviews = async (
	brandId: string,
	page = 1,
	limit = 10,
	includeReplies = true,
	maxReplyDepth = 3,
	sortBy = 'latest'
) => {
	try {
		const response = await apiClient.get<BrandReviewsResponse>({
			url: `${BrandReviewApi.BrandReviews}/brand/${brandId}`,
			params: { page, limit, type: "long", includeReplies, maxReplyDepth, sortBy },
		});
		return response;
	} catch (error) {
		console.error(`Long Brand Reviews API error for brand ${brandId}:`, error);
		throw error;
	}
};

// Get short reviews for a specific brand
const getShortReviews = async (
	brandId: string,
	page = 1,
	limit = 10,
	includeReplies = true,
	maxReplyDepth = 3,
	sortBy = 'latest'
) => {
	try {
		const response = await apiClient.get<BrandReviewsResponse>({
			url: `${BrandReviewApi.BrandReviews}/brand/${brandId}`,
			params: { page, limit, type: "short", includeReplies, maxReplyDepth, sortBy },
		});
		return response;
	} catch (error) {
		console.error(`Short Brand Reviews API error for brand ${brandId}:`, error);
		throw error;
	}
};

// Define the BrandReviewSummary interface with the exact fields from the API response
export interface BrandReviewSummaryDetail {
  _id: string;
  brand_id: string;
  average_rating: number;
  total_rating: number;
  review_count: number;
  short_review_count: number;
  long_review_count: number;
  created_at: string;
  updated_at: string;
  __v: number;
}

// Get brand review summary for a specific brand
const getBrandReviewSummary = async (brandId: string) => {
  try {
    const response = await apiClient.get<BrandReviewSummaryDetail>({
      url: `${BrandReviewApi.BrandReviews}/summary/${brandId}`,
    });
    return response;
  } catch (error) {
    console.error(`Brand Review Summary API error for brand ${brandId}:`, error);
    throw error;
  }
};

// Define interface for top products
export interface TopBrandProduct {
  product_id: string;
  name: string;
  brand: string;
  image_url: string;
  average_rating: number;
  review_count: number;
  popularity_score: number;
}

// Get top products for a specific brand
const getTopProductsForBrand = async (brandId: string, limit = 5) => {
  try {
    const response = await apiClient.get<TopBrandProduct[]>({
      url: `${BrandReviewApi.BrandReviews}/brand/${brandId}/top-products`,
      params: { limit },
    });
    return response;
  } catch (error) {
    console.error(`Top products for brand API error for brand ${brandId}:`, error);
    throw error;
  }
};

// Define interface for brand review replies response that matches the actual API
export interface BrandReviewRepliesResponse {
  currentPage: number;
  maxReplyDepth: number;
  replies: BrandReview[];
  totalCount: number;
  totalPages: number;
}

// Get replies to a specific brand review
const getReviewReplies = async (reviewId: string, page = 1, limit = 10) => {
  try {
    const response = await apiClient.get<BrandReviewRepliesResponse>({
      url: `${BrandReviewApi.BrandReviews}/${reviewId}/replies`,
      params: {
        page,
        limit,
        includeUserInfo: true
      },
    });
    return response;
  } catch (error) {
    console.error(`Brand Review replies API error for review ${reviewId}:`, error);
    throw error;
  }
};

// Check if user has already reviewed a brand
const hasUserReviewedBrand = async (brandId: string, userId: string) => {
  try {
    const response = await apiClient.get<{ hasReviewed: boolean }>({
      url: `${BrandReviewApi.BrandReviews}/brand/${brandId}/user/${userId}/has-reviewed`,
    });
    return response;
  } catch (error) {
    console.error(`Has user reviewed brand API error for brand ${brandId}:`, error);
    throw error;
  }
};

// Get user's review for a specific brand
const getUserReviewForBrand = async (brandId: string, userId: string) => {
  try {
    const response = await apiClient.get<BrandReview | null>({
      url: `${BrandReviewApi.BrandReviews}/brand/${brandId}/user/${userId}/review`,
    });
    return response;
  } catch (error) {
    console.error(`Get user review for brand API error for brand ${brandId}:`, error);
    throw error;
  }
};

// Get rating distribution for a brand
const getRatingDistribution = async (brandId: string) => {
  try {
    const response = await apiClient.get<{
      distribution: { [key: string]: number },
      percentages: number[]
    }>({
      url: `${BrandReviewApi.BrandReviews}/brand/${brandId}/rating-distribution`,
    });
    return response;
  } catch (error) {
    console.error(`Get rating distribution API error for brand ${brandId}:`, error);
    throw error;
  }
};

// Like a brand review
const likeReview = async (reviewId: string) => {
  try {
    const response = await apiClient.post<{
      review_id: string;
      likes: number;
      dislikes: number;
      liked_by: string[];
      disliked_by: string[];
    }>({
      url: `${BrandReviewApi.BrandReviews}/${reviewId}/like`,
      data: {}, // user_id will be extracted from Authorization header by backend
    });
    return response;
  } catch (error) {
    console.error(`Brand review like API error for ID ${reviewId}:`, error);
    throw error;
  }
};

// Unlike a brand review
const unlikeReview = async (reviewId: string) => {
  try {
    const response = await apiClient.post<{
      review_id: string;
      likes: number;
      dislikes: number;
      liked_by: string[];
      disliked_by: string[];
    }>({
      url: `${BrandReviewApi.BrandReviews}/${reviewId}/unlike`,
      data: {}, // user_id will be extracted from Authorization header by backend
    });
    return response;
  } catch (error) {
    console.error(`Brand review unlike API error for ID ${reviewId}:`, error);
    throw error;
  }
};

// Dislike a brand review
const dislikeReview = async (reviewId: string) => {
  try {
    const response = await apiClient.post<{
      review_id: string;
      likes: number;
      dislikes: number;
      liked_by: string[];
      disliked_by: string[];
    }>({
      url: `${BrandReviewApi.BrandReviews}/${reviewId}/dislike`,
      data: {}, // user_id will be extracted from Authorization header by backend
    });
    return response;
  } catch (error) {
    console.error(`Brand review dislike API error for ID ${reviewId}:`, error);
    throw error;
  }
};

// Undislike a brand review
const undislikeReview = async (reviewId: string) => {
  try {
    const response = await apiClient.post<{
      review_id: string;
      likes: number;
      dislikes: number;
      liked_by: string[];
      disliked_by: string[];
    }>({
      url: `${BrandReviewApi.BrandReviews}/${reviewId}/undislike`,
      data: {}, // user_id will be extracted from Authorization header by backend
    });
    return response;
  } catch (error) {
    console.error(`Brand review undislike API error for ID ${reviewId}:`, error);
    throw error;
  }
};

// Get user reaction status for a brand review
const getUserReactionStatus = async (reviewId: string) => {
  try {
    const response = await apiClient.get<{
      liked: boolean;
      disliked: boolean;
    }>({
      url: `${BrandReviewApi.BrandReviews}/${reviewId}/reaction-status`, // Remove userId from URL, use auth header
    });
    return response;
  } catch (error) {
    console.error(`Get user reaction status API error for brand review ${reviewId}:`, error);
    throw error;
  }
};

export default {
	getBrandReviewSummaries,
	getReviewsByBrandId,
	getReviewById,
	updateReviewStatus,
	updateReviewContent,
	deleteReview,
	createReview,
	getDailyRandomBrands,
	getLongReviews,
	getShortReviews,
	getBrandReviewSummary,
	getTopProductsForBrand,
	getReviewReplies,
	hasUserReviewedBrand,
	getUserReviewForBrand,
	getRatingDistribution,
	likeReview,
	unlikeReview,
	dislikeReview,
	undislikeReview,
	getUserReactionStatus,
};

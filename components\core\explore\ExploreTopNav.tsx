import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const tabs = [
  { key: 'products', title: '产品' },
  { key: 'brands', title: '品牌' },
];

interface ExploreTopNavProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function ExploreTopNav({ activeTab, setActiveTab }: ExploreTopNavProps) {
  return (
    <View style={styles.topNavBar}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabButton,
            activeTab === tab.key && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab(tab.key)}>
          <Text
            style={[
              styles.tabButtonText,
              activeTab === tab.key && styles.activeTabButtonText,
            ]}>
            {tab.title}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  topNavBar: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 1,
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#006400',
  },
  tabButtonText: {
    fontSize: 15,
    color: '#006400',
  },
  activeTabButtonText: {
    fontWeight: 'bold',
    color: '#006400',
  },
});

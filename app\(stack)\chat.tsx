import { useLocal<PERSON>earch<PERSON>ara<PERSON>, useRouter } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MessageData } from '@/api/services/messageService';
import { useMarkAsRead, useMessages, useSendMessage } from '@/store/useMessages';
import { useUserInfo } from '@/store/userStore';
import { formatMessageTime } from '@/utils/dateUtils';

interface MessageItemProps {
  message: MessageData;
  isOwn: boolean;
  showAvatar: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, isOwn, showAvatar }) => {
  // 安全地获取消息内容
  const messageContent = message.content || '';
  const senderAvatar = message.sender?.avatar || 'https://via.placeholder.com/32x32/cccccc/ffffff?text=U';

  return (
    <SafeAreaView>
      <View style={[styles.messageContainer, isOwn ? styles.ownMessage : styles.otherMessage]}>
        {!isOwn && showAvatar && (
          <Image
            source={{ uri: senderAvatar }}
            style={styles.messageAvatar}
          />
        )}

        <View style={[
          styles.messageBubble,
          isOwn ? styles.ownBubble : styles.otherBubble,
          !isOwn && !showAvatar && styles.messageWithoutAvatar
        ]}>
          <Text style={[
            styles.messageText,
            isOwn ? styles.ownMessageText : styles.otherMessageText
          ]}>
            {messageContent}
          </Text>

          <View style={styles.messageFooter}>
            <Text style={[
              styles.messageTime,
              isOwn ? styles.ownMessageTime : styles.otherMessageTime
            ]}>
              {formatMessageTime(message.created_at)}
            </Text>

            {isOwn && (
              <Icon
                name={message.is_read ? "check-all" : "check"}
                size={14}
                color={message.is_read ? "#007AFF" : "#999"}
                style={styles.readStatus}
              />
            )}
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default function ChatScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{
    conversationId: string;
    otherUserId: string;
    otherUserName: string;
    otherUserAvatar: string;
  }>();

  const userInfo = useUserInfo();
  const [inputText, setInputText] = useState('');
  const [isInputFocused, setIsInputFocused] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const hasMarkedAsRead = useRef(false);

  const {
    data: messagesData,
    isFetching,
    hasNextPage,
    fetchNextPage,
  } = useMessages(params.conversationId || '', userInfo?.id || '');

  const sendMessageMutation = useSendMessage();
  const markAsReadMutation = useMarkAsRead();

  const messages = useMemo(() => {
    if (!messagesData?.pages) return [];

    const allMessages = messagesData.pages.flatMap(page => {
      if (!page.data || !Array.isArray(page.data.items)) return [];
      return page.data.items;
    });

    return allMessages.reverse(); // 最新消息在底部
  }, [messagesData]);

  // 标记消息为已读
  useEffect(() => {
    if (params.conversationId && userInfo?.id && !hasMarkedAsRead.current) {
      hasMarkedAsRead.current = true;
      markAsReadMutation.mutate({
        conversationId: params.conversationId,
        userId: userInfo.id
      });
    }
  }, [params.conversationId, userInfo?.id, markAsReadMutation]);

  // 重置标记状态当会话改变时
  useEffect(() => {
    hasMarkedAsRead.current = false;
  }, [params.conversationId]);

  // 键盘事件监听
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        // 键盘显示时滚动到底部
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
    };
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!inputText.trim() || sendMessageMutation.isPending || !userInfo?.id) return;

    const messageContent = inputText.trim();
    setInputText('');

    try {
      await sendMessageMutation.mutateAsync({
        senderId: userInfo.id,
        receiver_id: params.otherUserId || '',
        content: messageContent,
        message_type: 'text'
      });

      // 滚动到底部
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Send message error:', error);
      Alert.alert('发送失败', '消息发送失败，请重试');
      setInputText(messageContent); // 恢复输入内容
    }
  }, [inputText, sendMessageMutation, userInfo?.id, params.otherUserId]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetching, fetchNextPage]);

  const renderMessageItem = useCallback(({ item, index }: { item: MessageData; index: number }) => {
    // 确保消息数据完整
    if (!item || !item.message_id) {
      return null;
    }

    const isOwn = item.sender_id === userInfo?.id;
    const prevMessage = index > 0 ? messages[index - 1] : null;
    const showAvatar = !isOwn && (!prevMessage || prevMessage.sender_id !== item.sender_id);

    return (
      <MessageItem
        message={item}
        isOwn={isOwn}
        showAvatar={showAvatar}
      />
    );
  }, [userInfo?.id, messages]);

  const renderHeader = useCallback(() => {
    if (!isFetching) return null;
    return (
      <View style={styles.loadingHeader}>
        <ActivityIndicator size="small" color="#007AFF" />
      </View>
    );
  }, [isFetching]);

  // 安全地获取参数
  const otherUserName = params.otherUserName || '未知用户';
  const otherUserAvatar = params.otherUserAvatar || 'https://via.placeholder.com/32x32/cccccc/ffffff?text=U';

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 88 : 0}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Icon name="arrow-left" size={24} color="#333" />
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <Image
            source={{ uri: otherUserAvatar }}
            style={styles.headerAvatar}
          />
          <Text style={styles.headerTitle}>{otherUserName}</Text>
        </View>

        <TouchableOpacity>
          <Icon name="dots-vertical" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessageItem}
        keyExtractor={(item) => item.message_id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListHeaderComponent={renderHeader}
        showsVerticalScrollIndicator={false}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        inverted={false}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        }}
      />

      {/* Input */}
      <SafeAreaView edges={['bottom']}>
        <View style={[styles.inputContainer, isInputFocused && styles.inputContainerFocused]}>
          <View style={styles.inputWrapper}>
              <TextInput
                style={styles.textInput}
                value={inputText}
                onChangeText={setInputText}
                placeholder="输入消息..."
                placeholderTextColor="#999"
                multiline
                maxLength={1000}
                onFocus={() => {
                  setIsInputFocused(true);
                  // 输入框获得焦点时滚动到底部
                  setTimeout(() => {
                    flatListRef.current?.scrollToEnd({ animated: true });
                  }, 300);
                }}
                onBlur={() => setIsInputFocused(false)}
              />

              <TouchableOpacity
                style={[
                  styles.sendButton,
                  (!inputText.trim() || sendMessageMutation.isPending) && styles.sendButtonDisabled
                ]}
                onPress={handleSendMessage}
                disabled={!inputText.trim() || sendMessageMutation.isPending}
              >
                {sendMessageMutation.isPending ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Icon name="send" size={20} color="#fff" />
                )}
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingTop: 50,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  headerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 8,
  },
  loadingHeader: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 2,
    paddingHorizontal: 16,
  },
  ownMessage: {
    justifyContent: 'flex-end',
  },
  otherMessage: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
    alignSelf: 'flex-end',
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    marginVertical: 2,
  },
  ownBubble: {
    backgroundColor: '#007AFF',
    borderBottomRightRadius: 4,
  },
  otherBubble: {
    backgroundColor: '#fff',
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  messageWithoutAvatar: {
    marginLeft: 40,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  ownMessageText: {
    color: '#fff',
  },
  otherMessageText: {
    color: '#333',
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  messageTime: {
    fontSize: 12,
    marginRight: 4,
  },
  ownMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  otherMessageTime: {
    color: '#999',
  },
  readStatus: {
    marginLeft: 2,
  },
  inputContainer: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  inputContainerFocused: {
    borderTopColor: '#007AFF',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 8,
    backgroundColor: '#f8f9fa',
  },
  sendButton: {
    backgroundColor: '#007AFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
});

import { useTranslation } from "react-i18next";

/**
 * 用于翻译产品成分的钩子函数
 * @returns 翻译函数
 */
export const useIngredientTranslator = () => {
	const { t, i18n } = useTranslation();

	/**
	 * 翻译单个成分
	 * @param ingredient 成分英文名
	 * @returns 翻译后的成分名称
	 */
	const translateIngredient = (ingredient: string): string => {
		if (!ingredient) return "";

		// 尝试多种可能的翻译路径
		const possiblePaths = [
			`sys.menu.ingredients.${ingredient.toLowerCase()}`,
			`ingredients.${ingredient.toLowerCase()}`,
			`${ingredient.toLowerCase()}`,
		];

		let translation = ingredient;

		// 尝试所有可能的路径
		for (const path of possiblePaths) {
			const result = t(path, { defaultValue: null });
			if (result !== null) {
				translation = result;
				break;
			}
		}

		// 当在开发环境时，如果没有找到翻译，在控制台输出提示信息
		if (process.env.NODE_ENV === "development" && translation === ingredient) {
			console.log(`未找到成分翻译: ${ingredient}`);
		}

		return translation;
	};

	/**
	 * 批量翻译成分列表
	 * @param ingredients 成分英文名列表
	 * @returns 翻译后的成分名称列表
	 */
	const translateIngredients = (ingredients: string[]): string[] => {
		if (!ingredients || !Array.isArray(ingredients)) return [];
		return ingredients.map(translateIngredient);
	};

	/**
	 * 调试用函数，检查成分翻译是否正常工作
	 * @param ingredients 要测试的成分列表
	 */
	const debugIngredientTranslation = (ingredients: string[]): void => {
		if (process.env.NODE_ENV !== "development") return;

		console.group("成分翻译调试");

		// 输出当前加载的所有翻译资源
		console.log("当前i18n资源:", i18n.getDataByLanguage(i18n.language));

		for (const ingredient of ingredients) {
			// 尝试多种可能的路径
			const paths = [
				`sys.menu.ingredients.${ingredient.toLowerCase()}`,
				`ingredients.${ingredient.toLowerCase()}`,
				`${ingredient.toLowerCase()}`,
			];

			console.log(`调试 '${ingredient}' 翻译:`);

			for (const path of paths) {
				const translation = t(path, { defaultValue: null });
				console.log(`  路径 "${path}": ${translation || "未找到"}`);
			}
		}

		console.groupEnd();
	};

	return {
		translateIngredient,
		translateIngredients,
		debugIngredientTranslation,
		currentLang: i18n.language,
	};
};

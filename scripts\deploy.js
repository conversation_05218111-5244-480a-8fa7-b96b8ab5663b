#!/usr/bin/env node

/**
 * 部署脚本
 * 支持多种部署平台
 */

const { execSync } = require('child_process');
const fs = require('fs');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n执行: ${command}`, 'yellow');
  
  try {
    execSync(command, { 
      encoding: 'utf8', 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    log(`✅ ${description} 成功`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} 失败: ${error.message}`, 'red');
    return false;
  }
}

function showHelp() {
  log('\n🚀 PetFood 后端部署工具', 'cyan');
  log('==================================================', 'cyan');
  
  log('\n用法:', 'yellow');
  log('  node scripts/deploy.js [选项]', 'cyan');
  
  log('\n选项:', 'yellow');
  log('  --railway        部署到 Railway', 'cyan');
  log('  --vercel         部署到 Vercel', 'cyan');
  log('  --render         部署到 Render', 'cyan');
  log('  --docker         构建 Docker 镜像', 'cyan');
  log('  --build          仅构建项目', 'cyan');
  log('  --help           显示帮助信息', 'cyan');
  
  log('\n示例:', 'yellow');
  log('  node scripts/deploy.js --build', 'cyan');
  log('  node scripts/deploy.js --railway', 'cyan');
  log('  node scripts/deploy.js --docker', 'cyan');
}

function buildProject() {
  log('\n📦 构建项目...', 'magenta');
  return runCommand('npm run build', '项目构建');
}

function deployToRailway() {
  log('\n🚂 部署到 Railway...', 'magenta');
  
  // 检查是否安装了 Railway CLI
  try {
    execSync('railway --version', { stdio: 'ignore' });
  } catch (error) {
    log('❌ Railway CLI 未安装', 'red');
    log('请先安装: npm install -g @railway/cli', 'yellow');
    return false;
  }
  
  return runCommand('railway up', 'Railway 部署');
}

function deployToVercel() {
  log('\n▲ 部署到 Vercel...', 'magenta');
  
  // 检查是否安装了 Vercel CLI
  try {
    execSync('vercel --version', { stdio: 'ignore' });
  } catch (error) {
    log('❌ Vercel CLI 未安装', 'red');
    log('请先安装: npm install -g vercel', 'yellow');
    return false;
  }
  
  return runCommand('vercel --prod', 'Vercel 部署');
}

function buildDocker() {
  log('\n🐳 构建 Docker 镜像...', 'magenta');
  
  const imageName = 'petfood-backend';
  const tag = 'latest';
  
  return runCommand(`docker build -t ${imageName}:${tag} .`, 'Docker 镜像构建');
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help')) {
    showHelp();
    return;
  }
  
  log('\n🚀 PetFood 后端部署开始', 'cyan');
  log('==================================================', 'cyan');
  
  let success = true;
  
  // 构建项目
  if (args.includes('--build') || args.includes('--railway') || args.includes('--vercel')) {
    success = buildProject() && success;
  }
  
  // 部署选项
  if (args.includes('--railway')) {
    success = deployToRailway() && success;
  }
  
  if (args.includes('--vercel')) {
    success = deployToVercel() && success;
  }
  
  if (args.includes('--docker')) {
    success = buildDocker() && success;
  }
  
  // 总结
  log('\n' + '='.repeat(50), 'cyan');
  if (success) {
    log('🎉 部署完成！', 'green');
    log('\n📱 下一步:', 'yellow');
    log('  • 检查部署状态', 'cyan');
    log('  • 测试 API 接口', 'cyan');
    log('  • 更新前端 API 地址', 'cyan');
  } else {
    log('❌ 部署过程中出现错误', 'red');
    log('\n🔧 建议:', 'yellow');
    log('  • 检查错误日志', 'cyan');
    log('  • 修复相关问题', 'cyan');
    log('  • 重新运行部署', 'cyan');
  }
}

main();

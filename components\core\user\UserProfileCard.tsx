import React from 'react';
import {
    ActivityIndicator,
    Image,
    ImageBackground,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { UserInfo } from '@/types/entity';
import {
    calculateCardHeight,
    formatUserId,
    getAvatarUrl,
    getBackgroundImageUrl,
    getGenderIconName,
    getMockSocialStats,
    formatSocialStats,
    getUserPersonalAttributes
} from '@/utils/userUtils';
import { useUserStats, useFollowStatus, useToggleFollow } from '@/store/userStatsStore';
import { useUserInfo } from '@/store/userStore';
import { useRouter } from 'expo-router';

interface UserProfileCardProps {
    user: UserInfo;
    onFollow?: () => void;
    onMessage?: () => void;
    isLoading?: boolean;
    isFollowing?: boolean;
}

export default function UserProfileCard({
    user,
    onFollow,
    onMessage,
    isLoading,
    isFollowing = false
}: UserProfileCardProps) {
    const router = useRouter();
    const currentUser = useUserInfo();

    // 获取真实的用户统计数据
    const { data: userStats, isLoading: statsLoading } = useUserStats(user.id || '');
    const { data: followStatus } = useFollowStatus(user.id || '', currentUser.id);
    const toggleFollowMutation = useToggleFollow(currentUser.id);

    // 使用真实数据或fallback到mock数据
    const socialStats = userStats ? formatSocialStats(userStats) : getMockSocialStats(user);
    const personalAttributes = getUserPersonalAttributes(user);
    const cardHeight = calculateCardHeight(personalAttributes.desc);

    // 实际的关注状态 - 完全依赖API返回的状态
    const actualIsFollowing = followStatus?.isFollowing ?? false;

    // 处理关注按钮点击
    const handleFollowPress = async () => {
        if (!currentUser.id) {
            console.log('User not logged in, calling onFollow callback');
            // 用户未登录，调用原有的onFollow回调
            onFollow?.();
            return;
        }

        try {
            await toggleFollowMutation.mutateAsync(user.id || '');
            // 不调用onFollow回调，避免状态冲突
        } catch (error) {
            console.error('Follow error:', error);
        }
    };

    // 处理关注数点击
    const handleFollowingPress = () => {
        router.push(`/(stack)/user-list?userId=${user.id}&type=following&title=关注`);
    };

    // 处理粉丝数点击
    const handleFollowersPress = () => {
        router.push(`/(stack)/user-list?userId=${user.id}&type=followers&title=粉丝`);
    };

    return (
        <ImageBackground
            source={{ uri: getBackgroundImageUrl(user.backgroundImage) }}
            style={[styles.cardContainer, { height: cardHeight }]}
            imageStyle={styles.backgroundImage}
        >
            <View style={styles.overlay} />
            <View style={styles.overlay} />

            {/* Loading indicator */}
            {isLoading && (
                <View style={styles.loadingOverlay}>
                    <ActivityIndicator size="small" color="#fff" />
                </View>
            )}

            <View style={styles.profileSection}>
                <View style={styles.leftSection}>
                    <Image
                        source={{ uri: getAvatarUrl(user.avatar) }}
                        style={styles.avatar}
                    />

                    <View style={styles.userInfo}>
                        <Text style={styles.username}>{user.username || ''}</Text>
                        <Text style={styles.id} numberOfLines={2}>
                            {formatUserId(user.id)}
                        </Text>
                    </View>
                </View>
            </View>

            {/* Description Section */}
            {personalAttributes.desc && (
                <View style={styles.descSection}>
                    <Text
                        style={styles.descText}
                        numberOfLines={5}
                        ellipsizeMode="tail"
                    >
                        {personalAttributes.desc}
                    </Text>
                </View>
            )}

            {/* Personal Attributes Tags */}
            <View style={styles.attributesSection}>
                {personalAttributes.gender && (
                    <View style={styles.attributeTag}>
                        <Icon
                            name={getGenderIconName(personalAttributes.gender)}
                            size={12}
                            color="#fff"
                        />
                    </View>
                )}

                {personalAttributes.age && (
                    <View style={styles.attributeTag}>
                        <Text style={styles.attributeText}>{personalAttributes.age}岁</Text>
                    </View>
                )}

                {personalAttributes.location && (
                    <View style={styles.attributeTag}>
                        <Text style={styles.attributeText}>{personalAttributes.location}</Text>
                    </View>
                )}

                {personalAttributes.zodiac && (
                    <View style={styles.attributeTag}>
                        <Text style={styles.attributeText}>{personalAttributes.zodiac}</Text>
                    </View>
                )}
            </View>

            <View style={styles.socialSection}>
                <View style={styles.statsSection}>
                    <TouchableOpacity style={styles.statItem} onPress={handleFollowingPress}>
                        <Text style={styles.statNumber}>{socialStats.following}</Text>
                        <Text style={styles.statLabel}>关注</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.statItem} onPress={handleFollowersPress}>
                        <Text style={styles.statNumber}>{socialStats.followers}</Text>
                        <Text style={styles.statLabel}>粉丝</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.statItem}>
                        <Text style={styles.statNumber}>{socialStats.likes}</Text>
                        <Text style={styles.statLabel}>赞与收藏</Text>
                    </TouchableOpacity>
                </View>

                <View style={styles.linkSection}>
                    <TouchableOpacity
                        style={[
                            styles.actionButton,
                            actualIsFollowing && styles.followingButton
                        ]}
                        onPress={handleFollowPress}
                        disabled={toggleFollowMutation.isPending}
                    >
                        <Text style={[
                            styles.actionButtonText,
                            actualIsFollowing && styles.followingButtonText
                        ]}>
                            {toggleFollowMutation.isPending
                                ? '处理中...'
                                : actualIsFollowing ? '已关注' : '关注'
                            }
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.privateMsgButton} onPress={onMessage}>
                        <Icon name="message-text" size={14} color="#333" />
                    </TouchableOpacity>
                </View>
            </View>
        </ImageBackground>
    );
}

const styles = StyleSheet.create({
    cardContainer: {
        borderRadius: 0,
        overflow: 'hidden',
        minHeight: 280,
    },
    backgroundImage: {
        borderRadius: 0,
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    profileSection: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        padding: 20,
        paddingBottom: 10,
        paddingTop: 140, // 增加顶部留白给 header overlay
    },
    descSection: {
        paddingHorizontal: 20,
        paddingVertical: 8,
    },
    descText: {
        fontSize: 13,
        color: 'rgba(255, 255, 255, 0.95)',
        lineHeight: 18,
    },
    attributesSection: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        paddingHorizontal: 20,
        paddingVertical: 10,
        gap: 8,
    },
    attributeTag: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        gap: 4,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.3)',
    },
    attributeText: {
        fontSize: 11,
        color: '#fff',
        fontWeight: '500',
    },
    socialSection: {
        marginTop: 'auto',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingBottom: 20,
        justifyContent: 'space-between',
    },
    leftSection: {
        flexDirection: 'row',
        flex: 1,
    },
    avatar: {
        width: 80,
        height: 80,
        borderRadius: 30,
        borderWidth: 1,
        borderColor: '#fff',
        marginRight: 12,
    },
    userInfo: {
        flex: 1,
        justifyContent: 'center',
    },
    username: {
        fontSize: 22,
        fontWeight: 'bold',
        color: '#fff',
        marginBottom: 4,
    },
    id: {
        fontSize: 12,
        color: 'rgba(255, 255, 255, 0.9)',
        lineHeight: 16,
    },
    statsSection: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'space-around',
        marginRight: 30,
        gap: 2
    },
    linkSection: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end',
        gap: 8,
    },
    statItem: {
        alignItems: 'center',
        flex: 1,
    },
    statNumber: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#fff',
        marginBottom: 2,
    },
    statLabel: {
        fontSize: 10,
        color: 'rgba(255, 255, 255, 0.9)',
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        gap: 4,
        minWidth: 60,
        justifyContent: 'center',
    },
    followingButton: {
        backgroundColor: 'rgba(0, 100, 0, 0.8)',
    },
    privateMsgButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        gap: 4,
        minWidth: 30,
        justifyContent: 'center',
    },
    actionButtonText: {
        fontSize: 12,
        color: '#333',
        fontWeight: '600',
    },
    followingButtonText: {
        color: '#fff',
    },
    loadingOverlay: {
        position: 'absolute',
        top: 10,
        right: 10,
        zIndex: 1,
    },
});

import { useRouter } from 'expo-router';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';

import { useUnreadCount } from '@/store/useMessages';
import { useUserInfo } from '@/store/userStore';

export default function PrivateMessageButton() {
    const router = useRouter();
    const userInfo = useUserInfo();
    const { data: unreadData } = useUnreadCount(userInfo?.id || '', !!userInfo?.id);

    const handlePress = () => {
        router.push('/(stack)/messages');
    };

    const unreadCount = unreadData?.total_unread || 0;

    return (
        <TouchableOpacity
            style={styles.button}
            onPress={handlePress}
        >
            <Icon name="mail" size={23} color="black" />
            {unreadCount > 0 && (
                <View style={styles.badge}>
                    <Text style={styles.badgeText}>
                        {unreadCount > 99 ? '99+' : String(unreadCount)}
                    </Text>
                </View>
            )}
        </TouchableOpacity>
    )
}

const styles = StyleSheet.create({
    button: {
        padding: 0,
        marginTop: 5,
        position: 'relative',
    },
    badge: {
        position: 'absolute',
        top: -2,
        right: -6,
        backgroundColor: '#ff4444',
        borderRadius: 10,
        minWidth: 18,
        height: 18,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 4,
        borderWidth: 2,
        borderColor: '#fff',
    },
    badgeText: {
        color: '#fff',
        fontSize: 10,
        fontWeight: '600',
    },
});
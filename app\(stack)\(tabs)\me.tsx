import { useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import {
  StatusBar,
  StyleSheet,
  View,
} from 'react-native';

import UserSocialCard from '@/components/core/user/UserSocialCard';
import { useCurrentUserData, useUserActions, useUserInfo } from '@/store/userStore';

export default function MeScreen() {
  const router = useRouter();
  const userInfo = useUserInfo();
  const { setUserInfo } = useUserActions();
  
  const { isLoading: isLoadingUserData, data: freshUserData } = useCurrentUserData();

  //如果没有登录则直接跳出登录页面
  useEffect(() => {
    if (!userInfo.id) {
      router.push('/(auth)/login');
    }
  }, [userInfo.id, router]);

  useEffect(() => {
    if (freshUserData && freshUserData.id === userInfo.id) {
      setUserInfo(freshUserData);
    }
  }, [freshUserData, userInfo.id, setUserInfo]);

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleEditProfile = () => {
    router.push('/(stack)/edit-profile');
  };


  return (
    <View style={styles.container}>
      <UserSocialCard 
        userId={userInfo.id || ''} 
        onEditProfile={handleEditProfile}
        onLogin={handleLogin}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentSection: {
    backgroundColor: '#fff',
    marginTop: 12,
    borderRadius: 0,
    overflow: 'hidden',
  },
  settingsSection: {
    backgroundColor: '#fff',
    marginTop: 12,
    borderRadius: 0,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    marginRight: 16,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 12,
    color: '#999',
  },
  logoutSection: {
    backgroundColor: '#fff',
    marginTop: 12,
    borderRadius: 0,
    overflow: 'hidden',
  },
  logoutButton: {
    padding: 20,
    alignItems: 'center',
  },
  logoutText: {
    fontSize: 16,
    color: '#e74c3c',
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 100,
  },
});

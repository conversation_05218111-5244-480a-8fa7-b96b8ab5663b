import { searchService } from "@/api/services/searchService";
import { useInfiniteQuery } from "@tanstack/react-query";

// 搜索产品的无限滚动
export function useSearchProductsInfinite(searchQuery: string) {
  const query = useInfiniteQuery({
    queryKey: ["searchProducts", searchQuery],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        const response = await searchService.searchProducts(searchQuery, pageParam, 20);
        return {
          products: response.products || [],
          total: response.totalCount || 0,
          page: response.currentPage || pageParam,
          pages: response.totalPages || 0
        };
      } catch (error) {
        console.error("Search products fetch error:", error);
        throw error;
      }
    },
    enabled: !!searchQuery,
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const nextPage = lastPage.page + 1;
      return nextPage <= lastPage.pages ? nextPage : undefined;
    },
    staleTime: 2 * 60 * 1000,
    retry: 3,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    fetchNextPage: query.fetchNextPage,
    hasNextPage: query.hasNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    refetch: query.refetch,
  };
}

// 搜索品牌的无限滚动
export function useSearchBrandsInfinite(searchQuery: string) {
  const query = useInfiniteQuery({
    queryKey: ["searchBrands", searchQuery],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        const response = await searchService.searchBrands(searchQuery, pageParam, 20);
        return {
          brands: response.brands || [],
          total: response.totalCount || 0,
          page: response.currentPage || pageParam,
          pages: response.totalPages || 0
        };
      } catch (error) {
        console.error("Search brands fetch error:", error);
        throw error;
      }
    },
    enabled: !!searchQuery,
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const nextPage = lastPage.page + 1;
      return nextPage <= lastPage.pages ? nextPage : undefined;
    },
    staleTime: 2 * 60 * 1000,
    retry: 3,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    fetchNextPage: query.fetchNextPage,
    hasNextPage: query.hasNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    refetch: query.refetch,
  };
}

// 搜索帖子的无限滚动
export function useSearchThreadsInfinite(searchQuery: string) {
  const query = useInfiniteQuery({
    queryKey: ["searchThreads", searchQuery],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        const response = await searchService.searchThreads(searchQuery, pageParam, 20);
        return {
          threads: response.threads || [],
          total: response.totalCount || 0,
          page: response.currentPage || pageParam,
          pages: response.totalPages || 0
        };
      } catch (error) {
        console.error("Search threads fetch error:", error);
        throw error;
      }
    },
    enabled: !!searchQuery,
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const nextPage = lastPage.page + 1;
      return nextPage <= lastPage.pages ? nextPage : undefined;
    },
    staleTime: 2 * 60 * 1000,
    retry: 3,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    fetchNextPage: query.fetchNextPage,
    hasNextPage: query.hasNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    refetch: query.refetch,
  };
}
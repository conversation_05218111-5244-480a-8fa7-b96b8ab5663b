import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useFollowers, useFollowing } from '@/store/userStatsStore';
import UserListItem from '@/components/core/user/UserListItem';
import { SafeAreaView } from 'react-native-safe-area-context';
type ListType = 'followers' | 'following';

interface UserListItemData {
  follower_id?: string;
  following_id?: string;
  created_at?: string;
  user?: {
    id: string;
    username: string;
    avatar_url?: string;
    bio?: string;
  };
}

interface UserListResponse {
  items: UserListItemData[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export default function UserListScreen() {
  const router = useRouter();
  const { userId, type, title } = useLocalSearchParams<{
    userId: string;
    type: ListType;
    title: string;
  }>();

  const [page, setPage] = useState(1);
  const limit = 20;

  // 根据类型选择对应的hook
  const {
    data: followersData,
    isLoading: followersLoading,
    refetch: refetchFollowers,
    isFetching: isFetchingFollowers,
  } = useFollowers(userId || '', page, limit);

  const {
    data: followingData,
    isLoading: followingLoading,
    refetch: refetchFollowing,
    isFetching: isFetchingFollowing,
  } = useFollowing(userId || '', page, limit);

  // 根据类型选择数据
  const data: UserListResponse | undefined = type === 'followers' ? followersData : followingData;
  const isLoading = type === 'followers' ? followersLoading : followingLoading;
  const isFetching = type === 'followers' ? isFetchingFollowers : isFetchingFollowing;
  const refetch = type === 'followers' ? refetchFollowers : refetchFollowing;

  const handleRefresh = useCallback(() => {
    setPage(1);
    refetch();
  }, [refetch]);

  const handleLoadMore = useCallback(() => {
    if (data && data.totalPages > page && !isFetching) {
      setPage(prev => prev + 1);
    }
  }, [data, page, isFetching]);

  const handleUserPress = useCallback((user: any) => {
    // 确定正确的用户ID
    const targetUserId = user.user?.id || user.follower_id || user.following_id;

    if (targetUserId) {
      // 导航到用户详情页面
      router.push(`/(stack)/user-profile?userId=${targetUserId}`);
    } else {
      console.error('No valid user ID found in user data:', user);
    }
  }, [router]);

  const renderUserItem = useCallback(({ item }: { item: any }) => {
    return (
      <UserListItem
        user={item}
        onPress={() => handleUserPress(item)}
      />
    );
  }, [handleUserPress]);

  const renderFooter = useCallback(() => {
    if (!isFetching) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#007AFF" />
        <Text style={styles.footerText}>加载中...</Text>
      </View>
    );
  }, [isFetching]);

  const renderEmpty = useCallback(() => {
    if (isLoading) return null;

    return (
      <View style={styles.emptyContainer}>
        <Ionicons 
          name={type === 'followers' ? 'people-outline' : 'person-add-outline'} 
          size={64} 
          color="#ccc" 
        />
        <Text style={styles.emptyTitle}>
          {type === 'followers' ? '暂无粉丝' : '暂无关注'}
        </Text>
        <Text style={styles.emptySubtitle}>
          {type === 'followers' 
            ? '还没有人关注这个用户' 
            : '这个用户还没有关注任何人'
          }
        </Text>
      </View>
    );
  }, [isLoading, type]);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{title || (type === 'followers' ? '粉丝' : '关注')}</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {title || (type === 'followers' ? '粉丝' : '关注')}
          {data && ` (${data.total})`}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {/* 用户列表 */}
      <FlatList
        data={data?.items || []}
        renderItem={renderUserItem}
        keyExtractor={(item, index) => `${item.follower_id || item.following_id}-${index}`}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isFetching && page === 1}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  listContainer: {
    flexGrow: 1,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  footerText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

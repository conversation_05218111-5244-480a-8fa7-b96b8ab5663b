import apiClient from '../apiClient';
import { HistoryItem, HistoryResponse, CreateHistoryRequest, HistoryStats } from '@/types/history';

const HistoryApi = {
  History: 'history',
} as const;

// 获取用户浏览历史
const getUserHistory = async (userId: string, page = 1, limit = 20, itemType?: string) => {
  try {
    const params: any = { page, limit };
    if (itemType && itemType !== 'all') {
      params.item_type = itemType;
    }

    const response = await apiClient.get<HistoryResponse>({
      url: HistoryApi.History,
      params,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error('Get user history API error:', error);
    throw error;
  }
};

// 添加浏览历史记录
const addHistoryItem = async (userId: string, data: CreateHistoryRequest) => {
  try {
    const response = await apiClient.post<HistoryItem>({
      url: HistoryApi.History,
      data,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error('Add history item API error:', error);
    throw error;
  }
};

// 删除单个历史记录
const deleteHistoryItem = async (userId: string, historyId: string) => {
  try {
    const response = await apiClient.delete<{ success: boolean }>({
      url: `${HistoryApi.History}/${historyId}`,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error('Delete history item API error:', error);
    throw error;
  }
};

// 清空所有历史记录
const clearAllHistory = async (userId: string) => {
  try {
    const response = await apiClient.delete<{ success: boolean }>({
      url: `${HistoryApi.History}/clear`,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error('Clear all history API error:', error);
    throw error;
  }
};

// 获取历史统计信息
const getHistoryStats = async (userId: string) => {
  try {
    const response = await apiClient.get<HistoryStats>({
      url: `${HistoryApi.History}/stats`,
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error('Get history stats API error:', error);
    throw error;
  }
};

// 批量删除历史记录
const deleteMultipleHistoryItems = async (userId: string, historyIds: string[]) => {
  try {
    const response = await apiClient.delete<{ success: boolean; deletedCount: number }>({
      url: `${HistoryApi.History}/batch`,
      data: { historyIds },
      headers: { 'user-id': userId },
    });
    return response;
  } catch (error) {
    console.error('Delete multiple history items API error:', error);
    throw error;
  }
};

export default {
  getUserHistory,
  addHistoryItem,
  deleteHistoryItem,
  clearAllHistory,
  getHistoryStats,
  deleteMultipleHistoryItems,
};

export type { HistoryItem, HistoryResponse, CreateHistoryRequest, HistoryStats };

import apiClient from "../apiClient";

export interface ConversationData {
  conversation_id: string;
  participants: string[];
  last_message_id?: string;
  last_message_content?: string;
  last_message_time?: string;
  last_message_sender_id?: string;
  unread_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  other_user: {
    id: string;
    username: string;
    avatar?: string;
  };
}

export interface MessageData {
  message_id: string;
  conversation_id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  message_type: 'text' | 'image' | 'system';
  images?: string[];
  is_read: boolean;
  read_at?: string;
  is_deleted: boolean;
  deleted_at?: string;
  reply_to_message_id?: string;
  created_at: string;
  updated_at: string;
  sender: {
    id: string;
    username: string;
    avatar?: string;
  };
}

export interface CreateMessageRequest {
  senderId: string;
  receiver_id: string;
  content: string;
  message_type?: 'text' | 'image';
  images?: string[];
  reply_to_message_id?: string;
}

export interface ConversationListResponse {
  items: ConversationData[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export interface MessageListResponse {
  items: MessageData[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  conversation: any;
}

export interface UnreadCountResponse {
  total_unread: number;
  conversations: {
    conversation_id: string;
    unread_count: number;
  }[];
}

export interface CreateConversationResponse {
  conversation_id: string;
  participants: string[];
  created_at: string;
  is_new: boolean;
}

export enum MessageApi {
  Messages = "messages",
}

// 获取用户的会话列表
const getConversations = async (userId: string, page: number = 1, limit: number = 20) => {
  try {
    const response = await apiClient.get<ConversationListResponse>({
      url: `${MessageApi.Messages}/conversations`,
      params: { userId, page, limit }
    });
    return response;
  } catch (error) {
    console.error("Conversations API error:", error);
    throw error;
  }
};

// 获取会话中的消息列表
const getMessages = async (conversationId: string, userId: string, page: number = 1, limit: number = 50) => {
  try {
    const response = await apiClient.get<MessageListResponse>({
      url: `${MessageApi.Messages}/conversations/${conversationId}/messages`,
      params: { userId, page, limit }
    });
    return response;
  } catch (error) {
    console.error(`Messages API error for conversation ${conversationId}:`, error);
    throw error;
  }
};

// 发送消息
const sendMessage = async (messageRequest: CreateMessageRequest) => {
  try {
    const response = await apiClient.post<MessageData>({
      url: `${MessageApi.Messages}/send`,
      data: messageRequest
    });
    return response;
  } catch (error) {
    console.error("Send message API error:", error);
    throw error;
  }
};

// 标记会话为已读
const markConversationAsRead = async (conversationId: string, userId: string) => {
  try {
    const response = await apiClient.put<{ success: boolean }>({
      url: `${MessageApi.Messages}/conversations/${conversationId}/read`,
      data: { userId }
    });
    return response;
  } catch (error) {
    console.error(`Mark conversation as read API error for ${conversationId}:`, error);
    throw error;
  }
};

// 获取未读消息统计
const getUnreadCount = async (userId: string) => {
  try {
    const response = await apiClient.get<UnreadCountResponse>({
      url: `${MessageApi.Messages}/unread-count`,
      params: { userId }
    });
    return response;
  } catch (error) {
    console.error("Unread count API error:", error);
    throw error;
  }
};

// 删除消息
const deleteMessage = async (messageId: string, userId: string) => {
  try {
    const response = await apiClient.delete<{ success: boolean }>({
      url: `${MessageApi.Messages}/messages/${messageId}`,
      headers: { 
        'Content-Type': 'application/json',
        'X-User-ID': userId // 通过 header 传递用户 ID
      }
    });
    return response;
  } catch (error) {
    console.error(`Delete message API error for ${messageId}:`, error);
    throw error;
  }
};

// 搜索消息
const searchMessages = async (userId: string, searchTerm: string, page: number = 1, limit: number = 20) => {
  try {
    const response = await apiClient.get<MessageData[]>({
      url: `${MessageApi.Messages}/search`,
      params: { userId, q: searchTerm, page, limit }
    });
    return response;
  } catch (error) {
    console.error("Search messages API error:", error);
    throw error;
  }
};

// 获取或创建会话
const getOrCreateConversation = async (userId1: string, userId2: string) => {
  try {
    console.log('Creating conversation with APIClient');
    console.log('Request body:', { userId1, userId2 });
    
    const result = await apiClient.post<CreateConversationResponse>({
      url: `${MessageApi.Messages}/conversations`,
      data: { userId1, userId2 }
    });

    console.log('API Response:', result);
    return result;
  } catch (error) {
    console.error('Network error in getOrCreateConversation:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw new Error('请求超时，请检查网络连接');
      }
      if (error.message.includes('Network')) {
        throw new Error('网络连接失败，请检查网络设置和服务器状态');
      }
    }
    throw error;
  }
};

export const messageService = {
  getConversations,
  getMessages,
  sendMessage,
  markConversationAsRead,
  getUnreadCount,
  deleteMessage,
  searchMessages,
  getOrCreateConversation,
};

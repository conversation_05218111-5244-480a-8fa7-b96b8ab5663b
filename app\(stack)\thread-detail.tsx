import type { CreateThreadRequest, Thread } from '@/api/services/threadsService';
import ReplyCard from '@/components/threads/ReplyCard';
import ReplyModal from '@/components/threads/ReplyModal';
import ThreadOptionsModal from '@/components/threads/ThreadOptionsModal';
import ThreadDetailContent from '@/components/threads/ThreadDetailContent';
import { useThreads } from '@/store/threadsStore';
import { useUserInfo } from '@/store/userStore';
import { getAvatarUrl } from '@/utils/userUtils';
import { recordThreadHistory } from '@/utils/historyUtils';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

type DisplayItem = {
  id: string;
  isReply: boolean;
  thread: Thread;
  hasReplies?: boolean;
  depth: number; 
};

export default function ThreadDetailScreen() {
  const router = useRouter();
  const { threadId, commentId } = useLocalSearchParams<{
    threadId: string;
    commentId?: string;
  }>();
  const userInfo = useUserInfo();
  const flatListRef = useRef<FlatList>(null);

  const [replyingTo, setReplyingTo] = useState<Thread | null>(null);
  const [showOptionsModal, setShowOptionsModal] = useState(false);

  const {
    useInfiniteThreadComments,
    createThread,
    updateThread,
    deleteThread,
    toggleLike,
    toggleBookmark,
    isCreating,
    isLiking,
    isBookmarking
  } = useThreads();

  // 使用无限查询获取帖子和评论
  const {
    data: threadCommentsData,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteThreadComments(threadId || '');

  // 从无限查询数据中提取主帖和所有评论
  // pages用于存储已加载的页面数据
  /**
   * // 第一次加载（第1页）
    pages: [
      { thread: {主帖}, comments: [评论1-10], total: 25, page: 1 }
    ]

    // 用户滑动，加载第2页后
    pages: [
      { thread: {主帖}, comments: [评论1-10], total: 25, page: 1 },
      { thread: undefined, comments: [评论11-20], total: 25, page: 2 }
    ]

    // 继续滑动，加载第3页后
    pages: [
      { thread: {主帖}, comments: [评论1-10], total: 25, page: 1 },
      { thread: undefined, comments: [评论11-20], total: 25, page: 2 },
      { thread: undefined, comments: [评论21-25], total: 25, page: 3 }
    ]
   */
  const threadDetail = React.useMemo(() => {
    if (!threadCommentsData?.pages?.length) return null;
    
    const firstPage = threadCommentsData.pages[0];
    // pages.flatMa 结果：[评论1, 评论2, ..., 评论25] (来自3个页面)
    const allComments = threadCommentsData.pages.flatMap(page => page.comments);
    
    return {
      thread: firstPage.thread!,
      comments: {
        items: allComments,
        total: firstPage.total,
        page: threadCommentsData.pages.length,
        limit: firstPage.limit,
      }
    };
  }, [threadCommentsData]);

  const allComments = threadDetail?.comments.items || [];

  const displayItems = React.useMemo(() => {
    if (!allComments.length) {
      return [];
    }
    
    const items: DisplayItem[] = [];
    
    // 递归函数来展开嵌套评论
    const flattenComments = (comments: Thread[], depth: number = 0) => {
      comments.forEach(comment => {
        items.push({ 
          id: comment.thread_id, 
          isReply: depth > 0, 
          thread: comment, 
          depth: depth 
        });
        
        // 递归处理嵌套回复
        if (comment.replay_tread && comment.replay_tread.length > 0) {
          flattenComments(comment.replay_tread, depth + 1);
        }
      });
    };

    // 处理顶级评论和它们的嵌套回复
    flattenComments(allComments);

    return items;
  }, [allComments]);

  // 滚动到指定评论的逻辑
  useEffect(() => {
    if (commentId && displayItems.length > 0 && !isLoading) {
      // 查找目标评论的索引
      const targetIndex = displayItems.findIndex(item => item.thread.thread_id === commentId);

      if (targetIndex !== -1) {
        // 延迟滚动，确保列表已经渲染完成
        setTimeout(() => {
          flatListRef.current?.scrollToIndex({
            index: targetIndex,
            animated: true,
            viewPosition: 0.2, // 将目标项显示在屏幕的20%位置
          });
        }, 500);
      }
    }
  }, [commentId, displayItems, isLoading]);

  // 记录浏览历史
  useEffect(() => {
    if (userInfo?.id && threadDetail?.thread && !isLoading) {
      // 延迟记录，确保页面已经完全加载
      const timer = setTimeout(() => {
        recordThreadHistory(
          userInfo.id!,
          threadDetail.thread.thread_id,
          threadDetail.thread.title,
          threadDetail.thread.content,
          threadDetail.thread.images?.[0]
        );
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [userInfo?.id, threadDetail?.thread, isLoading]);

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    try {
      await refetch();
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  }, [refetch]);

  // 加载更多评论
  const loadMoreComments = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, isLoading, fetchNextPage]);

  // 发送回复
  const handleSubmitReply = useCallback(async (data: CreateThreadRequest) => {
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录');
      return;
    }

    try {
      createThread({ data, userId: userInfo.id });
      
      // 刷新数据
      setTimeout(() => {
        refetch();
      }, 1000);
      
    } catch (error: any) {
      Alert.alert('错误', error.message || '回复失败');
      throw error; // 重新抛出错误让ReplyModal处理
    }
  }, [userInfo.id, createThread, refetch]);

  // 点赞处理
  const handleLike = useCallback(async (threadId: string) => {
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录');
      return;
    }
    try {
      toggleLike({ threadId, userId: userInfo.id });
    } catch (error: any) {
      Alert.alert('错误', error.message || '点赞失败');
    }
  }, [toggleLike, userInfo.id]);

  // 收藏处理
  const handleBookmark = useCallback(async (threadId: string) => {
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录');
      return;
    }
    try {
      toggleBookmark({ threadId, userId: userInfo.id });
    } catch (error: any) {
      Alert.alert('错误', error.message || '收藏失败');
    }
  }, [toggleBookmark, userInfo.id]);

  // 回复处理 - 显示模态框
  const handleReply = useCallback((thread: Thread) => {
    setReplyingTo(thread);
  }, []);

  // 点击底部输入区域，弹出回复模态框
  const handleOpenReplyModal = useCallback(() => {
    if (threadDetail?.thread) {
      setReplyingTo(threadDetail.thread);
    }
  }, [threadDetail?.thread]);

  // 关闭回复模态框
  const handleCloseReplyModal = useCallback(() => {
    setReplyingTo(null);
  }, []);

  // 关注处理
  const handleFollow = useCallback(() => {
    if (!userInfo.id) {
      Alert.alert('提示', '请先登录');
      return;
    }
    Alert.alert('提示', '关注功能开发中...');
  }, [userInfo.id]);

  // 更多选项处理
  const handleMore = useCallback(() => {
    setShowOptionsModal(true);
  }, []);

  // 关闭更多选项模态框
  const handleCloseOptionsModal = useCallback(() => {
    setShowOptionsModal(false);
  }, []);

  // 编辑帖子
  const handleEditThread = useCallback(() => {
    if (threadDetail?.thread) {
      router.push(`/(stack)/edit-thread?threadId=${threadDetail.thread.thread_id}`);
    }
  }, [threadDetail?.thread, router]);

  // 分享帖子
  const handleShareThread = useCallback(() => {
    Alert.alert('提示', '分享功能开发中...');
  }, []);

  // 举报帖子
  const handleReportThread = useCallback(() => {
    Alert.alert('提示', '举报功能开发中...');
  }, []);

  // 删除帖子
  const handleDeleteThread = useCallback(() => {
    if (!threadDetail?.thread) return;

    Alert.alert(
      '确认删除',
      '确定要删除这个帖子吗？删除后无法恢复。',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              // 立即返回上一页，避免继续请求已删除的帖子
              router.back();

              // 执行删除操作
              deleteThread({
                threadId: threadDetail.thread.thread_id,
                userId: userInfo.id!
              });

              // 延迟显示成功提示，避免阻塞导航
              setTimeout(() => {
                Alert.alert('成功', '帖子已删除');
              }, 500);
            } catch (error: any) {
              // 如果删除失败，重新导航回帖子详情页
              router.push(`/(stack)/thread-detail?threadId=${threadDetail.thread.thread_id}`);
              Alert.alert('错误', error.message || '删除失败，请重试');
            }
          },
        },
      ]
    );
  }, [threadDetail?.thread, deleteThread, userInfo.id, router]);

  const handleAvatarPress = useCallback((userId: string) => {
    router.push(`/(stack)/user-profile?userId=${userId}`);
  }, [router]);


  // 渲染回复项 - 更新以支持层级样式和动态头像大小
  const renderReply = useCallback(({ item }: { item: DisplayItem }) => {
    const marginLeft = item.depth * 40; // 每层级缩进20px
    const showConnectingLine = item.depth > 0;
    
    // 根据深度调整连接线位置，对应头像中心
    const getAvatarSize = (depth: number) => {
      switch (depth) {
        case 0: return 36;
        case 1: return 32;
        case 2: return 28;
        default: return 24;
      }
    };
    
    const avatarSize = getAvatarSize(item.depth);
    const lineOffset = -(15 + (avatarSize / 2)); // 连接线位置对应头像中心
    
    return (
      <View style={[styles.replyContainer, { marginLeft }]}>
        {showConnectingLine && (
          <View style={[
            { left: lineOffset }
          ]} />
        )}
        <ReplyCard
          thread={item.thread}
          onLike={() => handleLike(item.thread.thread_id)}
          onComment={() => handleReply(item.thread)}
          onAvatarPress={handleAvatarPress}
          isLiking={isLiking}
          depth={item.depth}
        />
      </View>
    );
  }, [handleLike, handleReply, handleAvatarPress, isLiking]);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerUsername}>加载中...</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !threadDetail) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerUsername}>加载失败</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="warning-outline" size={64} color="#FF6B6B" />
          <Text style={styles.errorText}>加载失败</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryText}>重试</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const isLiked = threadDetail?.thread.liked_by.includes(userInfo.id || '') || false;
  const isBookmarked = threadDetail?.thread.bookmarked_by.includes(userInfo.id || '') || false;

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        {/* 用户信息 */}
        <TouchableOpacity
          style={styles.userInfoHeader}
          onPress={() => router.push(`/(stack)/user-profile?userId=${threadDetail.thread.user.id}`)}
          activeOpacity={0.7}
        >
          <View style={styles.avatarContainer}>
            <Image
              source={{ uri: getAvatarUrl(threadDetail.thread.user.avatar) }}
              style={styles.headerAvatar}
            />
          </View>
          <Text style={styles.headerUsername}>{threadDetail.thread.user.username}</Text>
        </TouchableOpacity>

        {/* 右侧按钮 */}
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.followButton} onPress={handleFollow}>
            <Text style={styles.followButtonText}>关注</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.shareButton} onPress={handleMore}>
            <Ionicons name="ellipsis-horizontal" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </View>

      {/* 帖子和回复列表 */}
      <FlatList
        ref={flatListRef}
        data={displayItems}
        renderItem={renderReply}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={
          <View>
            {/* 主帖 */}
            {threadDetail?.thread && (
              <ThreadDetailContent
                thread={threadDetail.thread}
              />
            )}
            
            {/* 评论总数 */}
            <View style={styles.commentHeader}>
              <Text style={styles.commentCount}>共 {allComments.length} 条回复</Text>
              <TouchableOpacity style={styles.commentSortButton}>
                <Ionicons name="chevron-down" size={16} color="#666" />
              </TouchableOpacity>
            </View>
          </View>
        }
        ListFooterComponent={() => {
          if (isFetchingNextPage) {
            return (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size="small" color="#007AFF" />
                <Text style={styles.loadingMoreText}>加载更多评论...</Text>
              </View>
            );
          }
          if (!hasNextPage && allComments.length > 0) {
            return (
              <View style={styles.endOfListContainer}>
                <Text style={styles.endOfListText}>没有更多评论了</Text>
              </View>
            );
          }
          return null;
        }}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
            tintColor="#007AFF"
          />
        }
        onEndReached={loadMoreComments}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />

      {/* 底部回复区域 */}
      <View style={styles.bottomContainer}>
        {/* 回复输入框 */}
        <TouchableOpacity 
          style={styles.replyInputFull} 
          onPress={handleOpenReplyModal}
        >
          <Text style={styles.replyPlaceholder}>说点什么...</Text>
          <Ionicons name="send-outline" size={20} color="#666" />
        </TouchableOpacity>

        {/* 互动按钮 */}
        <View style={styles.bottomActionsContainer}>
          <TouchableOpacity 
            style={styles.bottomActionButton} 
            onPress={() => handleLike(threadDetail?.thread.thread_id || '')}
            disabled={isLiking}
          >
            <Ionicons 
              name={isLiked ? "heart" : "heart-outline"} 
              size={22} 
              color={isLiked ? "#ff4757" : "#666"} 
            />
            <Text style={[styles.bottomActionText, isLiked && styles.likedText]}>
              {threadDetail?.thread.like_count || 0}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.bottomActionButton} 
            onPress={handleOpenReplyModal}
          >
            <Ionicons name="chatbubble-outline" size={22} color="#666" />
            <Text style={styles.bottomActionText}>
              {allComments.length}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.bottomActionButton} 
            onPress={() => handleBookmark(threadDetail?.thread.thread_id || '')}
            disabled={isBookmarking}
          >
            <Ionicons 
              name={isBookmarked ? "bookmark" : "bookmark-outline"} 
              size={22} 
              color={isBookmarked ? "#ffa502" : "#666"} 
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* 回复模态框 */}
      <ReplyModal
        visible={!!replyingTo}
        onClose={handleCloseReplyModal}
        onSubmit={handleSubmitReply}
        replyingTo={replyingTo}
        isLoading={isCreating}
      />

      {/* 更多选项模态框 */}
      <ThreadOptionsModal
        visible={showOptionsModal}
        onClose={handleCloseOptionsModal}
        thread={threadDetail?.thread || null}
        currentUserId={userInfo.id || ''}
        onEdit={handleEditThread}
        onShare={handleShareThread}
        onReport={handleReportThread}
        onDelete={handleDeleteThread}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  userInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 12,
  },
  avatarContainer: {
    marginRight: 8,
  },
  headerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  defaultHeaderAvatar: {
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  headerUsername: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  followButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  followButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  shareButton: {
    padding: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: '#6C757D',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#495057',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  commentCount: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  commentSortButton: {
    padding: 4,
  },
  bottomContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  bottomActionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  bottomActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  bottomActionText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  likedText: {
    color: '#ff4757',
  },
  replyInputFull: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 40,
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: 20,
    paddingHorizontal: 16,
  },
  replyPlaceholder: {
    fontSize: 14,
    color: '#6C757D',
  },
  replyContainer: {
    position: 'relative',
  },
  loadingMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  loadingMoreText: {
    fontSize: 14,
    color: '#666',
  },
  endOfListContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  endOfListText: {
    fontSize: 14,
    color: '#999',
  },
});


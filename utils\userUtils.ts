import { buildImageUrl } from '@/utils/imageUtils';

/**
 * Calculate age from birthday string
 * @param birthday Birthday in string format (YYYY-MM-DD)
 * @returns Age as string or empty string if invalid
 */
export const calculateAge = (birthday: string | undefined): string => {
    if (!birthday) return '';
    const birthDate = new Date(birthday);
    if (isNaN(birthDate.getTime())) return '';
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    return age > 0 ? age.toString() : '';
};

/**
 * Get zodiac sign from birthday string
 * @param birthday Birthday in string format (YYYY-MM-DD)
 * @returns Zodiac sign in Chinese or empty string if invalid
 */
export const getZodiacSign = (birthday: string | undefined): string => {
    if (!birthday) return '';
    const d = new Date(birthday);
    if (isNaN(d.getTime())) return '';
    const day = d.getDate();
    const month = d.getMonth() + 1;
    const signs = ["摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "摩羯座"];
    const signDates = [20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 23, 22];
    let index = month - 1;
    if (day < signDates[index]) {
        index = (index + 11) % 12;
    }
    return signs[index];
};

/**
 * Calculate dynamic card height based on description content
 * @param description User's bio/description text
 * @param baseHeight Base height of the card
 * @param maxLines Maximum number of lines for description
 * @param lineHeight Height per line
 * @param padding Additional padding for description section
 * @returns Calculated height
 */
export const calculateCardHeight = (
    description?: string,
    baseHeight: number = 320,
    maxLines: number = 5,
    lineHeight: number = 18,
    padding: number = 16
): number => {
    if (!description) return baseHeight;

    const lines = description.split('\n').length;
    const actualLines = Math.min(lines, maxLines);
    const descHeight = actualLines * lineHeight + padding;

    return baseHeight + descHeight;
};

/**
 * Get user's personal attributes from user info
 * @param userInfo User information object
 * @returns Formatted personal attributes
 */
export const getUserPersonalAttributes = (userInfo: any) => {
    return {
        gender: userInfo.gender, // 'male', 'female', 'other'
        location: userInfo.location,
        zodiac: getZodiacSign(userInfo.birthday),
        age: calculateAge(userInfo.birthday),
        desc: userInfo.bio
    };
};

/**
 * Get gender icon name for react-native-vector-icons
 * @param gender Gender string ('male', 'female', 'other')
 * @returns Icon name for MaterialCommunityIcons
 */
export const getGenderIconName = (gender: string): string => {
    switch (gender) {
        case 'female':
            return 'gender-female';
        case 'male':
            return 'gender-male';
        default:
            return 'gender-transgender';
    }
};

/**
 * Format user ID for display
 * @param id User ID string
 * @param prefix Prefix text (default: 'ID:')
 * @returns Formatted ID string
 */
export const formatUserId = (id: string | undefined, prefix: string = 'ID:'): string => {
    if (!id) return '';
    return `${prefix}${id}`;
};

/**
 * Get default avatar URL if user avatar is not available
 * @param avatar User's avatar URL
 * @param defaultAvatar Default avatar URL
 * @returns Avatar URL to use
 */
export const getAvatarUrl = (
    avatar?: string,
    defaultAvatar: string = 'https://pic.nximg.cn/file/20220910/30836555_215951293107_2.jpg'
): string => {
    if (!avatar) return defaultAvatar;
    // 如果是相对路径，使用buildImageUrl构建完整URL
    if (!avatar.startsWith('http')) {
        return buildImageUrl(avatar);
    }
    return avatar;
};

/**
 * Get default background image URL
 * @param backgroundImage User's background image URL
 * @param defaultBackground Default background image URL
 * @returns Background image URL to use
 */
export const getBackgroundImageUrl = (
    backgroundImage?: string,
    defaultBackground: string = 'https://pic4.zhimg.com/v2-2dbd8f296728cd64ac501fe2705e7ebf_r.jpg?source=1940ef5c'
): string => {
    if (!backgroundImage) return defaultBackground;
    // 如果是相对路径，使用buildImageUrl构建完整URL
    if (!backgroundImage.startsWith('http')) {
        return buildImageUrl(backgroundImage);
    }
    return backgroundImage;
};

/**
 * Mock social stats - in real app, this would come from API
 * @param userInfo User information object
 * @returns Social statistics object
 */
export const getMockSocialStats = (userInfo?: any) => {
    return {
        following: 128,
        followers: 256,
        likes: 1420,
        collections: 89
    };
};

/**
 * Format social stats from API data
 * @param stats User statistics from API
 * @returns Formatted social statistics object
 */
export const formatSocialStats = (stats?: {
    followersCount?: number;
    followingCount?: number;
    likesReceivedCount?: number;
    bookmarksReceivedCount?: number;
}) => {
    if (!stats) {
        return {
            following: 0,
            followers: 0,
            likes: 0,
        };
    }

    return {
        following: stats.followingCount || 0,
        followers: stats.followersCount || 0,
        likes: (stats.likesReceivedCount || 0) + (stats.bookmarksReceivedCount || 0),
    };
};

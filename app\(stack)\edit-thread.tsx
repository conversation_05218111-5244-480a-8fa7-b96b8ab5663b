import type { Thread, UpdateThreadRequest } from '@/api/services/threadsService';
import { useThreads } from '@/store/threadsStore';
import { useUserInfo } from '@/store/userStore';
import { uploadThreadImage } from '@/utils/fileSystemUpload';
import { buildImageUrl } from '@/utils/imageUtils';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ActivityIndicator,
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

export default function EditThreadScreen() {
  const router = useRouter();
  const { threadId } = useLocalSearchParams<{ threadId: string }>();
  const userInfo = useUserInfo();
  const { useInfiniteThreadComments, updateThread, isUpdating } = useThreads();

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [uploadingImages, setUploadingImages] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 获取帖子详情
  const {
    data: threadCommentsData,
    isLoading: isLoadingThread,
  } = useInfiniteThreadComments(threadId || '');

  const threadDetail = threadCommentsData?.pages[0];

  useEffect(() => {
    if (threadDetail?.thread && !isLoadingThread) {
      const thread = threadDetail.thread;
      
      // 检查是否是帖子作者
      if (thread.user_id !== userInfo.id) {
        Alert.alert('错误', '您没有权限编辑此帖子', [
          { text: '确定', onPress: () => router.back() }
        ]);
        return;
      }

      // 初始化表单数据
      setTitle(thread.title || '');
      setContent(thread.content || '');
      setImages(thread.images || []);
      setIsLoading(false);
    }
  }, [threadDetail, isLoadingThread, userInfo.id, router]);

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('提示', '请输入帖子标题');
      return;
    }

    if (!content.trim()) {
      Alert.alert('提示', '请输入帖子内容');
      return;
    }

    if (title.length > 100) {
      Alert.alert('提示', '帖子标题不能超过100字符');
      return;
    }

    if (content.length > 1000) {
      Alert.alert('提示', '帖子内容不能超过1000字符');
      return;
    }

    try {
      await updateThread({
        threadId: threadId!,
        data: {
          title: title.trim(),
          content: content.trim(),
          images: images.length > 0 ? images : undefined,
        },
        userId: userInfo.id!
      });

      // 立即返回，让用户看到更新后的内容
      router.back();

      // 延迟显示成功提示，避免阻塞导航
      setTimeout(() => {
        Alert.alert('成功', '帖子更新成功！');
      }, 100);
    } catch (error: any) {
      Alert.alert('错误', error.message || '更新失败，请重试');
    }
  };

  const uploadImage = async (asset: ImagePicker.ImagePickerAsset) => {
    try {
      const result = await uploadThreadImage(asset);
      // 只返回相对路径，不构建完整URL
      // 这样存储到数据库的就是相对路径，避免IP变化导致图片失效
      return result.imageUrl;
    } catch (error) {
      console.error('Upload image error:', error);
      throw error;
    }
  };

  const pickImage = async () => {
    if (images.length >= 9) {
      Alert.alert('提示', '最多只能选择9张图片');
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const currentIndex = images.length;
        setUploadingImages(prev => [...prev, true]);
        setImages(prev => [...prev, asset.uri]);

        try {
          const serverImageUrl = await uploadImage(asset);
          setImages(prev => prev.map((img, index) =>
            index === currentIndex ? serverImageUrl : img
          ));
          setUploadingImages(prev => prev.map((uploading, index) =>
            index === currentIndex ? false : uploading
          ));
        } catch (error) {
          setImages(prev => prev.filter((_, index) => index !== currentIndex));
          setUploadingImages(prev => prev.filter((_, index) => index !== currentIndex));
          Alert.alert('错误', '图片上传失败，请重试');
        }
      }
    } catch (error) {
      Alert.alert('错误', '选择图片失败，请重试');
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setUploadingImages(prev => prev.filter((_, i) => i !== index));
  };

  if (isLoading || isLoadingThread) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.cancelText}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>编辑帖子</Text>
          <TouchableOpacity
            onPress={handleSave}
            disabled={isUpdating || !title.trim() || !content.trim()}
            style={[
              styles.saveButton,
              (!title.trim() || !content.trim() || isUpdating) && styles.saveButtonDisabled
            ]}
          >
            {isUpdating ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.saveText}>保存</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 标题输入 */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>标题</Text>
            <TextInput
              style={styles.titleInput}
              placeholder="为你的帖子添加一个吸引人的标题..."
              value={title}
              onChangeText={setTitle}
              maxLength={100}
              multiline
            />
            <Text style={styles.charCount}>{title.length}/100</Text>
          </View>

          {/* 内容输入 */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>内容</Text>
            <TextInput
              style={styles.contentInput}
              placeholder="分享你的想法..."
              value={content}
              onChangeText={setContent}
              maxLength={1000}
              multiline
              textAlignVertical="top"
            />
            <Text style={styles.charCount}>{content.length}/1000</Text>
          </View>

          {/* 图片区域 */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>图片 (最多9张)</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imageScrollView}>
              {images.map((image, index) => (
                <View key={index} style={styles.imageContainer}>
                  <Image source={{ uri: buildImageUrl(image) }} style={styles.image} />
                  {uploadingImages[index] && (
                    <View style={styles.imageOverlay}>
                      <ActivityIndicator size="small" color="#fff" />
                    </View>
                  )}
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => removeImage(index)}
                  >
                    <Ionicons name="close" size={16} color="#fff" />
                  </TouchableOpacity>
                </View>
              ))}
              
              {images.length < 9 && (
                <TouchableOpacity style={styles.addImageButton} onPress={pickImage}>
                  <Ionicons name="add" size={24} color="#666" />
                </TouchableOpacity>
              )}
            </ScrollView>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelText: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  saveButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 60,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  titleInput: {
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    minHeight: 50,
  },
  contentInput: {
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    minHeight: 120,
  },
  charCount: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
    marginTop: 4,
  },
  imageScrollView: {
    marginTop: 8,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ff4757',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addImageButton: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

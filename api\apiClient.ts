import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';

// Define response data interface
interface ResponseData {
  success: boolean;
  data: any;
  message?: string;
  status?: number;
}

// Helper function to get stored user data
const getUserData = async () => {
  try {
    const userStoreData = await AsyncStorage.getItem('userStore');
    if (userStoreData) {
      const parsed = JSON.parse(userStoreData);
      return {
        userInfo: parsed.state?.userInfo || {},
        userToken: parsed.state?.userToken || {}
      };
    }
  } catch (error) {
    console.error('Error getting user data:', error);
  }
  return { userInfo: {}, userToken: {} };
};

// Helper function to clear user data
const clearUserData = async () => {
  try {
    await AsyncStorage.removeItem('userStore');
  } catch (error) {
    console.error('Error clearing user data:', error);
  }
};

class APIClient {
	private instance: AxiosInstance;
	private baseURL: string;

	constructor(baseURL: string = process.env.EXPO_PUBLIC_API_URL + '/api') {
		this.baseURL = baseURL;
		this.instance = axios.create({
			baseURL,
			timeout: 10000,
			headers: {
				'Content-Type': 'application/json',
			},
		});

		this.instance.interceptors.request.use(
			async (config) => {
				// console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
				if (config.data) {
					console.log('Request data:', config.data);
				}

				// 自动添加认证信息
				const { userInfo, userToken } = await getUserData();

				// 添加Authorization Bearer token（如果存在）
				if (userToken.accessToken) {
					config.headers.Authorization = `Bearer ${userToken.accessToken}`;
				}

				// 添加user-id header（后端兼容性）
				if (userInfo.id) {
					config.headers['user-id'] = userInfo.id;
				}

				// console.log('Request headers:', {
				// 	Authorization: config.headers.Authorization ? 'Bearer ***' : 'None',
				// 	'user-id': config.headers['user-id'] || 'None'
				// });

				return config;
			},
			(error) => {
				console.error('Request interceptor error:', error);
				return Promise.reject(error);
			}
		);

		this.instance.interceptors.response.use(
			(res) => {
				// console.log(`API Response: ${res.status} ${res.config.url}`);
				// console.log('Response data:', res.data);
				
				if (!res.data) throw new Error("API request failed: No data returned");
				const responseData: ResponseData = res.data;

				if (!responseData.success) {
					throw new Error(responseData.message || "API request failed");
				}
				return { ...res, data: responseData.data };
			},
			async (error: AxiosError) => {
				console.error('API Response Error:', error);
				const { response, message } = error || {};
				const status = response?.status;

				if (status === 401) {
					console.log("Authentication error - clearing user data and redirecting to login");

					// 清除用户数据
					await clearUserData();

					// 跳转到登录页面
					try {
						router.replace('/(auth)/login');
					} catch (routerError) {
						console.error('Router error:', routerError);
					}
				}

				// Extract error message from response
				let errorMessage = message;
				if (response?.data && typeof response.data === 'object') {
					const errorData = response.data as any;
					errorMessage = errorData.message || errorData.error || message;
				}

				const enhancedError = new Error(errorMessage);
				(enhancedError as any).status = status;
				return Promise.reject(enhancedError);
			},
		);
	}

	async get<T>({
		url,
		params,
		headers,
	}: {
		url: string;
		params?: Record<string, any>;
		headers?: Record<string, string>;
	}): Promise<T> {
		try {
			const response: AxiosResponse<T> = await this.instance.get(url, { params, headers });
			return response.data;
		} catch (error) {
			throw error;
		}
	}

	async post<T>({
		url,
		data,
		headers,
	}: {
		url: string;
		data?: any;
		headers?: Record<string, string>;
	}): Promise<T> {
		try {
			const response: AxiosResponse<T> = await this.instance.post(url, data, { headers });
			return response.data;
		} catch (error) {
			console.error(`POST request failed for ${url}`, error);
			throw error;
		}
	}

	async put<T>({
		url,
		data,
		headers,
	}: {
		url: string;
		data?: any;
		headers?: Record<string, string>;
	}): Promise<T> {
		try {
			const response: AxiosResponse<T> = await this.instance.put(url, data, { headers });
			return response.data;
		} catch (error) {
			console.error(`PUT request failed for ${url}`, error);
			throw error;
		}
	}

	async patch<T>({
		url,
		data,
		headers,
	}: {
		url: string;
		data?: any;
		headers?: Record<string, string>;
	}): Promise<T> {
		try {
			const response: AxiosResponse<T> = await this.instance.patch(url, data, { headers });
			return response.data;
		} catch (error) {
			console.error(`PATCH request failed for ${url}`, error);
			throw error;
		}
	}

	async delete<T>({
		url,
		headers,
		data,
	}: {
		url: string;
		headers?: Record<string, string>;
		data?: any;
	}): Promise<T> {
		try {
			const response: AxiosResponse<T> = await this.instance.delete(url, { 
				headers,
				data // 支持在 DELETE 请求中传递数据
			});
			return response.data;
		} catch (error) {
			console.error(`DELETE request failed for ${url}`, error);
			throw error;
		}
	}
}

const apiClient = new APIClient();
export default apiClient;
export { apiClient };

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useFonts } from 'expo-font';
import { Drawer } from 'expo-router/drawer';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { AuthGuard } from '@/components/auth/AuthGuard';
import CustomDrawerContent from '@/components/core/drawer/CustomDrawerContent';
const queryClient = new QueryClient();

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [fontsLoaded, fontError] = useFonts({
  });
  // 统一的 Splash Screen 隐藏逻辑
  useEffect(() => {
    async function hideSplash() {
      // 等待字体加载完成或出错，或者最多等待 3 秒
      if (fontsLoaded || fontError) {
        try {
          await SplashScreen.hideAsync();
        } catch (error) {

        }
      }
    }

    hideSplash();
  }, [fontsLoaded, fontError]);

  // 安全网：如果 3 秒后仍未隐藏，强制隐藏
  useEffect(() => {
    const timer = setTimeout(async () => {
      try {
        await SplashScreen.hideAsync();
      } catch (error) {
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  // 移除 onLayoutRootView，因为我们已经在 useEffect 中处理了
  if (!fontsLoaded && !fontError) {
    return null;
  }


  return (
    <SafeAreaProvider>
      <QueryClientProvider client={queryClient}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <AuthGuard>
          <Drawer
          drawerContent={(props) => <CustomDrawerContent {...props} />}
          screenOptions={{
            drawerStyle: {
              backgroundColor: '#fff',
              width: 240,
              shadowColor: '#000',
              shadowOffset: {
                width: 2,
                height: 0,
              },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 8,
            },
            drawerActiveTintColor: '#007AFF',
            drawerInactiveTintColor: '#666',
            drawerLabelStyle: {
              fontSize: 16,
              fontWeight: '500',
            },
            drawerType: 'slide',
          }}>
          <Drawer.Screen
            name="(stack)"
            options={{
              drawerLabel: '主页',
              title: '主页',
              headerShown: false,
              drawerItemStyle: { display: 'none' }
            }}
          />
          <Drawer.Screen
            name="mycomments"
            options={{
              drawerLabel: '我的评论',
              title: '我的评论',
              headerShown: false,
            }}
          />
          <Drawer.Screen
            name="following"
            options={{
              drawerLabel: '我的关注',
              title: '我的关注',
              headerShown: false,
            }}
          />
          <Drawer.Screen
            name="history"
            options={{
              drawerLabel: '浏览历史',
              title: '浏览历史',
              headerShown: false,
            }}
          />
          <Drawer.Screen
            name="settings"
            options={{
              drawerLabel: '设置',
              title: '设置',
              headerShown: false,
            }}
          />
          <Drawer.Screen
            name="help"
            options={{
              drawerLabel: '帮助与反馈',
              title: '帮助与反馈',
              headerShown: false,
            }}
          />
          <Drawer.Screen
            name="(auth)/login"
            options={{
              drawerItemStyle: { display: 'none' },
              headerShown: false,
            }}
          />
          <Drawer.Screen
            name="(auth)/verify-code"
            options={{
              drawerItemStyle: { display: 'none' },
              headerShown: false,
            }}
          />

          <Drawer.Screen
            name="+not-found"
            options={{
              drawerItemStyle: { display: 'none' },
              headerShown: false,
            }}
          />
          </Drawer>
          </AuthGuard>
        </GestureHandlerRootView>
      </QueryClientProvider>
    </SafeAreaProvider>
  );
}
import type { PopularProduct } from "@/api/services/productReviewService";
import reviewService from "@/api/services/productReviewService";
import type { ImageResult, ProductImagesRes, ProductRes } from "@/api/services/productService";
import productService from "@/api/services/productService";
import type { Product } from "@/types/entity";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { create } from "zustand";

type ProductStore = {
	popularProducts: PopularProduct[],
	productList: Product[];
	productImages: ImageResult[];
	actions: {
		setProductList: (
			products: Product[],
			pagination?: { totalCount: number; totalPages: number; currentPage: number },
		) => void;
		clearProductList: () => void;
		setPopularProducts: (products: PopularProduct[]) => void;
		setProductImages: (images: ImageResult[]) => void;
	};
};

const useProductStore = create<ProductStore>()((set) => ({
	popularProducts: [],
	productList: [],
	productImages: [],
	actions: {
		setProductList: (products) => {
			set({
				productList: products,
			});
		},
		clearProductList: () => {
			set({
				productList: [],
			});
		},
		setPopularProducts: (products) => {
			set({
				popularProducts: products,
			});
		},
		setProductImages: (images) => {
			set({
				productImages: images,
			});
		},
	},
}));

export const useProductList = () => useProductStore((state) => state.productList);
export const useProductAction = () => useProductStore((state) => state.actions);

export type SortByOption =
	| "quality"
	| "protein"
	| "fat"
	| "carbs"
	| "calories"
	| "quality_ingredients"
	| "questionable_ingredients";
export type SortOrderOption = "asc" | "desc";

export const useProducts = () => {
	const [currentPage, setCurrentPage] = useState(1);
	const [searchName, setSearchName] = useState("");
	const [isRankingMode, setIsRankingMode] = useState(false);
	const [sortBy, setSortBy] = useState<SortByOption>("quality");
	const [sortOrder, setSortOrder] = useState<SortOrderOption>("desc");

	const handleSearch = (name: string) => {
		setSearchName(name);
		setCurrentPage(1);
	};

	const query = useQuery<ProductRes>({
		queryKey: ["products", currentPage, searchName, isRankingMode, sortBy, sortOrder],
		queryFn: async () => {
			try {
				let response: any;
				if (isRankingMode) {
					response = await productService.getProductRanking(
						currentPage,
						sortBy,
						sortOrder,
						10,
						searchName || undefined,
					);
				} else {
					response = await productService.getProducts(currentPage, searchName || undefined);
				}

				if (typeof response !== "object" || !response) {
					throw new Error("Invalid response format");
				}

				return response;
			} catch (error) {
				console.error("Product fetch error:", error);
				throw error;
			}
		},
		placeholderData: (previousData) => previousData,
		staleTime: 10 * 100,
	});

	const data = query.data as ProductRes | undefined;

	return {
		data: data?.products || [],
		pagination: data
			? {
					totalCount: Number(data.totalCount) || 0,
					totalPages: Number(data.totalPages) || 1,
					currentPage: Number(data.currentPage) || 1,
				}
			: undefined,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		setPage: setCurrentPage,
		searchName,
		setSearchName: handleSearch,
		clearSearch: () => handleSearch(""),
		isRankingMode,
		setIsRankingMode,
		sortBy,
		setSortBy,
		sortOrder,
		setSortOrder,
	};
};

export const usePopularProducts = () => {
  const setPopularProducts = useProductStore((state) => state.actions.setPopularProducts);

  const query = useQuery<PopularProduct[]>({
    queryKey: ["popular-products"],
    queryFn: async () => {
      try {
        const response = await reviewService.getPopularProducts();
        if (!response) {
          return [];
        }
        const productsData = Array.isArray(response) ? response : [];
    
        setTimeout(() => {
          setPopularProducts(productsData);
        }, 0);

        return productsData;
      } catch (error) {
        console.error("Popular products fetch error:", error);
        throw error;
      }
    },
    staleTime: 60 * 1000, 
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  return {
    data: query.data || [],
    isLoading: query.isLoading,
    error: query.error,
  };
};

export const useProductDetail = (productId?: string) => {
  const { data: apiResponse, isLoading, error } = useQuery<Product>({
    queryKey: ['product', productId],
    queryFn: () => productService.getProductById(productId || ''),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, 
  });

  return {
    product: apiResponse,
    isLoading,
    error,
    apiResponse,
  };
};

export const useProductImages = (productIds: string[], enabled: boolean = true) => {
	const [currentPage, setCurrentPage] = useState(1);
	
	const query = useQuery<ProductImagesRes>({
		queryKey: ["product-images", productIds, currentPage],
		queryFn: async () => {
			if (!productIds || productIds.length === 0) {
				return { success: true, images: [], totalCount: 0, totalPages: 0, currentPage: 1 };
			}
			
			try {
				const response = await productService.getProductImages(productIds, currentPage, 10);
				return response;
			} catch (error) {
				console.error("Product images fetch error:", error);
				throw error;
			}
		},
		enabled: enabled && productIds && productIds.length > 0,
		staleTime: 5 * 60 * 1000, 
	});

	const data = query.data as ProductImagesRes | undefined;

	return {
		images: data?.images || [],
		pagination: data
			? {
					totalCount: Number(data.totalCount) || 0,
					totalPages: Number(data.totalPages) || 1,
					currentPage: Number(data.currentPage) || 1,
				}
			: undefined,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		setPage: setCurrentPage,
	};
};

export const useBrandImages = (brandName?: string) => {
	const query = useQuery<{
		brand: string;
		images: string[];
		count: number;
	}>({
		queryKey: ["brand-images", brandName],
		queryFn: () => productService.getBrandImages(brandName || ''),
		enabled: !!brandName,
		staleTime: 5 * 60 * 1000,
	});

	return {
		images: query.data?.images || [],
		count: query.data?.count || 0,
		isLoading: query.isLoading,
		error: query.error,
	};
};

// 获取评分排行榜的hook - 基于用户评分
export const useRatingRanking = () => {
  const query = useQuery<PopularProduct[]>({
    queryKey: ["rating-ranking"],
    queryFn: async () => {
      try {
        // 尝试使用热门产品API
        try {
          const response = await reviewService.getPopularProducts(15);

          if (response && Array.isArray(response)) {
            return response;
          } else if (response?.data && Array.isArray(response.data)) {
            return response.data;
          }
        } catch (reviewError) {
        }

        // 如果热门产品API失败，使用产品排行API作为备选
        try {
          const rankingResponse = await productService.getProductRanking(1, "quality", "desc", 15);
          if (rankingResponse?.products) {
            // 转换Product类型到PopularProduct类型
            const convertedProducts: PopularProduct[] = rankingResponse.products.map(product => ({
              product_id: product._id,
              name: product.name,
              brand: product.brand,
              image_url: product.image_url,
              average_rating: "暂无评分", // 产品排行API没有评分数据
              review_count: 0
            }));
            return convertedProducts;
          }
        } catch (rankingError) {
          console.log("Rating ranking - 产品排行API也失败:", rankingError instanceof Error ? rankingError.message : rankingError);
        }

        return [];
      } catch (error) {
        console.error("Rating ranking fetch error:", error);
        throw error;
      }
    },
    staleTime: 10 * 60 * 1000, // 10分钟缓存
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  return {
    data: query.data || [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  };
};

export default useProductStore;

import DateTimePicker from '@react-native-community/datetimepicker';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { ProfileFormField } from '@/components/profile/ProfileFormField';
import { ProfileImagePicker } from '@/components/profile/ProfileImagePicker';
import OptionPicker from '@/components/core/pickers/OptionPicker';
import { IMAGE_TYPES, LOCATION_OPTIONS, OCCUPATION_OPTIONS } from '@/constants/profile';
import { useImagePicker } from '@/hooks/useImagePicker';
import { useUpdateUserProfile, useUserActions, useUserInfo } from '@/store/userStore';
import type { UserInfo } from '@/types/entity';
import { ProfileData } from '@/types/profile-types';
export default function EditProfileScreen() {
  const router = useRouter();
  const userInfo = useUserInfo();
  const { setUserInfo } = useUserActions();
  const updateProfileMutation = useUpdateUserProfile();

  const [profileData, setProfileData] = useState<ProfileData>({
    username: '',
    id: '',
    backgroundImage: '',
    avatar: '',
    bio: '',
    gender: '',
    birthday: '',
    location: '',
    occupation: '',
  });

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [showOccupationPicker, setShowOccupationPicker] = useState(false);

  // 图片选择Hook
  const {showImagePicker } = useImagePicker({
    userId: userInfo.id || '',
    onImageUpdate: (type, imageUrl) => {
      const field = type === IMAGE_TYPES.AVATAR ? 'avatar' : 'backgroundImage';
      setProfileData(prev => ({ ...prev, [field]: imageUrl }));
    },
    onLoadingChange: (type, loading) => {
      const field = type === IMAGE_TYPES.AVATAR ? 'isUploadingAvatar' : 'isUploadingBackground';
      setProfileData(prev => ({ ...prev, [field]: loading }));
    },
  });



  // 组件数据跟随userinfostore状态库数据更新而渲染
  useEffect(() => {
    if (userInfo) {
      setProfileData({
        username: userInfo.username || '',
        id: userInfo.id || '',
        backgroundImage: userInfo.backgroundImage || '',
        avatar: userInfo.avatar || '',
        bio: userInfo.bio || '',
        gender: userInfo.gender || '',
        birthday: userInfo.birthday || '',
        location: userInfo.location || '',
        occupation: userInfo.occupation || '',
      });
    }
  }, [userInfo]);
  
  const handleSave = async () => {
    if (!userInfo.id) {
      Alert.alert('错误', '用户ID不存在，请重新登录');
      return;
    }

    try {
      if (profileData.bio) {
        const lines = profileData.bio.split('\n');
        if (lines.length > 5) {
          Alert.alert('错误', '简介最多只能有5行');
          return;
        }
      }

      // 验证生日格式
      if (profileData.birthday && !/^\d{4}-\d{2}-\d{2}$/.test(profileData.birthday)) {
        Alert.alert('错误', '生日格式应为 YYYY-MM-DD');
        return;
      }

      const updateData: Partial<UserInfo> = {};

      if (profileData.username !== userInfo.username) {
        updateData.username = profileData.username;
      }
      if (profileData.backgroundImage !== userInfo.backgroundImage) {
        updateData.backgroundImage = profileData.backgroundImage;
      }
      if (profileData.avatar !== userInfo.avatar) {
        updateData.avatar = profileData.avatar;
      }
      if (profileData.bio !== userInfo.bio) {
        updateData.bio = profileData.bio;
      }
      if (profileData.location !== userInfo.location) {
        updateData.location = profileData.location;
      }

      // Add extended fields
      if (profileData.gender) {
        (updateData as any).gender = profileData.gender;
      }
      if (profileData.birthday) {
        (updateData as any).birthday = profileData.birthday;
      }
      if (profileData.occupation) {
        (updateData as any).occupation = profileData.occupation;
      }

      const updatedUser = await updateProfileMutation.mutateAsync({
        id: userInfo.id,
        data: updateData
      });

      const newUserInfo = {
        ...userInfo,
        ...updatedUser,
      };
      setUserInfo(newUserInfo as any);

      Alert.alert('成功', '个人资料已保存', [
        { text: '确定', onPress: () => router.back() }
      ]);
    } catch (error: any) {
      const errorMessage = error?.message || '保存失败，请重试';
      Alert.alert('错误', errorMessage);
    }
  };

  const handleInputChange = (field: keyof ProfileData, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  const handleGenderChange = (gender: 'male' | 'female' | 'other') => {
    setProfileData(prev => ({ ...prev, gender }));
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      setProfileData(prev => ({ ...prev, birthday: formattedDate }));
    }
  };

  const showLocationOptions = () => {
    setShowLocationPicker(true);
  };

  const showOccupationOptions = () => {
    setShowOccupationPicker(true);
  };

  const handleLocationSelect = (location: string) => {
    setProfileData(prev => ({ ...prev, location }));
  };

  const handleOccupationSelect = (occupation: string) => {
    setProfileData(prev => ({ ...prev, occupation }));
  };

  const getDateFromString = (dateString: string) => {
    if (!dateString) return new Date();
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? new Date() : date;
  };

  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.scrollContentContainer}
      >
        <View style={styles.section}>
          {/* 背景图片选择器 */}
          <ProfileImagePicker
            type={IMAGE_TYPES.BACKGROUND}
            imageUri={profileData.backgroundImage}
            isUploading={profileData.isUploadingBackground}
            onPress={() => showImagePicker(IMAGE_TYPES.BACKGROUND)}
          />

          {/* 头像选择器 */}
          <ProfileImagePicker
            type={IMAGE_TYPES.AVATAR}
            imageUri={profileData.avatar}
            isUploading={profileData.isUploadingAvatar}
            onPress={() => showImagePicker(IMAGE_TYPES.AVATAR)}
          />

          {/* 基本信息表单字段 */}
          <ProfileFormField
            label="名字"
            value={profileData.username}
            onChangeText={(text) => handleInputChange('username', text)}
            placeholder="输入你的名字"
          />

          <ProfileFormField
            label="ID号"
            value={profileData.id}
            editable={false}
          />

          <ProfileFormField
            label="简介"
            value={profileData.bio}
            onChangeText={(text) => {
              const lines = text.split('\n');
              if (lines.length <= 5) {
                handleInputChange('bio', text);
              } else {
                Alert.alert('提示', '简介最多只能有5行');
              }
            }}
            placeholder="介绍一下自己（最多5行）"
            multiline={true}
            maxLength={500}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>个人信息</Text>
          <View style={styles.field}>

            <Text style={styles.label}>性别</Text>
            <View style={styles.genderSelector}>
              <TouchableOpacity
                style={[styles.genderOption, profileData.gender === 'male' && styles.genderOptionSelected]}
                onPress={() => handleGenderChange('male')}
              >
                <Text style={[styles.genderText, profileData.gender === 'male' && styles.genderTextSelected]}>男</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.genderOption, profileData.gender === 'female' && styles.genderOptionSelected]}
                onPress={() => handleGenderChange('female')}
              >
                <Text style={[styles.genderText, profileData.gender === 'female' && styles.genderTextSelected]}>女</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Enhanced Birthday Picker */}
          <View style={styles.field}>
            <Text style={styles.label}>生日</Text>
            <TouchableOpacity
              style={styles.pickerButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={[styles.pickerText, !profileData.birthday && styles.placeholderText]}>
                {profileData.birthday ? formatDisplayDate(profileData.birthday) : '选择生日'}
              </Text>
              <Icon name="calendar" size={16} color="#666" />
            </TouchableOpacity>
          </View>

          {showDatePicker && (
            <DateTimePicker
              value={getDateFromString(profileData.birthday)}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={handleDateChange}
              maximumDate={new Date()}
              minimumDate={new Date(1900, 0, 1)}
              locale="zh-CN"
            />
          )}

          {/* Enhanced Location Picker */}
          <View style={styles.field}>
            <Text style={styles.label}>地区</Text>
            <TouchableOpacity
              style={styles.pickerButton}
              onPress={showLocationOptions}
            >
              <Text style={[styles.pickerText, !profileData.location && styles.placeholderText]}>
                {profileData.location || '选择地区'}
              </Text>
              <Icon name="map-marker" size={16} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Enhanced Occupation Picker */}
          <View style={styles.field}>
            <Text style={styles.label}>职业</Text>
            <TouchableOpacity
              style={styles.pickerButton}
              onPress={showOccupationOptions}
            >
              <Text style={[styles.pickerText, !profileData.occupation && styles.placeholderText]}>
                {profileData.occupation || '选择职业'}
              </Text>
              <Icon name="briefcase" size={16} color="#666" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <TouchableOpacity
            style={[
              styles.saveButtonBottom,
              updateProfileMutation.isPending && styles.saveButtonDisabled
            ]}
            onPress={handleSave}
            disabled={updateProfileMutation.isPending}
          >
            <Text style={styles.saveButtonBottomText}>
              {updateProfileMutation.isPending ? '保存中...' : '保存'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Extra spacing to prevent keyboard covering */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Location Picker Modal */}
      <OptionPicker
        visible={showLocationPicker}
        onClose={() => setShowLocationPicker(false)}
        onSelect={handleLocationSelect}
        selectedOption={profileData.location}
        options={LOCATION_OPTIONS}
        title="选择地区"
        searchPlaceholder="搜索地区..."
      />

      {/* Occupation Picker Modal */}
      <OptionPicker
        visible={showOccupationPicker}
        onClose={() => setShowOccupationPicker(false)}
        onSelect={handleOccupationSelect}
        selectedOption={profileData.occupation}
        options={OCCUPATION_OPTIONS}
        title="选择职业"
        searchPlaceholder="搜索职业..."
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f6fa',
  },
  scrollView: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: Platform.OS === 'ios' ? 100 : 120, // Extra padding for keyboard
  },
  section: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 16,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2c3e50',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f2f6',
    marginBottom: 8,
  },
  field: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  label: {
    width: 80,
    fontSize: 15,
    fontWeight: '600',
    color: '#34495e',
  },
  input: {
    flex: 1,
    fontSize: 15,
    color: '#2c3e50',
    textAlign: 'right',
    paddingLeft: 12,
  },
  disabledInput: {
    color: '#95a5a6',
  },
  multilineInput: {
    minHeight: 90,
    textAlignVertical: 'top',
    textAlign: 'left',
    paddingTop: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  imagePicker: {
    flex: 1,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 20,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#e9ecef',
    borderStyle: 'dashed',
  },
  backgroundImage: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 10,
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  imageOverlayText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '600',
    marginTop: 6,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  avatarPicker: {
    width: 90,
    height: 90,
    borderRadius: 45,
    overflow: 'hidden',
    position: 'relative',
    borderWidth: 3,
    borderColor: '#3498db',
    shadowColor: '#3498db',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  avatarOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(52, 152, 219, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarOverlayText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
  },
  genderSelector: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 10,
  },
  genderOption: {
    paddingHorizontal: 18,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#e9ecef',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  genderOptionSelected: {
    backgroundColor: '#3498db',
    borderColor: '#3498db',
    shadowColor: '#3498db',
    shadowOpacity: 0.3,
  },
  genderText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#34495e',
  },
  genderTextSelected: {
    color: '#fff',
  },
  pickerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 3,
    elevation: 1,
  },
  pickerText: {
    fontSize: 15,
    color: '#2c3e50',
    flex: 1,
    textAlign: 'right',
    marginRight: 10,
    fontWeight: '500',
  },
  placeholderText: {
    color: '#95a5a6',
    fontStyle: 'italic',
  },
  saveButtonBottom: {
    backgroundColor: '#3498db',
    paddingVertical: 18,
    borderRadius: 12,
    alignItems: 'center',
    marginVertical: 20,
    marginHorizontal: 6,
    shadowColor: '#3498db',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  saveButtonBottomText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  saveButtonDisabled: {
    backgroundColor: '#bdc3c7',
    shadowOpacity: 0.1,
  },
  bottomSpacing: {
    height: Platform.OS === 'ios' ? 200 : 250, // Ensure content is accessible above keyboard
  },
});

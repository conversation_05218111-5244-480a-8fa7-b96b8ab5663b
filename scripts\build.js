#!/usr/bin/env node

/**
 * 自动化打包脚本
 * 用于构建不同平台和环境的应用版本
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n🔄 ${description}`, 'blue');
  log(`执行命令: ${command}`, 'yellow');
  
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    log(`✅ ${description} 完成`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} 失败`, 'red');
    log(`错误: ${error.message}`, 'red');
    return false;
  }
}

function showHelp() {
  log('\n📱 PetFood App 打包工具', 'bold');
  log('='.repeat(40), 'cyan');
  
  log('\n使用方法:', 'yellow');
  log('  node scripts/build.js [选项]', 'cyan');
  
  log('\n选项:', 'yellow');
  log('  --check          运行预检查', 'cyan');
  log('  --dev-android    构建 Android 开发版', 'cyan');
  log('  --dev-ios        构建 iOS 开发版', 'cyan');
  log('  --prod-android   构建 Android 生产版 APK', 'cyan');
  log('  --prod-android-aab 构建 Android 生产版 AAB', 'cyan');
  log('  --prod-ios       构建 iOS 生产版', 'cyan');
  log('  --web            构建 Web 版本', 'cyan');
  log('  --all-dev        构建所有平台开发版', 'cyan');
  log('  --all-prod       构建所有平台生产版', 'cyan');
  log('  --help           显示帮助信息', 'cyan');
  
  log('\n示例:', 'yellow');
  log('  node scripts/build.js --check', 'cyan');
  log('  node scripts/build.js --dev-android', 'cyan');
  log('  node scripts/build.js --prod-android', 'cyan');
  
  log('\n注意事项:', 'yellow');
  log('  • 构建前会自动运行预检查', 'cyan');
  log('  • iOS 构建需要 macOS 环境', 'cyan');
  log('  • 生产版本需要配置签名证书', 'cyan');
  log('  • 建议先构建开发版本进行测试', 'cyan');
}

function preCheck() {
  log('\n🔍 运行预检查...', 'magenta');
  
  // 检查必要文件
  const requiredFiles = [
    'package.json',
    'app.json',
    'app/_layout.tsx'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      log(`❌ 缺少必要文件: ${file}`, 'red');
      return false;
    }
  }
  
  // 运行检查脚本
  if (!runCommand('node scripts/check-errors.js', '运行完整检查')) {
    return false;
  }
  
  log('✅ 预检查通过', 'green');
  return true;
}

function updateVersion() {
  log('\n📝 检查版本信息...', 'blue');
  
  try {
    const appConfig = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    log(`当前版本: ${appConfig.expo.version}`, 'cyan');
    log(`包版本: ${packageJson.version}`, 'cyan');
    
    // 可以在这里添加自动版本号更新逻辑
    return true;
  } catch (error) {
    log(`❌ 读取版本信息失败: ${error.message}`, 'red');
    return false;
  }
}

function buildDevAndroid() {
  log('\n🤖 构建 Android 开发版...', 'magenta');
  return runCommand('npx eas build --platform android --profile development', 'Android 开发版构建');
}

function buildDevIOS() {
  log('\n🍎 构建 iOS 开发版...', 'magenta');
  return runCommand('npx eas build --platform ios --profile development', 'iOS 开发版构建');
}

function buildProdAndroid() {
  log('\n🤖 构建 Android 生产版 APK...', 'magenta');
  return runCommand('npx eas build --platform android --profile production', 'Android 生产版 APK 构建');
}

function buildProdAndroidAAB() {
  log('\n🤖 构建 Android 生产版 AAB...', 'magenta');
  return runCommand('npx eas build --platform android --profile production-aab', 'Android 生产版 AAB 构建');
}

function buildProdIOS() {
  log('\n🍎 构建 iOS 生产版...', 'magenta');
  return runCommand('npx eas build --platform ios --profile production', 'iOS 生产版构建');
}

function buildWeb() {
  log('\n🌐 构建 Web 版本...', 'magenta');
  return runCommand('npx expo export:web', 'Web 版本构建');
}

function checkEASCLI() {
  log('\n🔧 检查 EAS CLI...', 'blue');
  
  try {
    execSync('eas --version', { stdio: 'pipe' });
    log('✅ EAS CLI 已安装', 'green');
    return true;
  } catch (error) {
    log('❌ EAS CLI 未安装', 'red');
    log('请运行: npm install -g @expo/eas-cli', 'yellow');
    return false;
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help')) {
    showHelp();
    return;
  }
  
  log('🚀 PetFood App 构建工具启动', 'bold');
  log('='.repeat(50), 'cyan');
  
  // 检查 EAS CLI
  if (!checkEASCLI()) {
    process.exit(1);
  }
  
  // 预检查
  if (args.includes('--check')) {
    if (preCheck()) {
      log('\n🎉 所有检查通过，可以进行构建！', 'green');
    } else {
      log('\n❌ 检查失败，请修复错误后重试', 'red');
      process.exit(1);
    }
    return;
  }
  
  // 构建前预检查
  if (!preCheck()) {
    log('\n❌ 预检查失败，构建终止', 'red');
    process.exit(1);
  }
  
  // 检查版本信息
  if (!updateVersion()) {
    log('\n❌ 版本检查失败，构建终止', 'red');
    process.exit(1);
  }
  
  let success = true;
  
  // 执行构建
  if (args.includes('--dev-android')) {
    success = buildDevAndroid() && success;
  }
  
  if (args.includes('--dev-ios')) {
    success = buildDevIOS() && success;
  }
  
  if (args.includes('--prod-android')) {
    success = buildProdAndroid() && success;
  }

  if (args.includes('--prod-android-aab')) {
    success = buildProdAndroidAAB() && success;
  }

  if (args.includes('--prod-ios')) {
    success = buildProdIOS() && success;
  }
  
  if (args.includes('--web')) {
    success = buildWeb() && success;
  }
  
  if (args.includes('--all-dev')) {
    success = buildDevAndroid() && buildDevIOS() && buildWeb() && success;
  }
  
  if (args.includes('--all-prod')) {
    success = buildProdAndroid() && buildProdIOS() && buildWeb() && success;
  }
  
  // 总结
  log('\n' + '='.repeat(50), 'cyan');
  if (success) {
    log('🎉 构建完成！', 'green');
    log('\n📱 下一步:', 'yellow');
    log('  • 测试构建的应用', 'cyan');
    log('  • 检查所有功能是否正常', 'cyan');
    log('  • 准备发布到应用商店', 'cyan');
  } else {
    log('❌ 构建过程中出现错误', 'red');
    log('\n🔧 建议:', 'yellow');
    log('  • 检查错误日志', 'cyan');
    log('  • 修复相关问题', 'cyan');
    log('  • 重新运行构建', 'cyan');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch(error => {
  log(`\n💥 脚本执行失败: ${error.message}`, 'red');
  process.exit(1);
});

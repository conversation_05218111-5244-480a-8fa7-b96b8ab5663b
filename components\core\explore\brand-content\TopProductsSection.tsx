import { useTopBrandProducts } from '@/store/brandStore';
import { getProductImageUrlSync } from '@/utils/imageUtils';
import { useRouter } from 'expo-router';
import React, { useMemo } from 'react';
import { ActivityIndicator, Image, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
interface TopProductsSectionProps {
  brandId: string;
}

const TopProductsSection: React.FC<TopProductsSectionProps> = ({ brandId }) => {
  const { products, isLoading, error } = useTopBrandProducts(brandId);
  const router = useRouter();

  const productsDisplay = useMemo(() => {
    if (!products) return [];
    return products.map((product) => {
      return {
        ...product,
        image_url: getProductImageUrlSync(product.product_id)
      };
    });
  }, [products]);


  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color="#3498db" />
        <Text style={styles.loadingText}>加载推荐产品...</Text>
      </View>
    );
  }

  if (error || products.length === 0) {
    return null; 
  }

  const handleProductPress = (productId: string) => {
    router.push(`/(stack)/product-detail?id=${productId}`);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>代表产品</Text>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {productsDisplay.map((product) => (
          <Pressable 
            key={product.product_id}
            style={({ pressed }) => [
              styles.productCard,
              pressed && styles.productCardPressed
            ]}
            onPress={() => handleProductPress(product.product_id)}
          >
            <Image 
              source={{ uri: product.image_url }} 
              style={styles.productImage}
              resizeMode="contain"
            />
            <Text style={styles.productName} numberOfLines={2}>{product.name}</Text>
            <View style={styles.ratingContainer}>
              <Text style={styles.ratingText}>{product.average_rating.toFixed(1)}</Text>
              <Icon name="star" size={12} color="#ff9900" />
              <Text style={styles.reviewCount}>({product.review_count})</Text>
            </View>
          </Pressable>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    marginTop: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 12,
  },
  scrollContent: {
    paddingBottom: 8,
  },
  productCard: {
    width: 120,
    marginRight: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
  },
  productCardPressed: {
    opacity: 0.8,
    transform: [{ scale: 0.98 }],
  },
  productImage: {
    width: 80,
    height: 80,
    marginBottom: 8,
  },
  productName: {
    fontSize: 12,
    textAlign: 'center',
    color: '#333',
    height: 36,
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#ff9900',
    marginRight: 2,
  },
  reviewCount: {
    fontSize: 10,
    color: '#666',
    marginLeft: 2,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
});

export default TopProductsSection;

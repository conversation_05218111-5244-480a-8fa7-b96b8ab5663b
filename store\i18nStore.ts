import type { I18nEntry, I18nItemsResponse } from "@/api/services/i18nService";
import i18nService from "@/api/services/i18nService";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { create } from "zustand";
// 存储i18n数据的状态管理
type I18nStore = {
	translationCache: Record<string, Record<string, any>>;
	lastUpdated: Record<string, number>;
	entryItems: I18nEntry[];
	actions: {
		setEntryItems: (
			entryItems: I18nEntry[],
			pagination?: { totalCount: number; totalPages: number; currentPage: number },
		) => void;
		clearEntryItems: () => void;
		setTranslations: (locale: string, translations: Record<string, any>) => void;
		clearTranslations: () => void;
		getTranslations: (locale: string) => Record<string, any> | null;
	};
};

const useI18nStore = create<I18nStore>()((set, get) => ({
	translationCache: {},
	lastUpdated: {},
	entryItems: [],
	actions: {
		setEntryItems: (entryItems) => {
			set({
				entryItems: entryItems,
			});
		},
		clearEntryItems: () => {
			set({
				entryItems: [],
			});
		},
		setTranslations: (locale, translations) => {
			set((state) => ({
				translationCache: {
					...state.translationCache,
					[locale]: translations,
				},
				lastUpdated: {
					...state.lastUpdated,
					[locale]: Date.now(),
				},
			}));
		},
		clearTranslations: () => {
			set({
				translationCache: {},
				lastUpdated: {},
			});
		},
		getTranslations: (locale) => {
			const { translationCache } = get();
			return translationCache[locale] || null;
		},
	},
}));

// 创建i18n管理hook
export function useI18nAdmin() {
	const [currentPage, setCurrentPage] = useState(1);
	const [searchValue, setSearchValue] = useState("");

	const clearSearch = () => {
		setSearchValue("");
	};
	const query = useQuery<I18nItemsResponse>({
		queryKey: ["i18n", "entries", currentPage, searchValue],
		queryFn: async () => {
			try {
				const response = await i18nService.getI18nEntries(searchValue, currentPage);
				if (typeof response !== "object" || !response) {
					throw new Error("Invalid response format");
				}

				return response;
			} catch (error) {
				console.error("Brand fetch error:", error);
				throw error;
			}
		},
		placeholderData: (previousData) => previousData,
		staleTime: 10 * 100,
	});

	const queryClient = useQueryClient();

	// 获取i18n树结构
	const useI18nTree = () => {
		return useQuery({
			queryKey: ["i18n", "tree"],
			queryFn: () => i18nService.getI18nTree(),
		});
	};

	// 获取特定语言的i18n条目
	const useI18nByLocale = (locale: string, page = 1, limit = 10) => {
		return useQuery({
			queryKey: ["i18n", "locale", locale, page, limit],
			queryFn: () => i18nService.getI18nByLocale(locale, page, limit),
		});
	};

	// 获取特定命名空间的i18n条目
	const useI18nByNamespace = (namespace: string, page = 1, limit = 10) => {
		return useQuery({
			queryKey: ["i18n", "namespace", namespace, page, limit],
			queryFn: () => i18nService.getI18nByNamespace(namespace, page, limit),
		});
	};

	// 获取特定语言和命名空间的i18n条目
	const useI18nByLocaleAndNamespace = (locale: string, namespace: string, page = 1, limit = 10) => {
		return useQuery({
			queryKey: ["i18n", "locale", locale, "namespace", namespace, page, limit],
			queryFn: () => i18nService.getI18nByLocaleAndNamespace(locale, namespace, page, limit),
		});
	};

	// 获取特定ID的i18n条目
	const useI18nById = (id: string) => {
		return useQuery({
			queryKey: ["i18n", "id", id],
			queryFn: () => i18nService.getI18nById(id),
			enabled: !!id,
		});
	};

	// 创建i18n条目
	const createI18nMutation = useMutation({
		mutationFn: (data: Partial<I18nEntry>) => i18nService.createI18nEntry(data),
		onSuccess: () => {
			// 创建成功后刷新查询
			queryClient.invalidateQueries({ queryKey: ["i18n"] });
		},
	});

	// 更新i18n条目
	const updateI18nMutation = useMutation({
		mutationFn: ({ id, data }: { id: string; data: Partial<I18nEntry> }) => i18nService.updateI18nEntry(id, data),
		onSuccess: () => {
			// 更新成功后刷新查询
			queryClient.invalidateQueries({ queryKey: ["i18n"] });
		},
	});

	// 删除i18n条目
	const deleteI18nMutation = useMutation({
		mutationFn: (id: string) => i18nService.deleteI18nEntry(id),
		onSuccess: () => {
			// 删除成功后刷新查询
			queryClient.invalidateQueries({ queryKey: ["i18n"] });
		},
	});

	return {
		data: query.data?.items,
		pagination: query.data
			? {
					totalCount: Number(query.data.totalCount) || 0,
					totalPages: Number(query.data.totalPages) || 1,
					currentPage: Number(query.data.currentPage) || 1,
				}
			: undefined,
		setPage: setCurrentPage,
		useI18nTree,
		clearSearch,
		useI18nByLocale,
		useI18nByNamespace,
		useI18nByLocaleAndNamespace,
		useI18nById,
		setSearchValue,
		createI18n: createI18nMutation.mutate,
		updateI18n: updateI18nMutation.mutate,
		deleteI18n: deleteI18nMutation.mutate,
		isCreating: createI18nMutation.isPending,
		isUpdating: updateI18nMutation.isPending,
		isDeleting: deleteI18nMutation.isPending,
		isLoading: query.isLoading || query.isFetching,
	};
}

export default useI18nStore;
